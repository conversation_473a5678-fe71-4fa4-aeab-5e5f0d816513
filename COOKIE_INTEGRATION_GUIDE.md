# 🍪 Cookie集成指南 - 使用真实登录数据

## 🎯 概述

基于您提供的登录cookie，我们的插件现在支持使用浏览器cookie来访问Augment的真实使用数据，这是一种更直接的认证方式。

## 🔍 Cookie信息分析

从您提供的cookie中，我们发现了以下关键信息：

### 用户信息
- **用户ID**: `f9e3adfd-26c0-4118-8a9b-843c89035c37`
- **邮箱**: `<EMAIL>`
- **认证状态**: 已验证

### Session信息
- **Session Token**: 包含OAuth2状态和用户信息
- **过期时间**: 2025-01-17 (需要定期更新)
- **认证提供商**: Auth0

## 🚀 使用Cookie集成的步骤

### 步骤1：获取最新Cookie
1. 在浏览器中访问 `https://app.augmentcode.com`
2. 登录您的账户
3. 按 `F12` 打开开发者工具
4. 切换到 `Application` 标签页
5. 在左侧选择 `Cookies` → `https://app.augmentcode.com`
6. 复制所有cookie值，格式如：
   ```
   cookie1=value1; cookie2=value2; cookie3=value3
   ```

### 步骤2：在插件中配置Cookie
1. 在VSCode中按 `F5` 启动Extension Development Host
2. 执行命令：
   ```
   Ctrl+Shift+P → "Augment Tracker: Setup Browser Cookies"
   ```
3. 粘贴您复制的cookie字符串
4. 插件会自动验证并保存

### 步骤3：测试Cookie认证
1. 执行命令：
   ```
   Ctrl+Shift+P → "Augment Tracker: Test API Connection"
   ```
2. 或者使用调试命令：
   ```
   Ctrl+Shift+P → "Augment Tracker: Debug API Calls"
   ```

## 🔧 Cookie vs Token 对比

| 认证方式 | 优点 | 缺点 | 适用场景 |
|---------|------|------|----------|
| **Cookie** | ✅ 直接使用浏览器登录状态<br>✅ 无需额外API token<br>✅ 与web应用同步 | ❌ 有过期时间<br>❌ 需要定期更新<br>❌ 依赖浏览器session | 个人使用，临时测试 |
| **API Token** | ✅ 长期有效<br>✅ 专门为API设计<br>✅ 更安全 | ❌ 需要额外配置<br>❌ 可能需要特殊权限 | 生产环境，长期使用 |

## 📊 Cookie认证的API调用

### 支持的端点
使用cookie认证可以访问以下端点：

1. **健康检查**: `GET /health`
   - 无需认证
   - 验证API服务状态

2. **用户信息**: `GET /user`
   - 需要cookie认证
   - 返回用户账户信息

3. **使用统计**: `GET /usage`
   - 需要cookie认证
   - 返回真实使用数据

4. **订阅信息**: `GET /subscription`
   - 需要cookie认证
   - 返回订阅和限额信息

### 请求头格式
```http
GET /usage HTTP/1.1
Host: i1.api.augmentcode.com
Cookie: intercom-id-oiuh4kg0=...; _session=...; ph_phc_...
Referer: https://app.augmentcode.com/
Origin: https://app.augmentcode.com
Content-Type: application/json
```

## 🔍 验证Cookie集成成功

### 方法1：查看控制台日志
```javascript
🌐 Augment API Request: {
  url: "https://i1.api.augmentcode.com/usage",
  method: "GET",
  headers: { 
    "Cookie": "[HIDDEN]",
    "Referer": "https://app.augmentcode.com/",
    "Origin": "https://app.augmentcode.com"
  }
}

✅ API request successful: {
  dataKeys: ["totalUsage", "usageLimit", "dailyUsage"],
  dataSize: 156
}
```

### 方法2：状态栏指示
- 状态栏显示 **● (实心圆)** 表示使用真实数据
- 工具提示显示 "Real data from Augment API"

### 方法3：使用调试命令
```
Ctrl+Shift+P → "Debug API Calls" → 选择 "📊 Usage Data"
```

## 🔄 Cookie更新流程

### 自动检测过期
插件会自动检测cookie是否过期：
- API返回401状态码时
- 自动提示用户更新cookie

### 手动更新
当cookie过期时：
1. 重新登录 `https://app.augmentcode.com`
2. 获取新的cookie
3. 执行 `Setup Browser Cookies` 命令
4. 粘贴新的cookie

## 🛡️ 安全注意事项

### Cookie安全
- ✅ 本地存储，不传输到第三方
- ✅ 密码输入框隐藏显示
- ✅ 用户完全控制数据
- ⚠️ 定期更新避免过期

### 最佳实践
1. **定期更新**: 建议每天更新一次cookie
2. **安全存储**: 不要在公共场所输入cookie
3. **及时清理**: 不使用时清除cookie
4. **监控状态**: 注意API调用失败提示

## 🧪 测试您的Cookie

使用您提供的cookie信息，可以执行以下测试：

```bash
# 运行独立测试脚本
node test-with-cookies.js
```

或者在插件中：
```
1. F5 启动插件
2. Ctrl+Shift+P → "Setup Browser Cookies"
3. 粘贴您的cookie
4. Ctrl+Shift+P → "Debug API Calls" → 测试各个端点
```

## 🎯 预期结果

成功集成后，您应该看到：
- ✅ 状态栏显示真实使用数据
- ✅ API调用返回200状态码
- ✅ 控制台显示详细的使用统计
- ✅ 工具提示显示API连接状态

这种cookie集成方式让您可以直接使用浏览器的登录状态来获取真实的Augment使用数据！

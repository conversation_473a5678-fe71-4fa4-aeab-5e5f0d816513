{"version": 3, "file": "i18n.js", "sourceRoot": "", "sources": ["../src/i18n.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,UAAU;AACV,MAAM,QAAQ,GAAG;IACb,IAAI,EAAE;QACF,kBAAkB;QAClB,eAAe,EAAE,iDAAiD;QAClE,qBAAqB,EAAE,6BAA6B;QACpD,mBAAmB,EAAE,4BAA4B;QACjD,kBAAkB,EAAE,uBAAuB;QAC3C,0BAA0B,EAAE,0CAA0C;QACtE,qBAAqB,EAAE,mCAAmC;QAC1D,mBAAmB,EAAE,wBAAwB;QAC7C,iBAAiB,EAAE,WAAW;QAC9B,uBAAuB,EAAE,iBAAiB;QAC1C,mBAAmB,EAAE,YAAY;QACjC,sBAAsB,EAAE,gBAAgB;QACxC,uBAAuB,EAAE,iBAAiB;QAC1C,gBAAgB,EAAE,SAAS;QAC3B,eAAe,EAAE,QAAQ;QACzB,cAAc,EAAE,OAAO;QACvB,mBAAmB,EAAE,2DAA2D;QAChF,4BAA4B,EAAE,uCAAuC;QAErE,kBAAkB;QAClB,sBAAsB,EAAE,oFAAoF;QAC5G,qBAAqB,EAAE,eAAe;QACtC,eAAe,EAAE,QAAQ;QACzB,sBAAsB,EAAE,iBAAiB;QAEzC,gBAAgB;QAChB,aAAa,EAAE,2BAA2B;QAC1C,oBAAoB,EAAE,eAAe;QACrC,oBAAoB,EAAE,eAAe;QACrC,uBAAuB,EAAE,kBAAkB;QAC3C,iBAAiB,EAAE,WAAW;QAC9B,iBAAiB,EAAE,YAAY;QAC/B,kBAAkB,EAAE,aAAa;QACjC,oBAAoB,EAAE,eAAe;QAErC,UAAU;QACV,6BAA6B,EAAE,uBAAuB;QACtD,iBAAiB,EAAE,SAAS;QAC5B,eAAe,EAAE,OAAO;QACxB,eAAe,EAAE,OAAO;QACxB,mBAAmB,EAAE,WAAW;QAChC,cAAc,EAAE,MAAM;QACtB,oBAAoB,EAAE,aAAa;QACnC,yBAAyB,EAAE,4BAA4B;QACvD,uBAAuB,EAAE,gBAAgB;QAEzC,UAAU;QACV,SAAS,EAAE,SAAS;KACvB;IACD,OAAO,EAAE;QACL,kBAAkB;QAClB,eAAe,EAAE,iBAAiB;QAClC,qBAAqB,EAAE,eAAe;QACtC,mBAAmB,EAAE,YAAY;QACjC,kBAAkB,EAAE,WAAW;QAC/B,0BAA0B,EAAE,0BAA0B;QACtD,qBAAqB,EAAE,cAAc;QACrC,mBAAmB,EAAE,UAAU;QAC/B,iBAAiB,EAAE,WAAW;QAC9B,uBAAuB,EAAE,iBAAiB;QAC1C,mBAAmB,EAAE,OAAO;QAC5B,sBAAsB,EAAE,OAAO;QAC/B,uBAAuB,EAAE,MAAM;QAC/B,gBAAgB,EAAE,MAAM;QACxB,eAAe,EAAE,MAAM;QACvB,cAAc,EAAE,IAAI;QACpB,mBAAmB,EAAE,0BAA0B;QAC/C,4BAA4B,EAAE,aAAa;QAE3C,kBAAkB;QAClB,sBAAsB,EAAE,2CAA2C;QACnE,qBAAqB,EAAE,UAAU;QACjC,eAAe,EAAE,IAAI;QACrB,sBAAsB,EAAE,UAAU;QAElC,gBAAgB;QAChB,aAAa,EAAE,eAAe;QAC9B,oBAAoB,EAAE,OAAO;QAC7B,oBAAoB,EAAE,MAAM;QAC5B,uBAAuB,EAAE,OAAO;QAChC,iBAAiB,EAAE,IAAI;QACvB,iBAAiB,EAAE,MAAM;QACzB,kBAAkB,EAAE,OAAO;QAC3B,oBAAoB,EAAE,MAAM;QAE5B,UAAU;QACV,6BAA6B,EAAE,gBAAgB;QAC/C,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,eAAe,EAAE,KAAK;QACtB,mBAAmB,EAAE,IAAI;QACzB,cAAc,EAAE,IAAI;QACpB,oBAAoB,EAAE,KAAK;QAC3B,yBAAyB,EAAE,oBAAoB;QAC/C,uBAAuB,EAAE,MAAM;QAE/B,UAAU;QACV,SAAS,EAAE,IAAI;KAClB;CACJ,CAAC;AAEF,MAAa,IAAI;IAGb,MAAM,CAAC,IAAI;QACP,eAAe;QACf,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;QAEzC,UAAU;QACV,MAAM,gBAAgB,GAA8B;YAChD,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;SAChB,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,MAAM,aAAa,YAAY,GAAG,CAAC,CAAC;IAC7F,CAAC;IAED,MAAM,CAAC,CAAC,CAAC,GAAW,EAAE,GAAG,IAAW;QAChC,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,MAA+B,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxF,IAAI,OAAO,GAAG,cAAc,CAAC,GAAkC,CAAC,IAAI,GAAG,CAAC;QAExE,UAAU;QACV,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACxB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;SACN;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,MAAM;QACT,OAAO,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC;IACnC,CAAC;;AAzCL,oBA0CC;AAzCkB,WAAM,GAAW,IAAI,CAAC;AA2CzC,OAAO;AACP,SAAgB,CAAC,CAAC,GAAW,EAAE,GAAG,IAAW;IACzC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAChC,CAAC;AAFD,cAEC"}
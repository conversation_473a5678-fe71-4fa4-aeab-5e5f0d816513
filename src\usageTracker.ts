import * as vscode from 'vscode';
import { StorageManager, UsageData } from './storage';
import { ConfigManager } from './config';

export interface RealUsageData {
    totalUsage?: number;
    dailyUsage?: number;
    lastUpdate?: string;
}

export class UsageTracker implements vscode.Disposable {
    private storageManager: StorageManager;
    private configManager: ConfigManager;
    private disposables: vscode.Disposable[] = [];
    private currentUsage: number = 0;
    private lastResetDate: string = '';
    private hasRealData: boolean = false;
    private realDataSource: string = 'simulation';

    constructor(storageManager: StorageManager, configManager: ConfigManager) {
        this.storageManager = storageManager;
        this.configManager = configManager;
        this.loadCurrentUsage();
    }

    private async loadCurrentUsage() {
        const data = await this.storageManager.getUsageData();
        this.currentUsage = data.totalUsage;
        this.lastResetDate = data.lastResetDate;
    }

    startTracking() {
        if (!this.configManager.isEnabled()) {
            return;
        }

        // Track text document changes (simulating AI assistance usage)
        const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument(event => {
            if (event.contentChanges.length > 0) {
                this.trackUsage('textChange');
            }
        });

        // Track when documents are saved (potential AI-assisted completion)
        const onDidSaveTextDocument = vscode.workspace.onDidSaveTextDocument(() => {
            this.trackUsage('documentSave');
        });

        // Track completion item selections and other editor events
        const onDidChangeSelection = vscode.window.onDidChangeTextEditorSelection(event => {
            if (event.selections.length > 0) {
                this.trackUsage('selection', 0.05); // Very small increment for selections
            }
        });

        // Track completion item selections (simulating AI completions)
        // Note: This is a simplified simulation since we can't directly access completion providers
        const onDidChangeActiveTextEditor = vscode.window.onDidChangeActiveTextEditor(() => {
            this.trackUsage('editorChange', 0.1); // Small increment for editor changes
        });

        this.disposables.push(
            onDidChangeTextDocument,
            onDidSaveTextDocument,
            onDidChangeSelection,
            onDidChangeActiveTextEditor
        );

        // Periodic cleanup of old data
        setInterval(() => {
            this.storageManager.cleanOldData();
        }, 24 * 60 * 60 * 1000); // Daily cleanup

        // 定期获取真实数据
        const refreshInterval = this.configManager.getRefreshInterval() * 1000;
        console.log(`🔄 Starting real data refresh with ${refreshInterval/1000}s interval`);

        setInterval(() => {
            this.fetchRealUsageData();
        }, refreshInterval);

        // 立即获取一次真实数据
        this.fetchRealUsageData();
    }



    private async trackUsage(type: string, increment: number = 1) {
        if (!this.configManager.isEnabled()) {
            return;
        }

        // Apply different weights based on action type
        let actualIncrement = increment;
        switch (type) {
            case 'textChange':
                actualIncrement = 0.1; // Small increment for text changes
                break;
            case 'documentSave':
                actualIncrement = 1; // Standard increment for saves
                break;
            case 'selection':
                actualIncrement = increment; // Use provided increment for selections
                break;
            case 'editorChange':
                actualIncrement = increment; // Use provided increment
                break;
            default:
                actualIncrement = increment;
        }

        try {
            const data = await this.storageManager.incrementUsage(actualIncrement);
            this.currentUsage = data.totalUsage;
            
            // Check if usage limit is reached
            const limit = this.configManager.getUsageLimit();
            if (this.currentUsage >= limit) {
                this.showUsageLimitWarning();
            }
        } catch (error) {
            console.error('Error tracking usage:', error);
        }
    }

    private showUsageLimitWarning() {
        const limit = this.configManager.getUsageLimit();
        vscode.window.showWarningMessage(
            `Augment usage limit reached (${limit}). Consider upgrading your plan.`,
            'View Details',
            'Reset Usage',
            'Open Settings'
        ).then(selection => {
            switch (selection) {
                case 'View Details':
                    vscode.commands.executeCommand('augmentTracker.showDetails');
                    break;
                case 'Reset Usage':
                    this.resetUsage();
                    break;
                case 'Open Settings':
                    vscode.commands.executeCommand('augmentTracker.openSettings');
                    break;
            }
        });
    }

    async resetUsage() {
        await this.storageManager.resetUsage();
        const data = await this.storageManager.getUsageData();
        this.currentUsage = data.totalUsage;
        this.lastResetDate = data.lastResetDate;
        this.hasRealData = false;
        this.realDataSource = 'simulation';
    }

    getCurrentUsage(): number {
        return Math.round(this.currentUsage);
    }

    getLastResetDate(): string {
        return new Date(this.lastResetDate).toLocaleDateString();
    }

    hasRealUsageData(): boolean {
        return this.hasRealData;
    }

    getDataSource(): string {
        return this.realDataSource;
    }

    async updateWithRealData(realData: RealUsageData): Promise<void> {
        try {
            if (realData.totalUsage !== undefined) {
                // Update with real total usage
                this.currentUsage = realData.totalUsage;
                this.hasRealData = true;
                this.realDataSource = 'augment_api';

                // Store the real data
                const data = await this.storageManager.getUsageData();
                data.totalUsage = realData.totalUsage;
                if (realData.lastUpdate) {
                    data.lastUpdateDate = realData.lastUpdate;
                }
                await this.storageManager.saveUsageData(data);

                console.log(`Updated with real Augment data: ${realData.totalUsage}`);
            } else if (realData.dailyUsage !== undefined) {
                // Update with daily usage increment
                await this.trackUsage('real_data', realData.dailyUsage);
                this.hasRealData = true;
                this.realDataSource = 'augment_daily';
            }
        } catch (error) {
            console.error('Error updating with real data:', error);
        }
    }

    async promptUserForRealData(): Promise<void> {
        const choice = await vscode.window.showInformationMessage(
            'Would you like to manually input your Augment usage data?',
            'Enter Usage', 'Enter Limit', 'Cancel'
        );

        if (choice === 'Enter Usage') {
            const usageInput = await vscode.window.showInputBox({
                prompt: 'Enter your current Augment usage count',
                placeHolder: 'e.g., 150',
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num < 0) {
                        return 'Please enter a valid positive number';
                    }
                    return null;
                }
            });

            if (usageInput) {
                const usage = parseInt(usageInput);
                await this.updateWithRealData({ totalUsage: usage });
                this.realDataSource = 'user_input';
                vscode.window.showInformationMessage(`Usage updated to ${usage}`);
            }
        } else if (choice === 'Enter Limit') {
            const limitInput = await vscode.window.showInputBox({
                prompt: 'Enter your Augment usage limit',
                placeHolder: 'e.g., 1000',
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num <= 0) {
                        return 'Please enter a valid positive number';
                    }
                    return null;
                }
            });

            if (limitInput) {
                const limit = parseInt(limitInput);
                await this.configManager.updateConfig('usageLimit', limit);
                vscode.window.showInformationMessage(`Usage limit updated to ${limit}`);
            }
        }
    }

    async getTodayUsage(): Promise<number> {
        return await this.storageManager.getTodayUsage();
    }

    async getWeeklyUsage(): Promise<number> {
        return await this.storageManager.getWeeklyUsage();
    }

    private async fetchRealUsageData(): Promise<void> {
        try {
            console.log('🔍 Fetching real usage data from Augment API...');

            // 这里需要访问AugmentDetector来获取API客户端
            // 由于架构限制，我们通过全局变量或事件来获取
            const event = new CustomEvent('requestRealUsageData');
            if (typeof window !== 'undefined') {
                window.dispatchEvent(event);
            }

            // 模拟API调用（实际应该通过AugmentDetector）
            // 这个方法会在extension.ts中被重写
            console.log('⚠️ fetchRealUsageData method needs to be connected to AugmentDetector');

        } catch (error) {
            console.error('❌ Error fetching real usage data:', error);
        }
    }

    // 设置真实数据获取器（由extension.ts调用）
    setRealDataFetcher(fetcher: () => Promise<void>): void {
        this.fetchRealUsageData = fetcher;
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}

import * as vscode from 'vscode';
import { StorageManager, UsageData } from './storage';
import { ConfigManager } from './config';

export class UsageTracker implements vscode.Disposable {
    private storageManager: StorageManager;
    private configManager: ConfigManager;
    private disposables: vscode.Disposable[] = [];
    private currentUsage: number = 0;
    private lastResetDate: string = '';

    constructor(storageManager: StorageManager, configManager: ConfigManager) {
        this.storageManager = storageManager;
        this.configManager = configManager;
        this.loadCurrentUsage();
    }

    private async loadCurrentUsage() {
        const data = await this.storageManager.getUsageData();
        this.currentUsage = data.totalUsage;
        this.lastResetDate = data.lastResetDate;
    }

    startTracking() {
        if (!this.configManager.isEnabled()) {
            return;
        }

        // Track text document changes (simulating AI assistance usage)
        const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument(event => {
            if (event.contentChanges.length > 0) {
                this.trackUsage('textChange');
            }
        });

        // Track when documents are saved (potential AI-assisted completion)
        const onDidSaveTextDocument = vscode.workspace.onDidSaveTextDocument(() => {
            this.trackUsage('documentSave');
        });

        // Track command executions that might indicate AI usage
        const onDidExecuteCommand = vscode.commands.onDidExecuteCommand(event => {
            if (this.isAIRelatedCommand(event.command)) {
                this.trackUsage('aiCommand');
            }
        });

        // Track completion item selections (simulating AI completions)
        // Note: This is a simplified simulation since we can't directly access completion providers
        const onDidChangeActiveTextEditor = vscode.window.onDidChangeActiveTextEditor(() => {
            this.trackUsage('editorChange', 0.1); // Small increment for editor changes
        });

        this.disposables.push(
            onDidChangeTextDocument,
            onDidSaveTextDocument,
            onDidExecuteCommand,
            onDidChangeActiveTextEditor
        );

        // Periodic cleanup of old data
        setInterval(() => {
            this.storageManager.cleanOldData();
        }, 24 * 60 * 60 * 1000); // Daily cleanup
    }

    private isAIRelatedCommand(command: string): boolean {
        const aiCommands = [
            'editor.action.triggerSuggest',
            'editor.action.triggerParameterHints',
            'editor.action.quickFix',
            'editor.action.refactor',
            'workbench.action.showCommands',
            // Add more AI-related commands as needed
        ];
        
        return aiCommands.some(cmd => command.includes(cmd)) || 
               command.includes('augment') || 
               command.includes('copilot') ||
               command.includes('ai');
    }

    private async trackUsage(type: string, increment: number = 1) {
        if (!this.configManager.isEnabled()) {
            return;
        }

        // Apply different weights based on action type
        let actualIncrement = increment;
        switch (type) {
            case 'textChange':
                actualIncrement = 0.1; // Small increment for text changes
                break;
            case 'documentSave':
                actualIncrement = 1; // Standard increment for saves
                break;
            case 'aiCommand':
                actualIncrement = 2; // Higher increment for AI commands
                break;
            case 'editorChange':
                actualIncrement = increment; // Use provided increment
                break;
            default:
                actualIncrement = increment;
        }

        try {
            const data = await this.storageManager.incrementUsage(actualIncrement);
            this.currentUsage = data.totalUsage;
            
            // Check if usage limit is reached
            const limit = this.configManager.getUsageLimit();
            if (this.currentUsage >= limit) {
                this.showUsageLimitWarning();
            }
        } catch (error) {
            console.error('Error tracking usage:', error);
        }
    }

    private showUsageLimitWarning() {
        const limit = this.configManager.getUsageLimit();
        vscode.window.showWarningMessage(
            `Augment usage limit reached (${limit}). Consider upgrading your plan.`,
            'View Details',
            'Reset Usage',
            'Open Settings'
        ).then(selection => {
            switch (selection) {
                case 'View Details':
                    vscode.commands.executeCommand('augmentTracker.showDetails');
                    break;
                case 'Reset Usage':
                    this.resetUsage();
                    break;
                case 'Open Settings':
                    vscode.commands.executeCommand('augmentTracker.openSettings');
                    break;
            }
        });
    }

    async resetUsage() {
        await this.storageManager.resetUsage();
        const data = await this.storageManager.getUsageData();
        this.currentUsage = data.totalUsage;
        this.lastResetDate = data.lastResetDate;
    }

    getCurrentUsage(): number {
        return Math.round(this.currentUsage);
    }

    getLastResetDate(): string {
        return new Date(this.lastResetDate).toLocaleDateString();
    }

    async getTodayUsage(): Promise<number> {
        return await this.storageManager.getTodayUsage();
    }

    async getWeeklyUsage(): Promise<number> {
        return await this.storageManager.getWeeklyUsage();
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}

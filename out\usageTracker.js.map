{"version": 3, "file": "usageTracker.js", "sourceRoot": "", "sources": ["../src/usageTracker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,MAAa,YAAY;IAOrB,YAAY,cAA8B,EAAE,aAA4B;QAJhE,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAW,CAAC,CAAC;QACzB,kBAAa,GAAW,EAAE,CAAC;QAG/B,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC1B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;IAC5C,CAAC;IAED,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE;YACjC,OAAO;SACV;QAED,+DAA+D;QAC/D,MAAM,uBAAuB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YAC7E,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;aACjC;QACL,CAAC,CAAC,CAAC;QAEH,oEAAoE;QACpE,MAAM,qBAAqB,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE;YACtE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,wDAAwD;QACxD,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YACpE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBACxC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;aAChC;QACL,CAAC,CAAC,CAAC;QAEH,+DAA+D;QAC/D,4FAA4F;QAC5F,MAAM,2BAA2B,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,GAAG,EAAE;YAC/E,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,qCAAqC;QAC/E,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,uBAAuB,EACvB,qBAAqB,EACrB,mBAAmB,EACnB,2BAA2B,CAC9B,CAAC;QAEF,+BAA+B;QAC/B,WAAW,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACvC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;IAC7C,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACtC,MAAM,UAAU,GAAG;YACf,8BAA8B;YAC9B,qCAAqC;YACrC,wBAAwB;YACxB,wBAAwB;YACxB,+BAA+B;YAC/B,yCAAyC;SAC5C,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC7C,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC3B,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,IAAY,EAAE,YAAoB,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE;YACjC,OAAO;SACV;QAED,+CAA+C;QAC/C,IAAI,eAAe,GAAG,SAAS,CAAC;QAChC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY;gBACb,eAAe,GAAG,GAAG,CAAC,CAAC,mCAAmC;gBAC1D,MAAM;YACV,KAAK,cAAc;gBACf,eAAe,GAAG,CAAC,CAAC,CAAC,+BAA+B;gBACpD,MAAM;YACV,KAAK,WAAW;gBACZ,eAAe,GAAG,CAAC,CAAC,CAAC,mCAAmC;gBACxD,MAAM;YACV,KAAK,cAAc;gBACf,eAAe,GAAG,SAAS,CAAC,CAAC,yBAAyB;gBACtD,MAAM;YACV;gBACI,eAAe,GAAG,SAAS,CAAC;SACnC;QAED,IAAI;YACA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YACvE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;YAEpC,kCAAkC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;YACjD,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAChC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAEO,qBAAqB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,gCAAgC,KAAK,kCAAkC,EACvE,cAAc,EACd,aAAa,EACb,eAAe,CAClB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,QAAQ,SAAS,EAAE;gBACf,KAAK,cAAc;oBACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;oBAC7D,MAAM;gBACV,KAAK,aAAa;oBACd,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,MAAM;gBACV,KAAK,eAAe;oBAChB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;oBAC9D,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;IAC5C,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,aAAa;QACf,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;IACtD,CAAC;IAED,OAAO;QACH,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AArKD,oCAqKC"}
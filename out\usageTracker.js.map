{"version": 3, "file": "usageTracker.js", "sourceRoot": "", "sources": ["../src/usageTracker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAUjC,MAAa,YAAY;IASrB,YAAY,cAA8B,EAAE,aAA4B;QANhE,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAW,CAAC,CAAC;QACzB,kBAAa,GAAW,EAAE,CAAC;QAC3B,gBAAW,GAAY,KAAK,CAAC;QAC7B,mBAAc,GAAW,YAAY,CAAC;QAG1C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC1B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;IAC5C,CAAC;IAED,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE;YACjC,OAAO;SACV;QAED,+DAA+D;QAC/D,MAAM,uBAAuB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YAC7E,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;aACjC;QACL,CAAC,CAAC,CAAC;QAEH,oEAAoE;QACpE,MAAM,qBAAqB,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE;YACtE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,2DAA2D;QAC3D,MAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,KAAK,CAAC,EAAE;YAC9E,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7B,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,sCAAsC;aAC7E;QACL,CAAC,CAAC,CAAC;QAEH,+DAA+D;QAC/D,4FAA4F;QAC5F,MAAM,2BAA2B,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,GAAG,EAAE;YAC/E,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,qCAAqC;QAC/E,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,uBAAuB,EACvB,qBAAqB,EACrB,oBAAoB,EACpB,2BAA2B,CAC9B,CAAC;QAEF,+BAA+B;QAC/B,WAAW,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACvC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;IAC7C,CAAC;IAIO,KAAK,CAAC,UAAU,CAAC,IAAY,EAAE,YAAoB,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE;YACjC,OAAO;SACV;QAED,+CAA+C;QAC/C,IAAI,eAAe,GAAG,SAAS,CAAC;QAChC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY;gBACb,eAAe,GAAG,GAAG,CAAC,CAAC,mCAAmC;gBAC1D,MAAM;YACV,KAAK,cAAc;gBACf,eAAe,GAAG,CAAC,CAAC,CAAC,+BAA+B;gBACpD,MAAM;YACV,KAAK,WAAW;gBACZ,eAAe,GAAG,SAAS,CAAC,CAAC,wCAAwC;gBACrE,MAAM;YACV,KAAK,cAAc;gBACf,eAAe,GAAG,SAAS,CAAC,CAAC,yBAAyB;gBACtD,MAAM;YACV;gBACI,eAAe,GAAG,SAAS,CAAC;SACnC;QAED,IAAI;YACA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YACvE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;YAEpC,kCAAkC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;YACjD,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAChC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAEO,qBAAqB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,gCAAgC,KAAK,kCAAkC,EACvE,cAAc,EACd,aAAa,EACb,eAAe,CAClB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,QAAQ,SAAS,EAAE;gBACf,KAAK,cAAc;oBACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;oBAC7D,MAAM;gBACV,KAAK,aAAa;oBACd,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,MAAM;gBACV,KAAK,eAAe;oBAChB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;oBAC9D,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC;IACvC,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,CAAC;IAC7D,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,aAAa;QACT,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAuB;QAC5C,IAAI;YACA,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;gBACnC,+BAA+B;gBAC/B,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC;gBACxC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;gBAEpC,sBAAsB;gBACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;gBACtD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;gBACtC,IAAI,QAAQ,CAAC,UAAU,EAAE;oBACrB,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC;iBAC7C;gBACD,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAE9C,OAAO,CAAC,GAAG,CAAC,mCAAmC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;aACzE;iBAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;gBAC1C,oCAAoC;gBACpC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACxD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;aACzC;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;SAC1D;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB;QACvB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,2DAA2D,EAC3D,aAAa,EAAE,aAAa,EAAE,QAAQ,CACzC,CAAC;QAEF,IAAI,MAAM,KAAK,aAAa,EAAE;YAC1B,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAChD,MAAM,EAAE,wCAAwC;gBAChD,WAAW,EAAE,WAAW;gBACxB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;oBACrB,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE;wBACvB,OAAO,sCAAsC,CAAC;qBACjD;oBACD,OAAO,IAAI,CAAC;gBAChB,CAAC;aACJ,CAAC,CAAC;YAEH,IAAI,UAAU,EAAE;gBACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACnC,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;gBACrD,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC;aACrE;SACJ;aAAM,IAAI,MAAM,KAAK,aAAa,EAAE;YACjC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAChD,MAAM,EAAE,gCAAgC;gBACxC,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;oBACrB,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;wBACxB,OAAO,sCAAsC,CAAC;qBACjD;oBACD,OAAO,IAAI,CAAC;gBAChB,CAAC;aACJ,CAAC,CAAC;YAEH,IAAI,UAAU,EAAE;gBACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACnC,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBAC3D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;aAC3E;SACJ;IACL,CAAC;IAED,KAAK,CAAC,aAAa;QACf,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;IACtD,CAAC;IAED,OAAO;QACH,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AA7OD,oCA6OC"}
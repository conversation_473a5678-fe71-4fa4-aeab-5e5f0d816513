# VSCode插件国际化深度解决方案

## 🚨 问题根本原因分析

经过深入研究，我发现VSCode插件的命令面板国际化有以下关键点：

### 1. VSCode语言选择机制
- **VSCode根据系统语言或VSCode界面语言选择语言包**
- **不是根据插件内部的语言设置**
- **package.nls.json文件必须与VSCode的语言设置匹配**

### 2. 语言包文件命名规则
- `package.nls.json` - 默认英文
- `package.nls.zh-cn.json` - 简体中文
- `package.nls.zh-tw.json` - 繁体中文
- 文件名必须严格按照VSCode的locale标准

### 3. 占位符格式要求
- 必须使用 `%key%` 格式
- 键名必须在语言包中存在
- 大小写敏感

## 🔧 完整解决方案

### 方案1: 使用vscode-nls库（推荐）

#### 1. 安装vscode-nls
```bash
npm install vscode-nls
```

#### 2. 修改package.json
```json
{
  "dependencies": {
    "vscode-nls": "^5.2.0"
  }
}
```

#### 3. 创建本地化文件结构
```
extension/
├── package.json
├── package.nls.json
├── package.nls.zh-cn.json
├── src/
│   ├── extension.ts
│   └── nls/
│       ├── bundle.l10n.json
│       └── bundle.l10n.zh-cn.json
```

#### 4. 修改extension.ts使用vscode-nls
```typescript
import * as nls from 'vscode-nls';

// 初始化本地化
const localize = nls.loadMessageBundle();

// 注册命令时使用本地化
const webLoginCommand = vscode.commands.registerCommand('augmentTracker.webLogin', async () => {
    // 使用本地化文本
    const message = localize('webLogin.browserOpened', 'Browser opened! Please login to Augment.');
    vscode.window.showInformationMessage(message);
});
```

### 方案2: 修复当前package.nls方案

#### 1. 确保VSCode语言设置
用户需要设置VSCode为中文：
1. `Ctrl+Shift+P` → "Configure Display Language"
2. 选择 "中文(简体)"
3. 重启VSCode

#### 2. 验证语言包文件格式
确保package.nls.zh-cn.json格式正确：
```json
{
  "command.webLogin": "🌐 网页自动登录",
  "command.showDetails": "显示使用详情",
  "command.manualRefresh": "🔄 手动刷新"
}
```

#### 3. 确保package.json占位符正确
```json
{
  "commands": [
    {
      "command": "augmentTracker.webLogin",
      "title": "%command.webLogin%",
      "category": "Augment Tracker"
    }
  ]
}
```

### 方案3: 混合方案（最佳实践）

#### 1. 保留package.nls用于命令标题
- 用于VSCode命令面板的静态文本
- 根据VSCode语言设置自动选择

#### 2. 使用vscode-nls用于动态文本
- 用于插件运行时的消息和界面
- 可以根据插件设置动态切换

## 🧪 测试验证方案

### 测试1: 验证VSCode语言设置
```bash
# 检查VSCode当前语言
code --locale=zh-cn

# 或在VSCode中检查
# Help → About → 查看locale信息
```

### 测试2: 创建最小测试插件
创建一个最小的测试插件来验证国际化机制：

```json
// package.json
{
  "name": "i18n-test",
  "contributes": {
    "commands": [
      {
        "command": "test.hello",
        "title": "%command.hello%"
      }
    ]
  }
}
```

```json
// package.nls.json
{
  "command.hello": "Hello World"
}
```

```json
// package.nls.zh-cn.json
{
  "command.hello": "你好世界"
}
```

### 测试3: 验证打包内容
```bash
# 解压插件包查看内容
unzip -l augment-usage-tracker-1.0.0.vsix | grep nls
```

## 🎯 立即可行的解决方案

### 快速修复（5分钟）
1. **确认用户VSCode语言设置**
   - 让用户检查VSCode是否设置为中文
   - 如果不是，设置为中文并重启VSCode

2. **验证语言包打包**
   - 确保package.nls.zh-cn.json被正确打包到插件中

### 中期解决方案（30分钟）
1. **添加vscode-nls依赖**
2. **重构动态文本使用vscode-nls**
3. **保留package.nls用于命令标题**

### 长期解决方案（2小时）
1. **完整的国际化架构**
2. **支持更多语言**
3. **用户友好的语言切换体验**

## 🔍 调试步骤

### 1. 检查VSCode语言设置
```javascript
// 在extension.ts中添加调试代码
console.log('VSCode locale:', vscode.env.language);
console.log('Plugin language setting:', vscode.workspace.getConfiguration('augmentTracker').get('language'));
```

### 2. 验证语言包加载
```bash
# 在VSCode开发者工具中检查
# Help → Toggle Developer Tools
# Console中查看是否有语言包加载错误
```

### 3. 手动测试命令
```bash
# 在命令面板中输入命令ID
# 查看显示的标题是否为中文
```

## 📋 检查清单

### 用户端检查
- [ ] VSCode界面语言设置为中文
- [ ] 重启VSCode后测试
- [ ] 检查其他插件的中文命令是否正常

### 开发端检查
- [ ] package.nls.zh-cn.json文件存在且格式正确
- [ ] package.json使用占位符格式
- [ ] 语言包文件被正确打包到插件中
- [ ] 占位符键名与语言包中的键名完全匹配

### 系统端检查
- [ ] 操作系统语言设置
- [ ] VSCode版本兼容性
- [ ] 插件安装位置和权限

## 🚀 推荐实施步骤

1. **立即执行**：运行诊断工具确认问题
2. **短期修复**：确保用户VSCode语言设置正确
3. **中期改进**：添加vscode-nls支持
4. **长期优化**：完善国际化体验

---

## 💡 关键洞察

**VSCode插件的命令面板国际化主要依赖于VSCode本身的语言设置，而不是插件内部的语言配置。**

这意味着：
- 用户必须将VSCode设置为中文，命令才会显示中文
- 插件的语言设置主要影响运行时的消息和界面
- package.nls.json的选择完全由VSCode的locale决定

这解释了为什么设置插件语言后命令面板仍显示英文的问题！

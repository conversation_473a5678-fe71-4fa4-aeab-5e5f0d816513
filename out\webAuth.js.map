{"version": 3, "file": "webAuth.js", "sourceRoot": "", "sources": ["../src/webAuth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAQjC,MAAa,cAAc;IAA3B;QACqB,sBAAiB,GAAG,6BAA6B,CAAC;QAClD,0BAAqB,GAAG,uCAAuC,CAAC;IA+PrF,CAAC;IA7PG,KAAK,CAAC,wBAAwB;QAC1B,IAAI;YACA,SAAS;YACT,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACpC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,IAAI;aACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;gBAEzB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC,CAAC;gBAE5E,SAAS;gBACT,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC1D,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAExC,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC,CAAC;gBAE/E,SAAS;gBACT,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,iGAAiG,EACjG,EAAE,KAAK,EAAE,KAAK,EAAE,EAChB,UAAU,EACV,QAAQ,CACX,CAAC;gBAEF,IAAI,WAAW,KAAK,UAAU,EAAE;oBAC5B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;iBACrE;gBAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAC;gBAE3E,eAAe;gBACf,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACjE,iDAAiD;oBACjD,yDAAyD;oBACzD,4DAA4D;oBAC5D,+BAA+B;oBAC/B,wCAAwC,EACxC,cAAc,EACd,cAAc,EACd,QAAQ,CACX,CAAC;gBAEF,IAAI,kBAAkB,KAAK,QAAQ,EAAE;oBACjC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;iBACxE;gBAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;gBAElE,IAAI,kBAAkB,KAAK,cAAc,EAAE;oBACvC,OAAO,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;iBAC1C;qBAAM;oBACH,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;iBACzC;YACL,CAAC,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B,KAAK,EAAE;aAC3C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,IAAI;YACA,6BAA6B;YAC7B,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2BrB,CAAC;YAEK,iBAAiB;YACjB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBAChD,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,YAAY;aACzB,CAAC,CAAC;YACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAE1C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,+BAA+B;gBAC/B,2CAA2C;gBAC3C,0DAA0D;gBAC1D,wBAAwB;gBACxB,4BAA4B;gBAC5B,8BAA8B;gBAC9B,gCAAgC,EAChC,eAAe,EACf,QAAQ,CACX,CAAC;YAEF,IAAI,WAAW,KAAK,eAAe,EAAE;gBACjC,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;aACzC;iBAAM;gBACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC;aACtE;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,KAAK,EAAE;aAC5C,CAAC;SACL;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,MAAM,EAAE,iCAAiC;YACzC,WAAW,EAAE,yDAAyD;YACtE,QAAQ,EAAE,IAAI;YACd,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrC,OAAO,yBAAyB,CAAC;iBACpC;gBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;oBAC9B,OAAO,4CAA4C,CAAC;iBACvD;gBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;oBACjC,OAAO,+CAA+C,CAAC;iBAC1D;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE;YACT,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;aAC1B,CAAC;SACL;aAAM;YACH,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qBAAqB;aAC/B,CAAC;SACL;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,UAAU;QACV,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;YAC7C;gBACI,KAAK,EAAE,cAAc;gBACrB,WAAW,EAAE,sCAAsC;gBACnD,MAAM,EAAE,kCAAkC;aAC7C;YACD;gBACI,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,qCAAqC;gBAClD,MAAM,EAAE,0CAA0C;aACrD;YACD;gBACI,KAAK,EAAE,cAAc;gBACrB,WAAW,EAAE,uBAAuB;gBACpC,MAAM,EAAE,oBAAoB;aAC/B;SACJ,EAAE;YACC,WAAW,EAAE,8BAA8B;YAC3C,cAAc,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC;SACzE;QAED,QAAQ,MAAM,CAAC,KAAK,EAAE;YAClB,KAAK,cAAc;gBACf,OAAO,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjD,KAAK,kBAAkB;gBACnB,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1C,KAAK,cAAc;gBACf,oBAAoB;gBACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,gDAAgD,EAAE,CAAC;YACvF;gBACI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;SACzE;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAe;QACjC,gBAAgB;QAChB,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACpD,OAAO,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CACtC,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC,CACrC,CAAC;IACN,CAAC;IAED,8BAA8B;QAC1B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAwCT,CAAC;IACH,CAAC;CACJ;AAjQD,wCAiQC"}
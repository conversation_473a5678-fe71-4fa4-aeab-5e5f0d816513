# Augment 使用量追踪器

> **中文** | [English](README.en.md)

一个在VSCode状态栏显示Augment AI使用统计的扩展插件。

## 🚀 功能特性

- **状态栏显示**: 显示使用量如 "Augment: 123/1000 ●"
- **实时API集成**: 从Augment API获取真实使用数据
- **5秒自动刷新**: 每5秒更新一次使用数据
- **多种认证方式**: 支持API令牌或浏览器Cookie
- **一键登录**: 自动化浏览器登录和Cookie提取
- **手动刷新**: 立即获取最新数据
- **多语言支持**: 支持中文和英文界面

## 📦 快速开始

### 安装插件
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

### 配置认证
1. 运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 在浏览器中登录并按照Cookie提取指南操作
3. 在状态栏查看真实使用数据

## 🔧 主要命令

| 命令 | 功能 |
|------|------|
| 🌐 网页自动登录 | 自动化登录和设置 |
| 设置浏览器Cookie | 手动配置Cookie |
| 检查认证状态 | 验证设置状态 |
| 显示使用详情 | 显示详细统计信息 |
| 重置使用统计 | 重置本地数据 |
| 🔄 手动刷新 | 立即刷新数据 |
| 🌐 设置语言 | 切换界面语言 |

## 📊 状态栏说明

```
$(pulse) Augment: 7/56 ● (12%)
```

- **7/56**: 当前使用量 / 总限额
- **●**: 真实数据指示器（○ 表示模拟数据）
- **(12%)**: 使用百分比
- **点击**: 打开Augment网站
- **悬停**: 查看详细信息

## ⚙️ 配置选项

- `augmentTracker.enabled`: 启用/禁用追踪器
- `augmentTracker.refreshInterval`: 刷新间隔（默认：5秒）
- `augmentTracker.showInStatusBar`: 在状态栏显示
- `augmentTracker.language`: 界面语言设置
- `augment.cookies`: 浏览器会话Cookie

## 🔐 认证配置

### 方法1：网页自动登录（推荐）
1. 运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 在打开的浏览器中登录Augment
3. 按照提示完成Cookie提取

### 方法2：手动Cookie设置
1. 登录到 https://app.augmentcode.com
2. 复制浏览器Cookie
3. 运行 `Ctrl+Shift+P` → "设置浏览器Cookie"
4. 粘贴Cookie

## 🔄 新功能

### 手动刷新
立即从API获取最新数据：
```
Ctrl+Shift+P → "🔄 手动刷新"
```

### 语言设置
切换界面语言（自动/英文/中文）：
```
Ctrl+Shift+P → "🌐 设置语言"
```

## 🔍 故障排除

### 没有显示真实数据
1. 检查认证状态：`Ctrl+Shift+P` → "检查认证状态"
2. 验证Cookie是否过期
3. 测试API连接：`Ctrl+Shift+P` → "🔄 手动刷新"

### 自动刷新不工作
1. 检查刷新间隔设置
2. 验证插件是否启用
3. 查看开发者控制台错误信息（F12）

## 🛡️ 隐私安全

所有认证数据都存储在本地。不会向第三方发送任何数据。

## 📖 详细文档

### 📚 用户文档
- **[完整中文文档](README.zh-cn.md)** - 详细的使用指南和功能说明
- **[English Documentation](README.en.md)** - Complete English guide
- **[手动功能指南](MANUAL_FEATURES_GUIDE.md)** - 手动刷新和语言设置

### 🔧 开发文档
- **[中文开发指南](DEVELOPMENT.zh-cn.md)** - 中文开发和构建指南
- **[English Development Guide](DEVELOPMENT.md)** - English development guide
- **[国际化指南](I18N_GUIDE.md)** - 多语言支持说明

### 📋 文档索引
- **[文档索引](DOCS_INDEX.md)** - 完整的文档导航和使用指南

## 📞 支持

- 📝 **问题反馈**: [GitHub Issues](https://github.com/augment-tracker/vscode-extension/issues)
- 📧 **邮箱**: <EMAIL>

## 📄 许可证

本项目采用MIT许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

---

**享受在VSCode中实时监控Augment使用情况！** 🚀

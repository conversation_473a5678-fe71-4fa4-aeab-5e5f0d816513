# Augment Usage Tracker | Augment 使用量追踪器

A VSCode extension that displays Augment AI usage statistics in the status bar.

一个在VSCode状态栏显示Augment AI使用统计的扩展插件。

## Features | 功能特性

- **Status Bar Display | 状态栏显示**: Shows usage as "Augment: 123/1000 ●" | 显示使用量如 "Augment: 123/1000 ●"
- **Real-time API Integration | 实时API集成**: Gets actual usage from Augment API | 从Augment API获取真实使用数据
- **5-second Auto Refresh | 5秒自动刷新**: Updates usage data every 5 seconds | 每5秒更新一次使用数据
- **Multiple Authentication | 多种认证方式**: API token or browser cookies | 支持API令牌或浏览器Cookie
- **One-click Login | 一键登录**: Automated browser login with cookie extraction | 自动化浏览器登录和Cookie提取

## Quick Start | 快速开始

### English
1. Install the extension
2. Run `Ctrl+Shift+P` → "🌐 Web Login (Auto)"
3. Login in browser and follow cookie extraction guide
4. View real usage data in status bar

### 中文
1. 安装扩展插件
2. 运行 `Ctrl+Shift+P` → "🌐 Web Login (Auto)"
3. 在浏览器中登录并按照Cookie提取指南操作
4. 在状态栏查看真实使用数据

## Main Commands | 主要命令

- **🌐 Web Login (Auto) | 网页自动登录**: Automated login and setup | 自动化登录和设置
- **Setup Browser Cookies | 设置浏览器Cookie**: Manual cookie configuration | 手动配置Cookie
- **Check Authentication Status | 检查认证状态**: Verify setup | 验证设置状态
- **Show Usage Details | 显示使用详情**: Display detailed statistics | 显示详细统计信息
- **Reset Usage Statistics | 重置使用统计**: Reset local data | 重置本地数据

## Status Bar | 状态栏

- **●** = Real data from API | 来自API的真实数据
- **○** = Simulated data | 模拟数据
- Click to open Augment website | 点击打开Augment网站
- Hover for detailed information | 悬停查看详细信息

## Configuration | 配置选项

- `augmentTracker.refreshInterval`: Refresh interval (default: 5 seconds) | 刷新间隔（默认：5秒）
- `augmentTracker.usageLimit`: Usage limit (default: 1000) | 使用限额（默认：1000）
- `augmentTracker.enabled`: Enable/disable tracker | 启用/禁用追踪器
- `augmentTracker.showInStatusBar`: Show in status bar | 在状态栏显示

## Development | 开发指南

### Prerequisites | 前置要求
- Node.js 16+
- VSCode 1.74.0+
- TypeScript 4.9+

### Setup | 设置开发环境

```bash
# Clone repository | 克隆仓库
git clone <repository-url>
cd augment-status

# Install dependencies | 安装依赖
npm install

# Install TypeScript globally (optional) | 全局安装TypeScript（可选）
npm install -g typescript
```

### Development Workflow | 开发流程

#### 1. Compile TypeScript | 编译TypeScript
```bash
# Compile once | 单次编译
npx tsc

# Watch mode (auto-compile on changes) | 监视模式（文件变化时自动编译）
npx tsc --watch
```

#### 2. Run Extension | 运行扩展
```bash
# Method 1: Use VSCode debugger | 方法1：使用VSCode调试器
# Press F5 in VSCode to launch Extension Development Host
# 在VSCode中按F5启动扩展开发主机

# Method 2: Package and install | 方法2：打包并安装
npm run package
code --install-extension augment-usage-tracker-1.0.0.vsix
```

#### 3. Testing | 测试
```bash
# Test in Extension Development Host | 在扩展开发主机中测试
# 1. Press F5 to open new VSCode window
# 2. Check status bar for "Augment: X/Y"
# 3. Run commands via Ctrl+Shift+P

# 1. 按F5打开新的VSCode窗口
# 2. 检查状态栏是否显示 "Augment: X/Y"
# 3. 通过Ctrl+Shift+P运行命令
```

### Build Commands | 构建命令

```bash
# Compile TypeScript | 编译TypeScript
npm run compile

# Package extension | 打包扩展
npm run package

# Watch mode for development | 开发监视模式
npm run watch
```

### File Structure | 文件结构

```
src/
├── extension.ts        # Main extension entry | 主扩展入口
├── augmentApi.ts      # API client | API客户端
├── augmentDetector.ts # Augment detection | Augment检测
├── statusBar.ts       # Status bar manager | 状态栏管理器
├── usageTracker.ts    # Usage tracking | 使用量追踪
├── storage.ts         # Data storage | 数据存储
├── config.ts          # Configuration | 配置管理
└── webAuth.ts         # Web authentication | 网页认证

out/                   # Compiled JavaScript | 编译后的JavaScript
package.json          # Extension manifest | 扩展清单
tsconfig.json         # TypeScript config | TypeScript配置
```

### Debugging | 调试

#### VSCode Debugging | VSCode调试
1. Open project in VSCode | 在VSCode中打开项目
2. Press `F5` to start debugging | 按F5开始调试
3. New VSCode window opens with extension loaded | 新的VSCode窗口会加载扩展
4. Use `console.log()` and check Debug Console | 使用console.log()并检查调试控制台

#### Console Logging | 控制台日志
```typescript
// Enable detailed logging | 启用详细日志
console.log('🔄 Fetching real usage data...');
console.log('📊 API Response:', response);
```

### Common Issues | 常见问题

#### Compilation Errors | 编译错误
```bash
# Clear output and recompile | 清除输出并重新编译
rm -rf out/
npx tsc
```

#### Extension Not Loading | 扩展未加载
1. Check `package.json` syntax | 检查package.json语法
2. Verify `main` field points to `./out/extension.js` | 验证main字段指向./out/extension.js
3. Ensure TypeScript compiled successfully | 确保TypeScript编译成功

#### API Connection Issues | API连接问题
1. Check authentication setup | 检查认证设置
2. Verify cookies haven't expired | 验证Cookie未过期
3. Test with "Check Authentication Status" command | 使用"检查认证状态"命令测试

## Privacy | 隐私政策

All authentication data is stored locally. No data is sent to third parties.

所有认证数据都存储在本地。不会向第三方发送任何数据。

## License | 许可证

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

本项目采用MIT许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

### MIT License Summary | MIT许可证摘要

- ✅ **Commercial use | 商业使用**: Allowed | 允许
- ✅ **Modification | 修改**: Allowed | 允许
- ✅ **Distribution | 分发**: Allowed | 允许
- ✅ **Private use | 私人使用**: Allowed | 允许
- ❌ **Liability | 责任**: Not provided | 不承担
- ❌ **Warranty | 保证**: Not provided | 不提供

## Contributing | 贡献

Contributions are welcome! Please feel free to submit a Pull Request.

欢迎贡献！请随时提交Pull Request。

## Support | 支持

If you encounter any issues or have questions:

如果您遇到任何问题或有疑问：

- 📝 **Issues | 问题反馈**: [GitHub Issues](https://github.com/augment-tracker/vscode-extension/issues)
- 📧 **Email | 邮箱**: <EMAIL>
- 📖 **Documentation | 文档**: [README](README.md) & [Development Guide](DEVELOPMENT.md)

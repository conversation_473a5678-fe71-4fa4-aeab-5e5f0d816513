# Augment Usage Tracker

A VSCode extension that tracks and displays Augment AI usage statistics in the status bar.

## Features

- **Status Bar Display**: Shows current usage in format "Augment: 123/1000"
- **Real-time Tracking**: Monitors editor events to simulate AI usage
- **Configurable Limits**: Set custom monthly usage limits
- **Click Actions**: Configurable click behavior (open website, show details, or settings)
- **Usage Analytics**: Track daily and weekly usage patterns
- **Augment Integration**: Detects if Augment extension is installed and active

## Installation

1. Open VSCode
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Augment Usage Tracker"
4. Click Install

## Configuration

The extension can be configured through VSCode settings:

- `augmentTracker.enabled`: Enable/disable the tracker (default: true)
- `augmentTracker.usageLimit`: Monthly usage limit (default: 1000)
- `augmentTracker.refreshInterval`: Status bar refresh interval in seconds (default: 30)
- `augmentTracker.showInStatusBar`: Show usage in status bar (default: true)
- `augmentTracker.clickAction`: Action when clicking status bar item (default: "openWebsite")

## Commands

- `Augment Tracker: Reset Usage Statistics` - Reset all usage data
- `Augment Tracker: Show Usage Details` - Display detailed usage information
- `Augment Tracker: Open Settings` - Open extension settings

## Usage Tracking

The extension tracks the following activities as "usage":

- Text document changes (0.1 points)
- Document saves (1 point)
- AI-related commands (2 points)
- Editor changes (0.1 points)

## Status Bar

The status bar item shows:
- Current usage count
- Usage limit
- Color coding based on usage percentage:
  - Normal: < 75%
  - Warning: 75-89%
  - Error: ≥ 90%

## Click Actions

Configure what happens when you click the status bar item:

- **Open Website**: Opens augmentcode.com (default)
- **Show Details**: Shows detailed usage statistics
- **Open Settings**: Opens extension settings

## Data Storage

Usage data is stored locally in VSCode's global state and includes:
- Total usage count
- Daily usage breakdown
- Last reset date
- Automatic cleanup of data older than 30 days

## Augment Integration

The extension can detect if the official Augment extension is installed and will:
- Show integration status in logs
- Attempt to use real usage data (future feature)
- Fall back to simulation mode if Augment is not available

## Development

To contribute or modify this extension:

1. Clone the repository
2. Run `npm install`
3. Open in VSCode
4. Press F5 to launch Extension Development Host
5. Make changes and test

## License

MIT License - see LICENSE file for details

## Support

For issues or feature requests, please visit the GitHub repository.

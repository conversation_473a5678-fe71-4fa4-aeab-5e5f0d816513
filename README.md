# Augment Usage Tracker

A VSCode extension that displays Augment AI usage statistics in the status bar.

## Features

- **Status Bar Display**: Shows usage as "Augment: 123/1000 ●"
- **Real-time API Integration**: Gets actual usage from Augment API
- **5-second Auto Refresh**: Updates usage data every 5 seconds
- **Multiple Authentication**: API token or browser cookies
- **One-click Login**: Automated browser login with cookie extraction

## Quick Start

1. Install the extension
2. Run `Ctrl+Shift+P` → "🌐 Web Login (Auto)"
3. Login in browser and follow cookie extraction guide
4. View real usage data in status bar

## Main Commands

- **🌐 Web Login (Auto)**: Automated login and setup
- **Setup Browser Cookies**: Manual cookie configuration  
- **Debug API Calls**: Test API connection
- **Check Authentication Status**: Verify setup

## Status Bar

- **●** = Real data from API
- **○** = Simulated data
- Click to open Augment website
- Hover for detailed information

## Configuration

- `augmentTracker.refreshInterval`: Refresh interval (default: 5 seconds)
- `augmentTracker.usageLimit`: Usage limit (default: 1000)
- `augmentTracker.enabled`: Enable/disable tracker

## Privacy

All authentication data is stored locally. No data is sent to third parties.

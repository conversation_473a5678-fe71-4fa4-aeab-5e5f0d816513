# Augment 使用量追踪器

一个在VSCode状态栏显示Augment AI使用统计的扩展插件。

## ✨ 主要功能

- 📊 **实时监控**: 状态栏显示当前使用量和限额
- 🔄 **自动刷新**: 每5秒自动更新使用数据
- 🍪 **智能认证**: 自动Cookie提取和过期管理
- 🌐 **一键登录**: 自动打开浏览器并配置认证
- 📈 **使用详情**: 详细的使用统计和剩余额度
- 🌍 **多语言**: 支持中英文界面切换
- 🚪 **安全退出**: 一键清空所有认证数据

## 🚀 快速开始

1. 安装插件后，运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 在打开的浏览器中登录Augment账户
3. 按照提示完成认证配置
4. 在状态栏查看实时使用数据

## 🔧 常用命令

通过 `Ctrl+Shift+P` 打开命令面板，然后输入：

- **🌐 网页自动登录** - 一键配置认证
- **显示使用详情** - 查看详细统计
- **🔄 手动刷新** - 立即更新数据
- **🍪 检查Cookie状态** - 查看认证状态
- **🚪 退出登录** - 清空所有数据

## 📊 状态栏说明

```
$(pulse) Augment: 7/56 ● (12%)     # 有数据时
$(circle-slash) Augment: 未登录    # 未登录时
```

- **7/56**: 当前使用量 / 总限额
- **●**: 真实数据指示器
- **(12%)**: 使用百分比
- **点击**: 配置认证或查看详情

## ⚙️ 配置选项

在VSCode设置中搜索"augment"可配置：

- **启用追踪器**: 开启/关闭功能
- **刷新间隔**: 数据更新频率（默认5秒）
- **状态栏显示**: 是否在状态栏显示
- **界面语言**: 中文/英文切换

## 🔐 认证配置

### 自动登录（推荐）
1. `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 在浏览器中登录Augment账户
3. 按照提示完成配置

### 手动配置
1. 登录 https://app.augmentcode.com
2. `Ctrl+Shift+P` → "设置浏览器Cookie"
3. 按提示输入Cookie信息

## 🔧 高级功能

### Cookie管理
- **自动检测**: 智能检测Cookie过期状态
- **定期提醒**: 过期前自动提醒刷新
- **一键刷新**: `Ctrl+Shift+P` → "🔄 刷新Cookie"

### 其他功能
- **手动刷新**: `Ctrl+Shift+P` → "🔄 手动刷新"
- **语言切换**: `Ctrl+Shift+P` → "🌐 设置语言"
- **安全退出**: `Ctrl+Shift+P` → "🚪 退出登录"

## 🔍 故障排除

### 状态栏显示"未登录"
1. 运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 检查认证状态：`Ctrl+Shift+P` → "检查认证状态"
3. 手动刷新数据：`Ctrl+Shift+P` → "🔄 手动刷新"

### 数据不更新
1. 检查插件是否启用
2. 验证网络连接
3. 查看开发者控制台（F12）是否有错误

## 🛡️ 隐私安全

所有认证数据都存储在本地VSCode中，不会向第三方发送任何数据。

## 📄 许可证

MIT License - 详情请查看 [LICENSE](LICENSE) 文件。

---

**享受在VSCode中实时监控Augment使用情况！** 🚀

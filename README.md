# Augment 使用量追踪器

一个在VSCode状态栏显示Augment AI使用统计的扩展插件。

> **语言**: 中文 | [English](README_EN.md)

## ✨ 主要功能

- 📊 **实时监控**: 状态栏显示当前使用量和限额
- 🔄 **自动刷新**: 每5秒自动更新使用数据
- 🍪 **智能认证**: 自动Cookie提取和过期管理
- 🌐 **一键登录**: 自动打开浏览器并配置认证
- 📈 **使用详情**: 详细的使用统计和剩余额度
- 🌍 **多语言**: 支持中英文界面切换
- 🚪 **安全退出**: 一键清空所有认证数据

## 🚀 快速开始

1. 从VSCode插件商店安装插件
2. 运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
3. 在打开的浏览器中登录Augment账户
4. 按照提示完成认证配置
5. 在状态栏查看实时使用数据

## 🔧 常用命令

通过 `Ctrl+Shift+P` 打开命令面板，然后输入：

- **🌐 网页自动登录** - 一键配置认证
- **显示使用详情** - 查看详细统计
- **🔄 手动刷新** - 立即更新数据
- **🍪 检查Cookie状态** - 查看认证状态
- **🚪 退出登录** - 清空所有数据

## 📊 状态栏说明

状态栏显示您的当前使用情况：

```
Augment: 7/56 (12%)     # 已认证时
Augment: 未登录         # 未登录时
```

- **7/56**: 当前使用量 / 总限额
- **(12%)**: 使用百分比
- **点击**: 配置认证或查看详情

## ⚙️ 配置选项

在VSCode设置中搜索"augment"可配置：

- **启用追踪器**: 开启/关闭功能
- **刷新间隔**: 数据更新频率（默认5秒）
- **状态栏显示**: 是否在状态栏显示
- **界面语言**: 中文/英文切换

## 🔐 认证配置

### 自动登录（推荐）
1. 使用"🌐 网页自动登录"命令
2. 在打开的浏览器中登录
3. 按照设置提示完成配置

### 手动配置
1. 登录 https://app.augmentcode.com
2. 使用"设置浏览器Cookie"命令
3. 按提示输入Cookie信息

## 🔧 高级功能

### Cookie管理
- **自动检测**: 智能检测Cookie过期状态
- **定期提醒**: 过期前自动提醒刷新
- **一键刷新**: 使用"🔄 刷新Cookie"命令

### 其他功能
- **手动刷新**: 使用"🔄 手动刷新"命令
- **语言切换**: 使用"🌐 设置语言"命令
- **安全退出**: 使用"🚪 退出登录"命令

## 🔍 故障排除

### 状态栏显示"未登录"
1. 运行"🌐 网页自动登录"命令
2. 使用"检查认证状态"检查认证
3. 尝试"🔄 手动刷新"更新数据

### 数据不更新
1. 检查插件是否在设置中启用
2. 验证网络连接是否正常
3. 查看开发者控制台(F12)是否有错误

## 🛡️ 隐私安全

所有认证数据都存储在本地VSCode中，不会向第三方发送任何数据。

## 📄 许可证

MIT许可证 - 详情请查看LICENSE文件。

## 📞 支持与反馈

- 在GitHub上报告问题
- 邮箱：<EMAIL>
- 在VSCode商店评分和评论

---

**在VSCode中高效监控您的Augment AI使用情况！**

## 🌟 主要优势

- **节省成本**: 避免意外的超额费用
- **提高效率**: 优化您的AI使用模式
- **保持知情**: 实时了解AI使用情况
- **简单设置**: 一键认证和配置
- **安全可靠**: 所有数据本地存储，无第三方共享

## 🔄 版本历史

### v1.0.0（当前版本）
- ✅ 状态栏实时使用量监控
- ✅ 基于浏览器的自动认证
- ✅ 多语言支持（中英文）
- ✅ 智能Cookie管理和过期检测
- ✅ 安全退出和数据清理
- ✅ 手动刷新和详细使用统计
- ✅ 可配置的刷新间隔和显示选项

## 🤝 支持与反馈

如果您觉得这个插件有用，请：

- ⭐ 在VSCode商店给我们评分
- 🐛 在GitHub上报告问题或请求功能
- 💬 分享您的反馈和建议

**祝您使用Augment编程愉快！** 🎉

@echo off
chcp 65001 > nul
echo ========================================
echo   🚀 精简Cookie获取方案 - 3个核心方案
echo ========================================
echo.

echo 🎯 我们精简了方案，只保留3个最实用的：
echo.
echo ✅ 保留的核心方案:
echo [1] 📋 3步获取 - 最可靠，人人会用
echo [2] 🔧 VSCode内置 - 最集成，体验最佳
echo [3] 🔌 浏览器扩展 - 最彻底，一键解决
echo.
echo ❌ 移除的方案:
echo • 🍪 一键书签 - 功能重复
echo • 📁 HAR文件导入 - 过于复杂
echo • 🔧 DevTools协议 - 技术门槛高
echo • 📱 二维码传输 - 使用场景有限
echo.

:MAIN_MENU
echo ========================================
echo   📋 选择您要使用的方案
echo ========================================
echo.
echo [1] 📋 3步获取（推荐新手）
echo     • 最可靠，成功率100%%
echo     • 有详细指导，不会出错
echo     • 适合所有用户
echo.
echo [2] 🔧 VSCode内置（推荐VSCode用户）
echo     • 完美集成在VSCode中
echo     • 智能验证和错误提示
echo     • 无需离开开发环境
echo.
echo [3] 🔌 浏览器扩展（推荐重度用户）
echo     • 最彻底的解决方案
echo     • 一键操作，30秒完成
echo     • 支持所有cookie类型
echo.
echo [4] 🎯 智能推荐 - 帮我选择
echo [5] 🚀 立即开始配置
echo [0] 退出
echo.

set /p choice="请输入选项 (0-5): "

if "%choice%"=="1" goto METHOD1
if "%choice%"=="2" goto METHOD2
if "%choice%"=="3" goto METHOD3
if "%choice%"=="4" goto SMART_RECOMMEND
if "%choice%"=="5" goto QUICK_START
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:METHOD1
cls
echo ========================================
echo   📋 方案1: 3步获取（最可靠）
echo ========================================
echo.
echo 🎯 适合人群: 所有用户，特别是新手
echo ⭐ 推荐指数: ★★★★★
echo 🕐 所需时间: 2-3分钟
echo 📊 成功率: 100%%
echo.
echo 📱 使用步骤:
echo.
echo 步骤1: 在VSCode中按 Ctrl+Shift+P
echo 步骤2: 输入 "🍪 超简单Cookie配置"
echo 步骤3: 选择 "📋 3步获取：F12 → Application → 复制"
echo 步骤4: 按照详细指导操作:
echo        • 按F12打开开发者工具
echo        • 点击Application标签页
echo        • 找到Cookies → app.augmentcode.com
echo        • 双击_session的Value值并复制
echo 步骤5: 返回VSCode粘贴，自动配置完成！
echo.
echo 💡 为什么推荐这个方案:
echo • ✅ 最可靠 - 成功率100%%，从不失败
echo • ✅ 最简单 - 有详细指导，不会出错
echo • ✅ 最通用 - 适合所有技术水平的用户
echo • ✅ 最安全 - 手动操作，完全可控
echo.
echo 🔍 适用场景:
echo • 首次配置Augment
echo • 其他方案失败时的兜底方案
echo • 不想安装额外工具
echo • 需要最高可靠性
echo.
pause
goto MAIN_MENU

:METHOD2
cls
echo ========================================
echo   🔧 方案2: VSCode内置（最集成）
echo ========================================
echo.
echo 🎯 适合人群: VSCode重度用户
echo ⭐ 推荐指数: ★★★★★
echo 🕐 所需时间: 2-5分钟
echo 📊 成功率: 95%%
echo.
echo 📱 使用步骤:
echo.
echo 步骤1: 在VSCode中按 Ctrl+Shift+P
echo 步骤2: 输入 "🍪 超简单Cookie配置"
echo 步骤3: 选择以下任一方式:
echo.
echo   方式A - 直接粘贴:
echo   • 选择 "🍪 我已复制cookie，直接粘贴"
echo   • 智能验证，实时检查格式
echo.
echo   方式B - 3步指导:
echo   • 选择 "📋 3步获取：F12 → Application → 复制"
echo   • 查看详细指导文档
echo.
echo   方式C - 浏览器扩展:
echo   • 选择 "🔌 浏览器扩展（推荐）"
echo   • 查看扩展安装指导
echo.
echo 💡 为什么推荐这个方案:
echo • ✅ 完美集成 - 无需离开VSCode
echo • ✅ 智能验证 - 实时检查cookie格式
echo • ✅ 详细提示 - 友好的错误信息
echo • ✅ 多种选择 - 3种方式任选
echo.
echo 🔍 适用场景:
echo • VSCode重度用户
echo • 喜欢集成体验
echo • 需要智能验证
echo • 想要多种选择
echo.
pause
goto MAIN_MENU

:METHOD3
cls
echo ========================================
echo   🔌 方案3: 浏览器扩展（最彻底）
echo ========================================
echo.
echo 🎯 适合人群: 重度用户，经常使用Augment
echo ⭐ 推荐指数: ★★★★★
echo 🕐 所需时间: 首次安装5分钟，后续30秒
echo 📊 成功率: 100%%
echo.
echo 📱 使用步骤:
echo.
echo 首次安装:
echo 步骤1: 打开Chrome浏览器
echo 步骤2: 进入扩展管理页面（chrome://extensions/）
echo 步骤3: 开启"开发者模式"
echo 步骤4: 点击"加载已解压的扩展程序"
echo 步骤5: 选择 browser-extension 文件夹
echo.
echo 日常使用:
echo 步骤1: 访问 app.augmentcode.com 并登录
echo 步骤2: 点击浏览器工具栏中的扩展图标
echo 步骤3: 点击 "🔍 提取Cookies"
echo 步骤4: 自动提取并复制到剪贴板
echo 步骤5: 在VSCode中粘贴配置
echo.
echo 💡 为什么推荐这个方案:
echo • ✅ 最彻底 - 支持所有cookie类型（包括HttpOnly）
echo • ✅ 最快速 - 30秒完成配置
echo • ✅ 最智能 - 自动检测登录状态
echo • ✅ 最强大 - 拥有特殊权限
echo.
echo 🔍 适用场景:
echo • 经常使用Augment
echo • 需要最快速的配置
echo • 遇到HttpOnly cookie问题
echo • 想要一劳永逸的解决方案
echo.
pause
goto MAIN_MENU

:SMART_RECOMMEND
cls
echo ========================================
echo   🎯 智能推荐 - 帮您选择最适合的方案
echo ========================================
echo.
echo 请回答几个简单问题：
echo.

set /p experience="1. 您的技术水平？ (1=新手, 2=普通, 3=高级): "
set /p frequency="2. 使用Augment的频率？ (1=偶尔, 2=经常, 3=每天): "
set /p preference="3. 您的偏好？ (1=简单可靠, 2=集成体验, 3=快速高效): "

echo.
echo 🤖 分析中...
echo.

if "%experience%"=="1" (
    echo 💡 推荐方案: 📋 3步获取（最可靠）
    echo 理由: 新手用户，需要最可靠和详细指导的方案
    echo.
    echo 📋 使用方法:
    echo Ctrl+Shift+P → "🍪 超简单Cookie配置" → "3步获取"
) else if "%frequency%"=="3" (
    echo 💡 推荐方案: 🔌 浏览器扩展（最彻底）
    echo 理由: 每天使用，需要最快速高效的方案
    echo.
    echo 📋 使用方法:
    echo 安装扩展 → 点击图标 → 一键提取
) else if "%preference%"=="2" (
    echo 💡 推荐方案: 🔧 VSCode内置（最集成）
    echo 理由: 喜欢集成体验，VSCode内置最合适
    echo.
    echo 📋 使用方法:
    echo Ctrl+Shift+P → "🍪 超简单Cookie配置" → 选择方式
) else (
    echo 💡 推荐方案: 📋 3步获取（最可靠）
    echo 理由: 综合考虑，3步获取最适合您
    echo.
    echo 📋 使用方法:
    echo Ctrl+Shift+P → "🍪 超简单Cookie配置" → "3步获取"
)

echo.
set /p try_now="是否立即尝试推荐的方案？ (y/N): "
if /i "%try_now%"=="y" goto QUICK_START

pause
goto MAIN_MENU

:QUICK_START
cls
echo ========================================
echo   🚀 立即开始配置Cookie
echo ========================================
echo.
echo 最快的配置方式（适合所有用户）：
echo.
echo 📋 步骤1: 确保已登录 app.augmentcode.com
echo 📋 步骤2: 在VSCode中按 Ctrl+Shift+P
echo 📋 步骤3: 输入 "🍪 超简单Cookie配置"
echo 📋 步骤4: 选择 "📋 3步获取" 或其他方式
echo 📋 步骤5: 按照指导操作，完成配置
echo.
echo 🎯 推荐顺序:
echo 1. 📋 3步获取 - 最可靠，适合所有人
echo 2. 🔧 VSCode内置 - 最集成，VSCode用户首选
echo 3. 🔌 浏览器扩展 - 最彻底，重度用户首选
echo.
echo 💡 成功标志:
echo 状态栏显示: "<EMAIL>: 7/300 ● (2%%)"
echo 包含: 用户邮箱 + 使用量/限制 + 百分比
echo.
echo 🆘 如果遇到问题:
echo • 确保已登录 app.augmentcode.com
echo • 尝试其他方案
echo • 查看VSCode的详细错误提示
echo • 重新获取cookie
echo.
echo 🔗 更多帮助:
echo • 查看 streamlined_cookie_solutions.md
echo • 运行 typescript_fixes_complete.bat
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo 🎉 感谢使用精简版Cookie配置工具！
echo.
echo 📋 记住最简单的方式:
echo Ctrl+Shift+P → "🍪 超简单Cookie配置" → "3步获取"
echo.
echo 🎯 3个核心方案:
echo • 📋 3步获取 - 最可靠，人人会用
echo • 🔧 VSCode内置 - 最集成，体验最佳
echo • 🔌 浏览器扩展 - 最彻底，一键解决
echo.
echo 💡 选择建议:
echo • 新手用户 → 3步获取
echo • VSCode用户 → VSCode内置
echo • 重度用户 → 浏览器扩展
echo.
pause
exit

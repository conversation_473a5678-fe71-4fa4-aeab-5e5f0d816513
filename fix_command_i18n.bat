@echo off
chcp 65001 > nul
echo ========================================
echo   修复命令面板国际化问题
echo ========================================
echo.

echo 🔧 修复内容:
echo [1] package.json中的命令标题改为占位符
echo [2] 完善英文和中文语言包
echo [3] 修改setLanguage命令提示重启VSCode
echo [4] 重新编译和发布
echo.

echo [1/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [2/4] 验证语言包文件...
if exist package.nls.json (
    echo ✅ 英文语言包存在
) else (
    echo ❌ 缺少英文语言包
    pause
    exit /b 1
)

if exist package.nls.zh-cn.json (
    echo ✅ 中文语言包存在
) else (
    echo ❌ 缺少中文语言包
    pause
    exit /b 1
)

echo.
echo [3/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [4/4] 发布更新...
echo 注意: 这将覆盖当前版本以修复命令面板国际化问题
set /p confirm="确定要发布吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo ❌ 发布失败
        echo 请检查是否已登录: vsce login augment-usage-tracker
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo ========================================
echo   修复完成！
echo ========================================
echo.
echo 修复内容:
echo - ✅ package.json命令标题使用占位符
echo - ✅ 支持命令面板多语言显示
echo - ✅ 设置语言时提示重启VSCode
echo - ✅ 状态栏和界面立即更新语言
echo.
echo 📋 用户使用说明:
echo 1. 运行 Ctrl+Shift+P → "🌐 设置语言"
echo 2. 选择所需语言（中文/英文）
echo 3. 点击"立即重启VSCode"应用命令面板语言
echo 4. 重启后命令面板将显示对应语言
echo.
echo 🔄 测试步骤:
echo 1. 设置语言为中文
echo 2. 重启VSCode
echo 3. 打开命令面板 Ctrl+Shift+P
echo 4. 输入"augment"查看命令是否为中文
echo.
pause

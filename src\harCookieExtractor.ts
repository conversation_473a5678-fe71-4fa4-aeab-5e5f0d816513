import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface HarCookieResult {
    success: boolean;
    cookies?: string;
    error?: string;
}

export interface HarEntry {
    request?: {
        headers?: Array<{ name: string; value: string }>;
        cookies?: Array<{ name: string; value: string }>;
    };
    response?: {
        headers?: Array<{ name: string; value: string }>;
        cookies?: Array<{ name: string; value: string }>;
    };
}

export interface HarFile {
    log?: {
        entries?: HarEntry[];
    };
}

export class HarCookieExtractor {
    
    /**
     * 从HAR文件中提取Augment cookies
     * HAR文件包含所有HTTP请求和响应，包括HttpOnly cookies
     */
    async extractCookiesFromHar(): Promise<HarCookieResult> {
        try {
            // 显示HAR文件导入指导
            const guide = `
📁 HAR文件Cookie提取指南

🎯 HAR文件的优势：
• 包含所有HTTP请求和响应数据
• 包含HttpOnly cookies（JavaScript无法获取的）
• 一次导出，包含完整会话信息
• 最可靠的cookie获取方式

📋 获取HAR文件步骤：

1️⃣ 打开浏览器，访问 app.augmentcode.com
2️⃣ 按F12打开开发者工具
3️⃣ 切换到"Network"标签页
4️⃣ 刷新页面或进行一些操作
5️⃣ 右键点击任意请求 → "Save all as HAR with content"
6️⃣ 保存HAR文件到本地

⚠️ 注意事项：
• HAR文件包含敏感信息，请妥善保管
• 导入后建议删除HAR文件
• 确保HAR文件包含对app.augmentcode.com的请求
            `;

            const doc = await vscode.workspace.openTextDocument({
                content: guide,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);

            const action = await vscode.window.showInformationMessage(
                '📁 请按照指导获取HAR文件，然后选择文件',
                '📂 选择HAR文件',
                '❌ 取消'
            );

            if (action === '📂 选择HAR文件') {
                return await this.selectAndParseHarFile();
            }

            return { success: false, error: 'User cancelled HAR import' };

        } catch (error) {
            return { success: false, error: `HAR extraction error: ${error}` };
        }
    }

    private async selectAndParseHarFile(): Promise<HarCookieResult> {
        try {
            const harFiles = await vscode.window.showOpenDialog({
                canSelectFiles: true,
                canSelectFolders: false,
                canSelectMany: false,
                filters: {
                    'HAR Files': ['har'],
                    'JSON Files': ['json'],
                    'All Files': ['*']
                },
                openLabel: '选择HAR文件'
            });

            if (!harFiles || harFiles.length === 0) {
                return { success: false, error: 'No HAR file selected' };
            }

            const harFilePath = harFiles[0].fsPath;
            return await this.parseHarFile(harFilePath);

        } catch (error) {
            return { success: false, error: `File selection error: ${error}` };
        }
    }

    private async parseHarFile(filePath: string): Promise<HarCookieResult> {
        try {
            console.log('📁 [HAR] 开始解析HAR文件:', filePath);

            // 读取HAR文件
            const harContent = fs.readFileSync(filePath, 'utf8');
            const harData: HarFile = JSON.parse(harContent);

            if (!harData.log || !harData.log.entries) {
                return { success: false, error: 'Invalid HAR file format' };
            }

            console.log('📊 [HAR] HAR文件包含', harData.log.entries.length, '个请求');

            // 查找Augment相关的请求
            const augmentEntries = harData.log.entries.filter(entry => {
                const url = (entry.request as any)?.url || '';
                return url.includes('augmentcode.com') || url.includes('augment');
            });

            console.log('🔍 [HAR] 找到', augmentEntries.length, '个Augment相关请求');

            if (augmentEntries.length === 0) {
                return { 
                    success: false, 
                    error: 'No Augment requests found in HAR file. Please ensure the HAR file contains requests to app.augmentcode.com' 
                };
            }

            // 提取cookies
            const cookies = this.extractCookiesFromEntries(augmentEntries);
            
            if (cookies.length === 0) {
                return { 
                    success: false, 
                    error: 'No _session cookie found in HAR file' 
                };
            }

            console.log('✅ [HAR] 成功提取cookies:', cookies.substring(0, 50) + '...');

            return {
                success: true,
                cookies: cookies
            };

        } catch (error) {
            console.error('❌ [HAR] 解析错误:', error);
            return { success: false, error: `HAR parsing error: ${error}` };
        }
    }

    private extractCookiesFromEntries(entries: HarEntry[]): string {
        const cookieMap = new Map<string, string>();

        for (const entry of entries) {
            // 从请求头中提取cookies
            if (entry.request?.headers) {
                for (const header of entry.request.headers) {
                    if (header.name.toLowerCase() === 'cookie') {
                        this.parseCookieHeader(header.value, cookieMap);
                    }
                }
            }

            // 从请求cookies中提取
            if (entry.request?.cookies) {
                for (const cookie of entry.request.cookies) {
                    if (cookie.name === '_session') {
                        cookieMap.set(cookie.name, cookie.value);
                    }
                }
            }

            // 从响应头中提取Set-Cookie
            if (entry.response?.headers) {
                for (const header of entry.response.headers) {
                    if (header.name.toLowerCase() === 'set-cookie') {
                        this.parseSetCookieHeader(header.value, cookieMap);
                    }
                }
            }

            // 从响应cookies中提取
            if (entry.response?.cookies) {
                for (const cookie of entry.response.cookies) {
                    if (cookie.name === '_session') {
                        cookieMap.set(cookie.name, cookie.value);
                    }
                }
            }
        }

        // 构建cookie字符串
        const cookiePairs: string[] = [];
        for (const [name, value] of cookieMap.entries()) {
            cookiePairs.push(`${name}=${value}`);
        }

        return cookiePairs.join('; ');
    }

    private parseCookieHeader(cookieHeader: string, cookieMap: Map<string, string>) {
        const cookies = cookieHeader.split(';');
        for (const cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === '_session' && value) {
                cookieMap.set(name, value);
            }
        }
    }

    private parseSetCookieHeader(setCookieHeader: string, cookieMap: Map<string, string>) {
        const cookieParts = setCookieHeader.split(';');
        if (cookieParts.length > 0) {
            const [name, value] = cookieParts[0].trim().split('=');
            if (name === '_session' && value) {
                cookieMap.set(name, value);
            }
        }
    }

    /**
     * 验证HAR文件是否包含有效的Augment数据
     */
    async validateHarFile(filePath: string): Promise<boolean> {
        try {
            const harContent = fs.readFileSync(filePath, 'utf8');
            const harData: HarFile = JSON.parse(harContent);

            if (!harData.log || !harData.log.entries) {
                return false;
            }

            // 检查是否包含Augment相关请求
            const hasAugmentRequests = harData.log.entries.some(entry => {
                const url = (entry.request as any)?.url || '';
                return url.includes('augmentcode.com');
            });

            return hasAugmentRequests;

        } catch (error) {
            return false;
        }
    }
}

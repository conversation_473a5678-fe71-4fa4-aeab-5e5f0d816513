# 🔄 真实数据5秒刷新测试指南

## 🎯 修改内容

### ✅ 已实现的功能
1. **5秒刷新间隔**：默认刷新间隔改为5秒
2. **强制使用Credits API**：优先使用`/api/credits`端点
3. **详细JSON输出**：每次刷新时输出完整的API响应数据
4. **实时数据更新**：每5秒自动获取最新使用统计

## 🚀 测试步骤

### 步骤1：重新启动插件
1. 关闭当前的Extension Development Host窗口
2. 按 `F5` 重新启动插件

### 步骤2：配置认证
```
Ctrl+Shift+P → "Setup Browser Cookies"
```
或
```
Ctrl+Shift+P → "🌐 Web Login (Auto)"
```

### 步骤3：观察控制台输出
1. 按 `F12` 打开开发者工具
2. 切换到 `Console` 标签页
3. 观察每5秒的数据输出

## 📊 预期的控制台输出

### 启动时输出
```
🔄 Starting real data refresh with 5s interval
🔍 Fetching real usage data from Augment API...
🪙 Trying Credits API...
```

### 每5秒的API响应
```
📊 Credits API Raw Response: {
  "usageUnitsAvailable": 49,
  "usageUnitsUsedThisBillingCycle": 7,
  "usageUnitsPending": 0
}

✅ Parsed Credits Data: {
  "totalUsage": 7,
  "usageLimit": 56,
  "dailyUsage": 7,
  "subscriptionType": "community"
}

🔄 Fetching real usage data...
✅ Updated with real Augment data: 7
```

### 状态栏显示
```
$(pulse) Augment: 7/56 ● (12%)
```

## 🔧 验证真实数据

### 1. 检查数据源指示器
- **● (实心圆)** = 使用真实API数据
- **○ (空心圆)** = 使用模拟数据

### 2. 检查工具提示
鼠标悬停状态栏应显示：
```
Augment Usage Tracker
Current: 7 credits
Limit: 56 credits
Usage: 12%
Remaining: 49 credits
Data Source: Real data from Augment API
```

### 3. 手动测试API
```
Ctrl+Shift+P → "Debug API Calls" → "🪙 Credits Info"
```

## 🔍 故障排除

### 如果没有看到5秒刷新：
1. 检查控制台是否有错误
2. 确认认证配置正确
3. 重新启动插件

### 如果没有JSON输出：
1. 确保开发者控制台已打开
2. 检查是否有认证错误
3. 验证cookie是否过期

### 如果显示模拟数据：
1. 检查认证状态：`Check Authentication Status`
2. 重新配置cookie或token
3. 查看控制台错误信息

## 📈 数据格式说明

### Credits API响应格式
```json
{
  "usageUnitsAvailable": 49,        // 剩余可用单位
  "usageUnitsUsedThisBillingCycle": 7,  // 本周期已使用
  "usageUnitsPending": 0            // 待处理单位
}
```

### 解析后的数据格式
```json
{
  "totalUsage": 7,          // 已使用 = usageUnitsUsedThisBillingCycle
  "usageLimit": 56,         // 总限额 = usageUnitsAvailable + usageUnitsUsedThisBillingCycle
  "dailyUsage": 7,          // 日使用量
  "subscriptionType": "community"  // 订阅类型
}
```

## 🎯 成功指标

### ✅ 插件正常工作的标志：
1. 控制台每5秒输出一次API数据
2. 状态栏显示实心圆 ●
3. JSON数据与web界面一致
4. 工具提示显示"Real data from Augment API"

### ✅ 实时更新验证：
1. 在Augment web界面进行操作
2. 等待5秒观察插件是否更新
3. 数据应该与web界面同步

## 🔄 配置调整

### 修改刷新间隔
如果5秒太频繁，可以在设置中调整：
```
Ctrl+, → 搜索 "augmentTracker.refreshInterval"
```
设置范围：1-300秒

### 关闭详细日志
如果不需要详细的JSON输出，可以修改代码中的console.log语句。

**现在您应该能看到每5秒一次的真实Credits API数据输出！** 🚀

# 功能测试指南

## 🧪 测试环境准备

### 编译项目
```bash
cd d:\workspace\augment-status
npx tsc
```

### 打包插件
```bash
npx @vscode/vsce package --no-dependencies
```

### 安装测试
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

## 🔧 核心功能测试

### 1. 首次安装测试

#### 测试步骤
1. 卸载现有插件（如果有）
2. 安装新版本插件
3. 重启VSCode
4. 观察是否在3秒后显示欢迎提示

#### 预期结果
```
🎉 欢迎使用 Augment 使用量追踪器！

为了获取真实的使用数据，需要配置Augment认证。
我们提供了简单的自动配置方法。

[🚀 立即配置] [⏰ 稍后配置] [❓ 了解更多]
```

#### 测试各选项
- **立即配置**: 应启动自动Cookie提取流程
- **稍后配置**: 应显示稍后配置提示
- **了解更多**: 应打开配置指南文档

### 2. 自动Cookie提取测试

#### 测试步骤
1. 运行命令：`Ctrl+Shift+P` → "🌐 网页自动登录"
2. 选择 "开始自动提取"
3. 观察浏览器是否自动打开提取页面
4. 按照页面指示完成Cookie提取

#### 预期结果
- 本地服务器启动在端口3000
- 浏览器打开 `http://localhost:3000`
- 显示美观的Cookie提取界面
- 成功提取后VSCode显示成功消息

### 3. Cookie状态检查测试

#### 测试步骤
1. 运行命令：`Ctrl+Shift+P` → "🍪 检查Cookie状态"
2. 观察显示的状态信息

#### 预期结果（有Cookie时）
```
🍪 Cookie状态检查

✅ Cookie状态正常
剩余时间: 20小时

可以正常获取真实使用数据。

[关闭]
```

#### 预期结果（无Cookie时）
```
🍪 Cookie状态检查

❌ 未找到cookie信息
建议运行 "🌐 网页自动登录" 来配置认证。

[立即配置]
```

### 4. Cookie刷新测试

#### 测试步骤
1. 运行命令：`Ctrl+Shift+P` → "🔄 刷新Cookie"
2. 观察刷新流程

#### 预期结果
- 显示 "🔄 正在刷新Augment认证..."
- 启动自动提取流程
- 成功后显示 "✅ 认证刷新成功！"
- 状态栏更新为真实数据指示器

### 5. 增强的使用详情测试

#### 测试步骤
1. 运行命令：`Ctrl+Shift+P` → "显示使用详情"
2. 检查是否包含Cookie状态信息

#### 预期结果
```
Augment 使用统计:
• 当前使用量: 7 积分
• 月度限额: 56 积分
• 使用百分比: 12%
• 剩余: 49 积分
• 数据源: 来自Augment API的真实数据
• 🍪 Cookie状态: ✅ 20小时后过期

上次重置: 2024-01-01
```

如果Cookie即将过期，应显示 "🔄 刷新Cookie" 按钮。

## ⏰ 定期检查测试

### 测试Cookie过期提醒

#### 模拟过期测试
由于正常过期需要20小时，可以通过以下方式模拟：

1. **修改过期时间**（开发测试）
   - 在 `cookieManager.ts` 中临时修改 `COOKIE_EXPIRY_HOURS = 0.1` (6分钟)
   - 重新编译和安装
   - 配置Cookie后等待6分钟观察提醒

2. **手动触发检查**
   - 在开发者控制台中运行：
   ```javascript
   vscode.commands.executeCommand('augmentTracker.checkCookieStatus')
   ```

#### 预期过期提醒
```
⚠️ Augment认证已过期

您的session cookie已过期，无法获取真实使用数据。
是否现在刷新认证？

[🔄 自动刷新] [⏰ 稍后刷新] [❌ 忽略]
```

#### 预期即将过期提醒
```
⏰ Augment认证即将过期

您的session cookie将在2小时后过期。
建议现在刷新以避免中断。

[🔄 现在刷新] [⏰ 稍后提醒] [❌ 忽略]
```

## 🔍 状态栏测试

### 数据源指示器测试

#### 测试步骤
1. 观察状态栏显示
2. 检查数据源指示器

#### 预期结果
- **有效Cookie**: `$(pulse) Augment: 7/56 ● (12%)`
- **无效Cookie**: `$(pulse) Augment: 7/56 ○ (12%)`

### 悬停信息测试

#### 测试步骤
1. 将鼠标悬停在状态栏项目上
2. 检查悬停信息

#### 预期结果
```
Augment 使用量追踪器
当前: 7 积分
限额: 56 积分
使用量: 12%
剩余: 49 积分
数据源: 来自Augment API的真实数据
Cookie状态: ✅ 20小时后过期
```

## 🛠️ 开发者测试

### 控制台日志测试

#### 启用详细日志
1. 打开VSCode开发者工具：`Help` → `Toggle Developer Tools`
2. 切换到Console标签
3. 观察Cookie管理相关日志

#### 预期日志
```
🍪 Initializing Cookie Manager...
🎉 First installation detected, triggering cookie setup...
🍪 Cookie saved, expires at: 2024-01-02 15:30:00
🔄 Periodic cookie check...
🍪 Cookie status: expired=false, nearExpiry=false
🕐 Started periodic cookie check (every 30 minutes)
```

### 错误处理测试

#### 网络错误测试
1. 断开网络连接
2. 尝试刷新Cookie
3. 观察错误处理

#### 预期结果
- 显示网络错误提示
- 提供重试建议
- 不影响其他功能

#### 端口占用测试
1. 手动占用端口3000
2. 尝试启动自动提取
3. 观察错误处理

#### 预期结果
- 显示端口占用错误
- 提供解决建议
- 回退到手动方法

## 📊 性能测试

### 内存泄漏测试

#### 测试步骤
1. 长时间运行VSCode（数小时）
2. 多次触发Cookie刷新
3. 观察内存使用情况

#### 预期结果
- 内存使用稳定
- 定时器正确清理
- 无明显内存泄漏

### 响应性测试

#### 测试步骤
1. 在大型项目中使用插件
2. 同时进行其他VSCode操作
3. 观察性能影响

#### 预期结果
- 不影响VSCode响应性
- 后台操作不阻塞UI
- 资源使用合理

## 🔧 故障排除测试

### 常见问题模拟

#### 1. 首次安装无提示
**模拟方法**: 删除globalState中的初始化标记
**预期修复**: 重启VSCode后显示提示

#### 2. Cookie频繁过期
**模拟方法**: 使用无效的Cookie
**预期修复**: 自动检测并提醒刷新

#### 3. 自动提取失败
**模拟方法**: 在无网络环境下测试
**预期修复**: 显示错误信息和手动方法

## ✅ 测试检查清单

### 基础功能
- [ ] 首次安装显示欢迎提示
- [ ] 自动Cookie提取正常工作
- [ ] Cookie状态检查显示正确信息
- [ ] Cookie刷新功能正常
- [ ] 使用详情包含Cookie状态

### 高级功能
- [ ] 定期检查正常运行
- [ ] 过期提醒及时显示
- [ ] 状态栏正确显示数据源
- [ ] 悬停信息包含Cookie状态
- [ ] 错误处理友好且有效

### 性能和稳定性
- [ ] 无内存泄漏
- [ ] 不影响VSCode性能
- [ ] 异常情况下自动恢复
- [ ] 资源正确清理

### 用户体验
- [ ] 操作流程简单直观
- [ ] 错误提示清晰有用
- [ ] 状态反馈及时准确
- [ ] 文档说明完整易懂

## 📝 测试报告模板

### 测试环境
- VSCode版本: 
- 操作系统: 
- 插件版本: 
- 测试日期: 

### 测试结果
| 功能 | 状态 | 备注 |
|------|------|------|
| 首次安装提示 | ✅/❌ | |
| 自动Cookie提取 | ✅/❌ | |
| Cookie状态检查 | ✅/❌ | |
| Cookie刷新 | ✅/❌ | |
| 定期检查 | ✅/❌ | |
| 过期提醒 | ✅/❌ | |
| 状态栏显示 | ✅/❌ | |
| 错误处理 | ✅/❌ | |

### 发现的问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 严重程度

### 改进建议
1. 功能改进
2. 用户体验优化
3. 性能优化
4. 文档完善

---

**通过完整的测试确保功能的稳定性和用户体验！** 🧪✨

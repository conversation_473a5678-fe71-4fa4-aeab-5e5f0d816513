# 开发指南

> **中文** | [English](DEVELOPMENT.md)

## 快速开始

### 环境要求
- Node.js 16+
- VSCode 1.74.0+
- TypeScript 4.9+

### 安装依赖
```bash
npm install
```

### 开发命令

#### 编译
```bash
# 单次编译
npm run compile

# 监视模式
npm run watch
# 或
npm run dev
```

#### 运行和测试
```bash
# 方法1：VSCode调试
# 按F5启动扩展开发主机

# 方法2：打包安装
npm run build
code --install-extension augment-usage-tracker-1.0.0.vsix
```

#### 打包
```bash
# 仅打包
npm run package

# 编译并打包
npm run build

# 清理输出
npm run clean
```

## 项目结构

```
augment-status/
├── src/                    # TypeScript源码
│   ├── extension.ts        # 主入口
│   ├── augmentApi.ts      # API客户端
│   ├── augmentDetector.ts # Augment检测
│   ├── statusBar.ts       # 状态栏
│   ├── usageTracker.ts    # 使用追踪
│   ├── storage.ts         # 数据存储
│   ├── config.ts          # 配置管理
│   ├── webAuth.ts         # 网页认证
│   └── i18n.ts            # 国际化
├── out/                   # 编译输出
├── package.json           # 扩展清单
├── tsconfig.json          # TypeScript配置
├── README.md              # 中文说明文档
├── README.en.md           # 英文说明文档
├── README.zh-cn.md        # 完整中文文档
└── .vscodeignore          # 打包忽略
```

## 开发流程

### 1. 修改代码
编辑 `src/` 目录下的TypeScript文件

### 2. 编译
```bash
npm run compile
# 或使用监视模式
npm run watch
```

### 3. 测试
按F5启动扩展开发主机，在新窗口中测试功能

### 4. 打包
```bash
npm run package
```

## 调试技巧

### VSCode调试
1. 在代码中添加断点
2. 按F5启动调试
3. 在新窗口中触发功能
4. 查看调试控制台

### 控制台日志
```typescript
console.log('🔄 Debug info:', data);
console.error('❌ Error:', error);
```

### 常见问题

#### 编译错误
```bash
# 清理并重新编译
npm run clean
npm run compile
```

#### 扩展未加载
1. 检查package.json语法
2. 确保main字段正确
3. 验证编译成功

#### API连接问题
1. 检查认证设置
2. 验证Cookie有效性
3. 使用"检查认证状态"命令

## 发布流程

### 1. 更新版本
```bash
# 更新package.json中的version字段
```

### 2. 编译和打包
```bash
npm run build
```

### 3. 测试
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

### 4. 分发
分享生成的.vsix文件

## 贡献指南

### 贡献流程
1. Fork项目
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 创建Pull Request

### 代码规范
- 使用TypeScript
- 遵循ESLint规则
- 添加适当的注释
- 保持代码简洁

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建/工具
```

## 许可证

MIT License

### 许可证详情
本项目采用MIT许可证，这意味着：

- ✅ 可以自由使用、修改和分发
- ✅ 可以用于商业项目
- ✅ 可以创建衍生作品
- ❌ 不提供任何保证
- ❌ 作者不承担责任

详情请查看 [LICENSE](LICENSE) 文件。

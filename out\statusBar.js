"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusBarManager = void 0;
const vscode = __importStar(require("vscode"));
class StatusBarManager {
    constructor(usageTracker, configManager) {
        this.usageTracker = usageTracker;
        this.configManager = configManager;
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.setupStatusBarItem();
        this.startRefreshTimer();
    }
    setupStatusBarItem() {
        this.statusBarItem.tooltip = 'Augment Usage Tracker - Click for more options';
        this.updateDisplay();
        // Set click command based on configuration
        this.updateClickCommand();
    }
    updateClickCommand() {
        const clickAction = this.configManager.getClickAction();
        switch (clickAction) {
            case 'openWebsite':
                this.statusBarItem.command = {
                    command: 'vscode.open',
                    arguments: [vscode.Uri.parse('https://www.augmentcode.com')],
                    title: 'Open Augment Website'
                };
                break;
            case 'showDetails':
                this.statusBarItem.command = 'augmentTracker.showDetails';
                break;
            case 'openSettings':
                this.statusBarItem.command = 'augmentTracker.openSettings';
                break;
            default:
                this.statusBarItem.command = 'augmentTracker.showDetails';
        }
    }
    updateDisplay() {
        if (!this.configManager.isEnabled() || !this.configManager.shouldShowInStatusBar()) {
            this.statusBarItem.hide();
            return;
        }
        const usage = this.usageTracker.getCurrentUsage();
        const limit = this.configManager.getUsageLimit();
        const percentage = Math.round((usage / limit) * 100);
        // Update text with usage information
        this.statusBarItem.text = `$(pulse) Augment: ${usage}/${limit}`;
        // Update tooltip with more details
        this.statusBarItem.tooltip = `Augment Usage Tracker
Current: ${usage}
Limit: ${limit}
Usage: ${percentage}%
Remaining: ${limit - usage}

Click to ${this.getClickActionDescription()}`;
        // Change color based on usage percentage
        if (percentage >= 90) {
            this.statusBarItem.color = new vscode.ThemeColor('statusBarItem.errorForeground');
        }
        else if (percentage >= 75) {
            this.statusBarItem.color = new vscode.ThemeColor('statusBarItem.warningForeground');
        }
        else {
            this.statusBarItem.color = undefined;
        }
        this.updateClickCommand();
        this.statusBarItem.show();
    }
    getClickActionDescription() {
        const action = this.configManager.getClickAction();
        switch (action) {
            case 'openWebsite': return 'open Augment website';
            case 'showDetails': return 'show usage details';
            case 'openSettings': return 'open settings';
            default: return 'show details';
        }
    }
    startRefreshTimer() {
        const interval = this.configManager.getRefreshInterval() * 1000;
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        this.refreshTimer = setInterval(() => {
            this.updateDisplay();
        }, interval);
    }
    show() {
        this.updateDisplay();
    }
    hide() {
        this.statusBarItem.hide();
    }
    dispose() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        this.statusBarItem.dispose();
    }
}
exports.StatusBarManager = StatusBarManager;
//# sourceMappingURL=statusBar.js.map
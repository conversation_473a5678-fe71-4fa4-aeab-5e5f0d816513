# Augment API 集成指南

## 🎯 概述

基于对Augment API的深入研究，我们的插件现在支持真实的Augment API集成，可以获取真实的使用统计数据。

## 🔍 API 发现过程

### 研究发现
通过系统性研究，我们发现了以下关键信息：

1. **API端点**：`https://i1.api.augmentcode.com`
2. **认证方式**：Bearer Token认证
3. **健康检查**：`/health` 端点可用于检查API状态
4. **主要端点**：
   - `/user` - 用户信息
   - `/usage` - 使用统计
   - `/subscription` - 订阅信息

### 认证流程
- Augment使用OAuth 2.0认证流程
- 登录页面：`https://login.augmentcode.com`
- 应用仪表板：`https://app.augmentcode.com`
- 所有API调用需要Authorization header

## 🚀 功能实现

### 新增API客户端
创建了完整的`AugmentApiClient`类，支持：
- ✅ 健康检查
- ✅ 用户认证
- ✅ 使用数据获取
- ✅ 订阅信息查询
- ✅ 错误处理和重试机制

### 增强的检测器
`AugmentDetector`现在支持：
- ✅ API token管理
- ✅ 连接测试
- ✅ 真实数据获取
- ✅ 多种集成方法

## 📋 新增命令

### 1. `Augment Tracker: Setup API Token`
配置Augment API认证token：
```
Ctrl+Shift+P → "Augment Tracker: Setup API Token"
```
- 安全的密码输入框
- 自动保存到VSCode配置
- 立即测试连接

### 2. `Augment Tracker: Test API Connection`
测试API连接状态：
```
Ctrl+Shift+P → "Augment Tracker: Test API Connection"
```
- 验证token有效性
- 检查网络连接
- 显示详细错误信息

### 3. `Augment Tracker: Clear API Token`
清除已保存的API token：
```
Ctrl+Shift+P → "Augment Tracker: Clear API Token"
```
- 安全清除认证信息
- 重置为模拟模式

## 🔧 使用方法

### 步骤1：获取API Token
1. 访问 `https://app.augmentcode.com`
2. 登录您的Augment账户
3. 在开发者设置中生成API token
4. 复制token备用

### 步骤2：配置插件
1. 在VSCode中执行 `Augment Tracker: Setup API Token`
2. 粘贴您的API token
3. 插件会自动测试连接

### 步骤3：验证集成
1. 执行 `Augment Tracker: Test API Connection`
2. 检查状态栏显示是否变为 ● (真实数据)
3. 查看工具提示确认数据源

## 📊 数据集成层级

### 优先级顺序
1. **Augment API** (最高优先级)
   - 直接从官方API获取
   - 实时准确数据
   - 需要认证token

2. **扩展间通信**
   - 调用Augment插件公开命令
   - 部分数据可用
   - 无需额外配置

3. **配置监听**
   - 监听Augment配置变化
   - 基础状态检测
   - 自动触发

4. **模拟数据** (回退方案)
   - 基于编辑器事件
   - 始终可用
   - 用户体验一致

## 🔒 安全考虑

### Token安全
- ✅ 使用VSCode安全存储
- ✅ 密码输入框隐藏显示
- ✅ 本地存储，不传输到第三方
- ✅ 用户完全控制

### API调用安全
- ✅ HTTPS加密传输
- ✅ 标准Bearer认证
- ✅ 错误处理和超时控制
- ✅ 无敏感信息泄露

## 🎨 用户体验

### 状态指示
- **● 绿色**：使用真实API数据
- **○ 灰色**：使用模拟数据
- **颜色编码**：根据使用率动态变化

### 工具提示信息
```
Augment Usage Tracker
Current: 123
Limit: 1000
Usage: 12%
Remaining: 877
Data Source: Real data from Augment API

Augment Plugin:
• Installed: Yes
• Active: Yes
• Version: 1.2.3
• Integration: api
• API Status: Connected

Click to open Augment website
```

## 🔄 数据同步

### 自动同步
- 每30秒检查一次API数据
- 配置变化时立即同步
- 错误时自动回退到模拟模式

### 手动同步
- 重新配置token时立即同步
- 测试连接时验证数据
- 重启插件时重新检测

## 🐛 故障排除

### 常见问题

#### 1. API连接失败
- 检查网络连接
- 验证token是否正确
- 确认Augment服务状态

#### 2. 认证错误
- 重新生成API token
- 检查token是否过期
- 确认账户权限

#### 3. 数据不更新
- 检查API限制
- 验证订阅状态
- 查看控制台错误日志

### 调试步骤
1. 执行 `Test API Connection` 命令
2. 查看开发者控制台日志
3. 检查网络请求状态
4. 验证返回数据格式

## 🚀 未来扩展

### 计划功能
- 📊 详细使用分析
- 📈 历史趋势图表
- 🔔 使用限额提醒
- 👥 团队使用统计
- 📤 数据导出功能

### API扩展
- 支持更多Augment API端点
- 实时WebSocket连接
- 批量数据获取
- 缓存优化策略

这个API集成为插件提供了真实数据支持，同时保持了良好的用户体验和安全性。

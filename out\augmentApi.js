"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AugmentApiClient = void 0;
const vscode = __importStar(require("vscode"));
class AugmentApiClient {
    constructor() {
        this.API_BASE_URL = 'https://app.augmentcode.com/api';
        this.WEB_BASE_URL = 'https://app.augmentcode.com';
        this.authToken = null;
        this.cookies = null;
        this.tempStorage = new Map();
        this.loadAuthToken();
        this.loadCookies();
    }
    async loadAuthToken() {
        try {
            // Try to get auth token from VSCode secrets
            const secrets = vscode.workspace.getConfiguration().get('augment.authToken');
            if (secrets) {
                this.authToken = secrets;
            }
        }
        catch (error) {
            console.log('No Augment auth token found');
        }
    }
    async loadCookies() {
        try {
            // Try to get cookies from VSCode configuration
            const cookies = vscode.workspace.getConfiguration().get('augment.cookies');
            if (cookies) {
                this.cookies = cookies;
            }
        }
        catch (error) {
            console.log('No Augment cookies found');
        }
    }
    async setAuthToken(token) {
        this.authToken = token;
        // Store in temporary storage and try VSCode configuration
        this.tempStorage.set('authToken', token);
        try {
            await vscode.workspace.getConfiguration().update('augment.authToken', token, vscode.ConfigurationTarget.Global);
        }
        catch (error) {
            console.warn('Failed to save auth token to configuration, using temporary storage:', error);
        }
    }
    async setCookies(cookies) {
        this.cookies = cookies;
        // Store in temporary storage and try VSCode configuration
        this.tempStorage.set('cookies', cookies);
        try {
            await vscode.workspace.getConfiguration().update('augment.cookies', cookies, vscode.ConfigurationTarget.Global);
        }
        catch (error) {
            console.warn('Failed to save cookies to configuration, using temporary storage:', error);
        }
    }
    async makeRequest(endpoint, options = {}) {
        try {
            const url = `${this.API_BASE_URL}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            if (this.authToken) {
                headers['Authorization'] = `Bearer ${this.authToken}`;
            }
            // 如果有cookies，也添加到请求中
            if (this.cookies) {
                headers['Cookie'] = this.cookies;
                headers['Referer'] = this.WEB_BASE_URL;
                headers['Origin'] = this.WEB_BASE_URL;
            }
            // 详细日志记录
            console.log(`🌐 Augment API Request:`, {
                url,
                method: options.method || 'GET',
                headers: { ...headers, Authorization: this.authToken ? 'Bearer [HIDDEN]' : 'None' },
                timestamp: new Date().toISOString()
            });
            const startTime = Date.now();
            const response = await fetch(url, {
                ...options,
                headers
            });
            const duration = Date.now() - startTime;
            console.log(`📡 Augment API Response:`, {
                url,
                status: response.status,
                statusText: response.statusText,
                duration: `${duration}ms`,
                headers: Object.fromEntries(response.headers.entries()),
                timestamp: new Date().toISOString()
            });
            if (response.status === 401) {
                console.warn('🔒 Authentication failed - token may be invalid or expired');
                return {
                    success: false,
                    error: 'Authentication required. Please provide your Augment auth token.'
                };
            }
            if (!response.ok) {
                console.error(`❌ API request failed:`, {
                    status: response.status,
                    statusText: response.statusText,
                    url
                });
                return {
                    success: false,
                    error: `API request failed: ${response.status} ${response.statusText}`
                };
            }
            const data = await response.json();
            console.log(`✅ API request successful:`, {
                url,
                dataKeys: Object.keys(data),
                dataSize: JSON.stringify(data).length,
                timestamp: new Date().toISOString()
            });
            return {
                success: true,
                data
            };
        }
        catch (error) {
            console.error(`🚨 Network error:`, {
                endpoint,
                error: error?.message || 'Unknown error',
                stack: error?.stack || 'No stack trace',
                timestamp: new Date().toISOString()
            });
            return {
                success: false,
                error: `Network error: ${error?.message || error}`
            };
        }
    }
    async checkHealth() {
        try {
            // 使用真实的用户API端点作为健康检查
            const response = await this.makeRequest('/user');
            if (response.success) {
                return {
                    success: true,
                    data: { status: 'healthy', userConnected: true }
                };
            }
            return {
                success: false,
                error: 'API health check failed'
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Health check failed: ${error}`
            };
        }
    }
    async getUserInfo() {
        return await this.makeRequest('/user');
    }
    async getUsageData() {
        return await this.makeRequest('/credits');
    }
    async getSubscriptionInfo() {
        return await this.makeRequest('/subscription');
    }
    async getCreditsInfo() {
        return await this.makeRequest('/credits');
    }
    async getPlansInfo() {
        return await this.makeRequest('/plans');
    }
    async parseUsageResponse(response) {
        if (!response.success || !response.data) {
            return null;
        }
        try {
            const data = response.data;
            // 基于HAR文件分析的真实数据结构
            if (data.usageUnitsUsedThisBillingCycle !== undefined) {
                // Credits API 响应格式
                return {
                    totalUsage: data.usageUnitsUsedThisBillingCycle,
                    usageLimit: data.usageUnitsAvailable + data.usageUnitsUsedThisBillingCycle,
                    dailyUsage: data.usageUnitsUsedThisBillingCycle,
                    monthlyUsage: data.usageUnitsUsedThisBillingCycle,
                    lastUpdate: new Date().toISOString(),
                    subscriptionType: 'community',
                    renewalDate: undefined
                };
            }
            // 订阅API响应格式
            if (data.creditsRenewingEachBillingCycle !== undefined) {
                return {
                    totalUsage: data.creditsIncludedThisBillingCycle - data.creditsRenewingEachBillingCycle,
                    usageLimit: data.creditsIncludedThisBillingCycle,
                    dailyUsage: undefined,
                    monthlyUsage: data.creditsIncludedThisBillingCycle - data.creditsRenewingEachBillingCycle,
                    lastUpdate: new Date().toISOString(),
                    subscriptionType: data.augmentPlanType || data.planName,
                    renewalDate: data.billingPeriodEnd
                };
            }
            // 回退到通用格式
            return {
                totalUsage: data.totalUsage || data.usage || data.count || 0,
                usageLimit: data.limit || data.quota || data.maxUsage || 1000,
                dailyUsage: data.dailyUsage || data.today,
                monthlyUsage: data.monthlyUsage || data.thisMonth,
                lastUpdate: data.lastUpdate || data.updatedAt || new Date().toISOString(),
                subscriptionType: data.plan || data.tier || data.subscriptionType,
                renewalDate: data.renewalDate || data.nextBilling
            };
        }
        catch (error) {
            console.error('Error parsing usage response:', error);
            return null;
        }
    }
    async promptForAuthToken() {
        const token = await vscode.window.showInputBox({
            prompt: 'Enter your Augment authentication token',
            placeHolder: 'Bearer token from Augment dashboard',
            password: true,
            ignoreFocusOut: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Token cannot be empty';
                }
                return null;
            }
        });
        if (token) {
            await this.setAuthToken(token.trim());
            return true;
        }
        return false;
    }
    async promptForCookies() {
        const cookies = await vscode.window.showInputBox({
            prompt: 'Enter your Augment session cookies',
            placeHolder: 'Copy cookies from browser after logging into app.augmentcode.com',
            password: true,
            ignoreFocusOut: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Cookies cannot be empty';
                }
                if (!value.includes('_session=')) {
                    return 'Invalid cookies format - should contain _session=';
                }
                return null;
            }
        });
        if (cookies) {
            await this.setCookies(cookies.trim());
            return true;
        }
        return false;
    }
    async testConnection() {
        // First check health
        const healthCheck = await this.checkHealth();
        if (!healthCheck.success) {
            return healthCheck;
        }
        // Then try to get user info to test auth
        const userInfo = await this.getUserInfo();
        return userInfo;
    }
    hasAuthToken() {
        return this.authToken !== null && this.authToken.length > 0;
    }
    hasCookies() {
        return this.cookies !== null && this.cookies.length > 0;
    }
    hasAnyAuth() {
        return this.hasAuthToken() || this.hasCookies();
    }
    clearAuthToken() {
        this.authToken = null;
        try {
            vscode.workspace.getConfiguration().update('augment.authToken', undefined, vscode.ConfigurationTarget.Global);
        }
        catch (error) {
            console.warn('Failed to clear auth token from configuration:', error);
        }
    }
    clearCookies() {
        this.cookies = null;
        try {
            vscode.workspace.getConfiguration().update('augment.cookies', undefined, vscode.ConfigurationTarget.Global);
        }
        catch (error) {
            console.warn('Failed to clear cookies from configuration:', error);
        }
    }
    clearAllAuth() {
        this.clearAuthToken();
        this.clearCookies();
    }
}
exports.AugmentApiClient = AugmentApiClient;
//# sourceMappingURL=augmentApi.js.map
{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA+C;AAC/C,iDAA8C;AAC9C,uCAA2C;AAC3C,uDAAoD;AACpD,qCAAyC;AAEzC,IAAI,gBAAkC,CAAC;AACvC,IAAI,YAA0B,CAAC;AAC/B,IAAI,cAA8B,CAAC;AACnC,IAAI,eAAgC,CAAC;AACrC,IAAI,aAA4B,CAAC;AAEjC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEpD,sBAAsB;IACtB,cAAc,GAAG,IAAI,wBAAc,CAAC,OAAO,CAAC,CAAC;IAC7C,aAAa,GAAG,IAAI,sBAAa,EAAE,CAAC;IACpC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;IACxC,YAAY,GAAG,IAAI,2BAAY,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IAC/D,gBAAgB,GAAG,IAAI,4BAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IAErE,oBAAoB;IACpB,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACxF,YAAY,CAAC,UAAU,EAAE,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC5F,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,gBAAgB,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1F,gBAAgB,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,iBAAiB;IACjB,YAAY,CAAC,aAAa,EAAE,CAAC;IAC7B,gBAAgB,CAAC,IAAI,EAAE,CAAC;IAExB,2BAA2B;IAC3B,eAAe,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QACrD,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACvC,gDAAgD;SACnD;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;SACrE;IACL,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,CACf,CAAC;IAEF,mCAAmC;IACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;QAC9C,IAAI,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;YAC9C,aAAa,CAAC,YAAY,EAAE,CAAC;YAC7B,gBAAgB,CAAC,aAAa,EAAE,CAAC;SACpC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAtDD,4BAsDC;AAED,SAAS,gBAAgB;IACrB,MAAM,KAAK,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;IAC7C,MAAM,KAAK,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;IAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;IAErD,MAAM,OAAO,GAAG;;mBAED,KAAK;mBACL,KAAK;sBACF,UAAU;eACjB,KAAK,GAAG,KAAK;;cAEd,YAAY,CAAC,gBAAgB,EAAE;KACxC,CAAC,IAAI,EAAE,CAAC;IAET,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC3F,IAAI,SAAS,KAAK,aAAa,EAAE;YAC7B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;SAC/D;aAAM,IAAI,SAAS,KAAK,eAAe,EAAE;YACtC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;SACjE;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,gBAAgB,EAAE;QAClB,gBAAgB,CAAC,OAAO,EAAE,CAAC;KAC9B;IACD,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;AACL,CAAC;AAPD,gCAOC"}
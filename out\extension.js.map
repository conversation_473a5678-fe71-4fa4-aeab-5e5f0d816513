{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA+C;AAC/C,iDAA8C;AAC9C,uCAA2C;AAC3C,uDAAoD;AACpD,qCAAyC;AAEzC,IAAI,gBAAkC,CAAC;AACvC,IAAI,YAA0B,CAAC;AAC/B,IAAI,cAA8B,CAAC;AACnC,IAAI,eAAgC,CAAC;AACrC,IAAI,aAA4B,CAAC;AAEjC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEpD,sBAAsB;IACtB,cAAc,GAAG,IAAI,wBAAc,CAAC,OAAO,CAAC,CAAC;IAC7C,aAAa,GAAG,IAAI,sBAAa,EAAE,CAAC;IACpC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;IACxC,YAAY,GAAG,IAAI,2BAAY,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;IAC/D,gBAAgB,GAAG,IAAI,4BAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IAErE,oBAAoB;IACpB,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACxF,YAAY,CAAC,UAAU,EAAE,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC5F,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,gBAAgB,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1F,gBAAgB,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC9F,YAAY,CAAC,qBAAqB,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QACpG,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,iBAAiB,EAAE,CAAC;QAC1D,IAAI,OAAO,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4CAA4C,CAAC,CAAC;YACnF,kCAAkC;YAClC,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACxD,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,EAAE;gBACxC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aACrD;SACJ;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QAC5G,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,iBAAiB,EAAE,CAAC;QACzD,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC;SAChF;aAAM;YACH,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ;gBAC3B,CAAC,CAAC,4BAA4B,MAAM,CAAC,KAAK,EAAE;gBAC5C,CAAC,CAAC,iEAAiE,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAC3C;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC9F,eAAe,CAAC,aAAa,EAAE,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;QACnE,sCAAsC;QACtC,gBAAgB,CAAC,aAAa,EAAE,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QACpG,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;YAC7C,EAAE,KAAK,EAAE,iBAAiB,EAAE,WAAW,EAAE,6BAA6B,EAAE,KAAK,EAAE,QAAQ,EAAE;YACzF,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,8BAA8B,EAAE,KAAK,EAAE,MAAM,EAAE;YACrF,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,sBAAsB,EAAE,KAAK,EAAE,OAAO,EAAE;YAC/E,EAAE,KAAK,EAAE,iBAAiB,EAAE,WAAW,EAAE,8BAA8B,EAAE,KAAK,EAAE,cAAc,EAAE;SACnG,EAAE;YACC,WAAW,EAAE,6BAA6B;YAC1C,cAAc,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,MAAM,CAAC,KAAK,0CAA0C,CAAC,CAAC;QAC3G,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAEzD,IAAI;YACA,MAAM,SAAS,GAAI,eAAuB,CAAC,SAAS,CAAC;YACrD,IAAI,MAAM,CAAC;YAEX,QAAQ,MAAM,CAAC,KAAK,EAAE;gBAClB,KAAK,QAAQ;oBACT,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC;oBACvC,MAAM;gBACV,KAAK,MAAM;oBACP,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC;oBACvC,MAAM;gBACV,KAAK,OAAO;oBACR,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,EAAE,CAAC;oBACxC,MAAM;gBACV,KAAK,cAAc;oBACf,MAAM,GAAG,MAAM,SAAS,CAAC,mBAAmB,EAAE,CAAC;oBAC/C,MAAM;aACb;YAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAExC,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,MAAM,CAAC,KAAK,yCAAyC,CAAC,CAAC;aACpG;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,MAAM,CAAC,KAAK,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;aAC/E;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;SAC9D;QAED,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,iBAAiB;IACjB,YAAY,CAAC,aAAa,EAAE,CAAC;IAC7B,gBAAgB,CAAC,IAAI,EAAE,CAAC;IAExB,mDAAmD;IACnD,eAAe,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAC7C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QAEvC,IAAI,MAAM,CAAC,SAAS,EAAE;YAClB,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,IAAI,MAAM,CAAC,WAAW,EAAE;oBACpB,OAAO,CAAC,GAAG,CAAC,2CAA2C,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBACpF,mDAAmD;oBACnD,IAAI,MAAM,CAAC,SAAS,EAAE;wBAClB,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;qBACrD;iBACJ;qBAAM;oBACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;iBACrE;aACJ;iBAAM;gBACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;aAC1D;SACJ;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;SACrE;QAED,0CAA0C;QAC1C,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,oBAAoB,EACpB,wBAAwB,EACxB,oBAAoB,EACpB,oBAAoB,EACpB,gBAAgB,EAChB,YAAY,CACf,CAAC;IAEF,mCAAmC;IACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;QAC9C,IAAI,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;YAC9C,aAAa,CAAC,YAAY,EAAE,CAAC;YAC7B,gBAAgB,CAAC,aAAa,EAAE,CAAC;SACpC;QAED,yCAAyC;QACzC,IAAI,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACvC,2BAA2B;YAC3B,eAAe,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC7C,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,EAAE;oBACxC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;iBACrD;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,MAAM,mBAAmB,GAAG,eAAe,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;QACtE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;QAC9C,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAE7C,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,EAAE;YACxC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACrD;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACpD,CAAC;AA1LD,4BA0LC;AAED,SAAS,gBAAgB;IACrB,MAAM,KAAK,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;IAC7C,MAAM,KAAK,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;IAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;IAErD,MAAM,OAAO,GAAG;;mBAED,KAAK;mBACL,KAAK;sBACF,UAAU;eACjB,KAAK,GAAG,KAAK;;cAEd,YAAY,CAAC,gBAAgB,EAAE;KACxC,CAAC,IAAI,EAAE,CAAC;IAET,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC3F,IAAI,SAAS,KAAK,aAAa,EAAE;YAC7B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;SAC/D;aAAM,IAAI,SAAS,KAAK,eAAe,EAAE;YACtC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;SACjE;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,gBAAgB,EAAE;QAClB,gBAAgB,CAAC,OAAO,EAAE,CAAC;KAC9B;IACD,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;AACL,CAAC;AAPD,gCAOC"}
import * as vscode from 'vscode';

export interface HttpOnlyCookieResult {
    success: boolean;
    cookies?: string;
    error?: string;
}

export class HttpOnlyCookieHandler {
    
    /**
     * 处理HttpOnly cookies的获取
     * 提供多种方法来获取无法通过JavaScript访问的HttpOnly cookies
     */
    async extractHttpOnlyCookies(): Promise<HttpOnlyCookieResult> {
        const method = await this.selectExtractionMethod();
        
        switch (method) {
            case 'devtools':
                return await this.extractFromDevTools();
            case 'network':
                return await this.extractFromNetworkTab();
            case 'manual':
                return await this.manualCookieInput();
            case 'bookmarklet':
                return await this.useBookmarkletMethod();
            default:
                return { success: false, error: 'No extraction method selected' };
        }
    }

    private async selectExtractionMethod(): Promise<string | undefined> {
        const options = [
            {
                label: '📋 开发者工具 - Application标签页',
                detail: '推荐：直接从浏览器存储中复制cookie值',
                value: 'devtools'
            },
            {
                label: '🔧 开发者工具 - Network标签页',
                detail: '从网络请求头中获取cookie',
                value: 'network'
            },
            {
                label: '📝 手动输入',
                detail: '直接粘贴cookie字符串',
                value: 'manual'
            },
            {
                label: '🔖 书签工具',
                detail: '使用特殊书签提取cookie（实验性）',
                value: 'bookmarklet'
            }
        ];

        const selected = await vscode.window.showQuickPick(options, {
            placeHolder: '选择HttpOnly Cookie提取方法',
            title: '🍪 HttpOnly Cookie提取器'
        });

        return selected?.value;
    }

    private async extractFromDevTools(): Promise<HttpOnlyCookieResult> {
        const instructions = `
🍪 从开发者工具Application标签页提取HttpOnly Cookie

📋 详细步骤：

1. 🌐 打开浏览器，访问 https://app.augmentcode.com
2. 🔐 确保您已经登录到Augment
3. 🛠️ 按F12打开开发者工具
4. 📱 切换到"Application"标签页（Chrome）或"Storage"标签页（Firefox）
5. 📂 在左侧面板中展开"Cookies"
6. 🌐 点击"https://app.augmentcode.com"
7. 🔍 在右侧找到名为"_session"的cookie
8. 📋 双击"Value"列中的值，全选并复制
9. ✅ 点击下面的"粘贴Cookie"按钮

⚠️ 重要提示：
- 如果看不到_session cookie，请确保已登录
- HttpOnly cookie不会显示在document.cookie中
- 只需要复制Value值，不需要整个cookie字符串
        `;

        // 显示指导文档
        const doc = await vscode.workspace.openTextDocument({
            content: instructions,
            language: 'markdown'
        });
        await vscode.window.showTextDocument(doc);

        const action = await vscode.window.showInformationMessage(
            '📋 请按照指导文档操作，然后粘贴_session cookie的Value值',
            '✅ 粘贴Cookie',
            '❌ 取消'
        );

        if (action === '✅ 粘贴Cookie') {
            return await this.promptForCookieValue();
        }

        return { success: false, error: 'User cancelled devtools extraction' };
    }

    private async extractFromNetworkTab(): Promise<HttpOnlyCookieResult> {
        const instructions = `
🔧 从Network标签页提取HttpOnly Cookie

📋 详细步骤：

1. 🌐 打开浏览器，访问 https://app.augmentcode.com
2. 🔐 确保您已经登录到Augment
3. 🛠️ 按F12打开开发者工具
4. 🌐 切换到"Network"标签页
5. 🔄 刷新页面或访问任意API（如/api/user）
6. 📋 点击任意请求（推荐选择/api/user）
7. 📝 在右侧面板中找到"Request Headers"
8. 🍪 找到"Cookie"行，复制_session=xxx部分
9. ✅ 点击下面的"粘贴Cookie"按钮

📋 Cookie格式示例：
_session=eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMjM0NX0.abc123

⚠️ 重要提示：
- 只需要_session=xxx部分
- 如果有多个cookie，用分号分隔
- 确保复制完整的session值
        `;

        // 显示指导文档
        const doc = await vscode.workspace.openTextDocument({
            content: instructions,
            language: 'markdown'
        });
        await vscode.window.showTextDocument(doc);

        const action = await vscode.window.showInformationMessage(
            '🔧 请按照指导文档从Network标签页获取cookie',
            '✅ 粘贴Cookie',
            '❌ 取消'
        );

        if (action === '✅ 粘贴Cookie') {
            return await this.promptForCookieValue();
        }

        return { success: false, error: 'User cancelled network extraction' };
    }

    private async manualCookieInput(): Promise<HttpOnlyCookieResult> {
        return await this.promptForCookieValue();
    }

    private async useBookmarkletMethod(): Promise<HttpOnlyCookieResult> {
        const bookmarkletCode = `
javascript:(function(){
    // 尝试从各种来源获取cookie信息
    var info = [];
    
    // 1. 尝试获取非HttpOnly cookies
    if (document.cookie) {
        info.push('📋 非HttpOnly Cookies:');
        info.push(document.cookie);
        info.push('');
    }
    
    // 2. 提示用户手动获取HttpOnly cookies
    info.push('🍪 HttpOnly Cookie获取指南:');
    info.push('1. 按F12打开开发者工具');
    info.push('2. Application → Cookies → ' + location.hostname);
    info.push('3. 复制_session cookie的Value值');
    info.push('');
    info.push('🔧 或者从Network标签页:');
    info.push('1. Network标签页 → 刷新页面');
    info.push('2. 点击任意请求 → Request Headers');
    info.push('3. 复制Cookie中的_session=xxx部分');
    
    // 显示信息
    alert(info.join('\\n'));
    
    // 尝试复制到剪贴板
    if (navigator.clipboard && document.cookie) {
        navigator.clipboard.writeText(document.cookie).then(() => {
            console.log('✅ 非HttpOnly cookies已复制到剪贴板');
        }).catch(() => {
            console.log('⚠️ 无法复制到剪贴板');
        });
    }
})();
        `;

        const instructions = `
🔖 书签工具方法（实验性）

📋 使用步骤：

1. 📋 复制下面的书签代码
2. 🔖 在浏览器中创建新书签
3. 📝 将代码粘贴为书签的URL
4. 🌐 在app.augmentcode.com页面点击书签
5. 📋 按照弹出的指导获取HttpOnly cookie

📋 书签代码：
${bookmarkletCode}

⚠️ 注意：
- 此方法仍需要手动从开发者工具获取HttpOnly cookie
- 主要用于提供指导和复制非HttpOnly cookies
        `;

        // 显示指导文档
        const doc = await vscode.workspace.openTextDocument({
            content: instructions,
            language: 'javascript'
        });
        await vscode.window.showTextDocument(doc);

        const action = await vscode.window.showInformationMessage(
            '🔖 请创建书签并在Augment页面使用，然后粘贴获取的cookie',
            '✅ 粘贴Cookie',
            '❌ 取消'
        );

        if (action === '✅ 粘贴Cookie') {
            return await this.promptForCookieValue();
        }

        return { success: false, error: 'User cancelled bookmarklet method' };
    }

    private async promptForCookieValue(): Promise<HttpOnlyCookieResult> {
        const cookieValue = await vscode.window.showInputBox({
            prompt: '🍪 请粘贴_session cookie的值',
            placeHolder: '例如：_session=eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMjM0NX0.abc123 或者只粘贴Value部分',
            password: true,
            ignoreFocusOut: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Cookie值不能为空';
                }
                
                // 检查是否包含_session
                if (!value.includes('_session=') && !value.startsWith('eyJ')) {
                    return '请确保包含_session cookie或其Value值';
                }
                
                return null;
            }
        });

        if (cookieValue) {
            // 标准化cookie格式
            let normalizedCookie = cookieValue.trim();
            
            // 如果只是Value值，添加_session=前缀
            if (!normalizedCookie.includes('_session=') && normalizedCookie.startsWith('eyJ')) {
                normalizedCookie = `_session=${normalizedCookie}`;
            }
            
            return {
                success: true,
                cookies: normalizedCookie
            };
        }

        return { success: false, error: 'No cookie value provided' };
    }

    /**
     * 验证HttpOnly cookie是否有效
     */
    async validateHttpOnlyCookie(cookies: string): Promise<boolean> {
        // 基本格式验证
        if (!cookies.includes('_session=')) {
            return false;
        }

        // 检查session值的基本格式（通常是JWT或类似格式）
        const sessionMatch = cookies.match(/_session=([^;]+)/);
        if (!sessionMatch) {
            return false;
        }

        const sessionValue = sessionMatch[1];
        
        // 基本长度检查（session值通常较长）
        if (sessionValue.length < 20) {
            return false;
        }

        return true;
    }
}

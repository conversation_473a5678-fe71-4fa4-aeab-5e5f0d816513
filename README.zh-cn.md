# Augment 使用量追踪器

一个在VSCode状态栏显示Augment AI使用统计的扩展插件。

## 🚀 功能特性

- **状态栏显示**: 显示使用量如 "Augment: 123/1000 ●"
- **实时API集成**: 从Augment API获取真实使用数据
- **5秒自动刷新**: 每5秒更新一次使用数据
- **多种认证方式**: 支持API令牌或浏览器Cookie
- **一键登录**: 自动化浏览器登录和Cookie提取
- **手动刷新**: 立即获取最新数据
- **多语言支持**: 支持中文和英文界面

## 📦 快速开始

### 安装插件
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

### 配置认证
1. 运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 在浏览器中登录并按照Cookie提取指南操作
3. 在状态栏查看真实使用数据

## 🔧 主要命令

| 命令 | 功能 |
|------|------|
| 🌐 网页自动登录 | 自动化登录和设置 |
| 设置浏览器Cookie | 手动配置Cookie |
| 检查认证状态 | 验证设置状态 |
| 显示使用详情 | 显示详细统计信息 |
| 重置使用统计 | 重置本地数据 |
| 🔄 手动刷新 | 立即刷新数据 |
| 🌐 设置语言 | 切换界面语言 |

## 📊 状态栏说明

```
$(pulse) Augment: 7/56 ● (12%)
```

- **7/56**: 当前使用量 / 总限额
- **●**: 真实数据指示器（○ 表示模拟数据）
- **(12%)**: 使用百分比
- **点击**: 打开Augment网站
- **悬停**: 查看详细信息

## ⚙️ 配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `augmentTracker.enabled` | `true` | 启用/禁用追踪器 |
| `augmentTracker.refreshInterval` | `5` | 刷新间隔（秒） |
| `augmentTracker.showInStatusBar` | `true` | 在状态栏显示 |
| `augmentTracker.language` | `"auto"` | 界面语言 |
| `augment.cookies` | `""` | 浏览器会话Cookie |

### 语言设置选项
- `"auto"`: 自动（跟随VSCode语言设置）
- `"en"`: 英文
- `"zh-cn"`: 简体中文

## 🔐 认证配置

### 方法1：网页自动登录（推荐）
1. 运行 `Ctrl+Shift+P` → "🌐 网页自动登录"
2. 在打开的浏览器中登录Augment
3. 按照提示完成Cookie提取

### 方法2：手动Cookie设置
1. 登录到 https://app.augmentcode.com
2. 复制浏览器Cookie
3. 运行 `Ctrl+Shift+P` → "设置浏览器Cookie"
4. 粘贴Cookie

## 🔄 手动刷新功能

### 使用场景
- 刚完成Augment操作，想立即查看使用量变化
- 自动刷新出现问题时，手动触发检查
- 验证API连接和数据准确性

### 使用方法
```
Ctrl+Shift+P → "🔄 手动刷新"
```

### 预期结果
- **成功**: "数据刷新成功: 7/56 积分"
- **失败**: "数据刷新失败: [错误信息]"
- **无认证**: "未配置认证信息，无法刷新"

## 🌐 语言设置

### 支持的语言
- **自动**: 根据VSCode语言设置自动选择
- **English**: 英文界面
- **简体中文**: 中文界面

### 设置方法
1. 运行 `Ctrl+Shift+P` → "🌐 设置语言"
2. 选择所需语言（当前语言显示✓）
3. 建议重启VSCode以完全应用更改

## 📈 真实数据集成

插件直接集成Augment的API端点：

- **Credits API**: `/api/credits` - 获取真实使用统计
- **用户API**: `/api/user` - 账户信息
- **订阅API**: `/api/subscription` - 计划和计费信息

### API响应示例
```json
{
  "usageUnitsAvailable": 49,
  "usageUnitsUsedThisBillingCycle": 7,
  "usageUnitsPending": 0
}
```

## 🔍 故障排除

### 没有显示真实数据
1. 检查认证状态：`Ctrl+Shift+P` → "检查认证状态"
2. 验证Cookie是否过期
3. 测试API连接：`Ctrl+Shift+P` → "🔄 手动刷新"

### 自动刷新不工作
1. 检查刷新间隔设置
2. 验证插件是否启用
3. 查看开发者控制台错误信息（F12）

### 认证错误
1. 重新登录Augment网站
2. 获取新的Cookie或令牌
3. 清除旧的认证数据

## 📝 控制台日志

插件提供详细的控制台日志：

```
🔄 Starting real data refresh with 5s interval
📊 Credits API Response: { ... }
✅ Parsed usage data: { ... }
```

启用开发者工具（F12）查看详细的API交互信息。

## 🛡️ 隐私安全

- 所有认证数据存储在本地
- 不向第三方发送任何数据
- 用户完全控制凭据
- 安全的Cookie和令牌处理

## 🔄 更新和维护

插件每5秒自动刷新数据并提供：
- 实时使用统计
- 自动认证验证
- 智能回退到模拟模式
- 全面的错误处理

## 📞 支持和反馈

如果您遇到任何问题或有疑问：

- 📝 **问题反馈**: [GitHub Issues](https://github.com/augment-tracker/vscode-extension/issues)
- 📧 **邮箱联系**: <EMAIL>
- 📖 **文档**: [中文文档](README.zh-cn.md) | [English Docs](README.en.md)

## 📄 许可证

本项目采用MIT许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

### MIT许可证摘要
- ✅ **商业使用**: 允许
- ✅ **修改**: 允许  
- ✅ **分发**: 允许
- ✅ **私人使用**: 允许
- ❌ **责任**: 不承担
- ❌ **保证**: 不提供

## 🤝 贡献

欢迎贡献！请随时提交Pull Request。

### 贡献流程
1. Fork项目
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 创建Pull Request

---

**享受在VSCode中实时监控Augment使用情况！** 🚀

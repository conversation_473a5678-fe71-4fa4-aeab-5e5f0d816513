# 命令面板国际化测试指南

## 🚨 问题描述

用户反馈：设置语言后，命令面板中的命令标题仍然显示英文，没有根据语言设置变化。

## 🔧 修复内容

### 1. package.json修复
**修复前**:
```json
{
  "command": "augmentTracker.webLogin",
  "title": "🌐 Web Login (Auto)",
  "category": "Augment Tracker"
}
```

**修复后**:
```json
{
  "command": "augmentTracker.webLogin", 
  "title": "%command.webLogin%",
  "category": "Augment Tracker"
}
```

### 2. 语言包完善
**英文语言包 (package.nls.json)**:
```json
{
  "command.webLogin": "🌐 Web Login (Auto)",
  "command.showDetails": "Show Usage Details",
  "command.manualRefresh": "🔄 Manual Refresh",
  "command.setLanguage": "🌐 Set Language",
  "command.logout": "🚪 Logout"
}
```

**中文语言包 (package.nls.zh-cn.json)**:
```json
{
  "command.webLogin": "🌐 网页自动登录",
  "command.showDetails": "显示使用详情", 
  "command.manualRefresh": "🔄 手动刷新",
  "command.setLanguage": "🌐 设置语言",
  "command.logout": "🚪 退出登录"
}
```

### 3. 设置语言命令增强
- 明确提示用户需要重启VSCode
- 区分立即生效的界面元素和需要重启的命令面板
- 提供"立即重启"和"稍后重启"选项

## 🧪 测试步骤

### 测试1: 英文环境测试
1. **设置VSCode为英文**
   - File → Preferences → Settings
   - 搜索 "locale"
   - 设置为 "en"

2. **测试命令面板**
   - 按 `Ctrl+Shift+P`
   - 输入 "augment"
   - 验证所有命令显示为英文

3. **预期结果**:
   ```
   🌐 Web Login (Auto)
   Show Usage Details
   🔄 Manual Refresh
   🌐 Set Language
   🚪 Logout
   ```

### 测试2: 中文环境测试
1. **设置插件语言为中文**
   - `Ctrl+Shift+P` → "🌐 Set Language"
   - 选择 "🇨🇳 中文"
   - 点击 "立即重启VSCode"

2. **重启后测试**
   - 按 `Ctrl+Shift+P`
   - 输入 "augment"
   - 验证所有命令显示为中文

3. **预期结果**:
   ```
   🌐 网页自动登录
   显示使用详情
   🔄 手动刷新
   🌐 设置语言
   🚪 退出登录
   ```

### 测试3: 语言切换测试
1. **从中文切换到英文**
   - `Ctrl+Shift+P` → "🌐 设置语言"
   - 选择 "🇺🇸 English"
   - 点击 "立即重启VSCode"

2. **验证切换效果**
   - 重启后命令面板应显示英文
   - 状态栏立即显示英文

3. **从英文切换到中文**
   - 重复上述步骤，验证反向切换

## 🔍 故障排除

### 问题1: 命令面板仍显示英文
**可能原因**:
- 没有重启VSCode
- 语言包文件缺失
- package.json中仍使用硬编码标题

**解决方案**:
1. 确保重启VSCode
2. 检查 package.nls.zh-cn.json 文件存在
3. 验证 package.json 使用占位符

### 问题2: 部分命令显示英文，部分显示中文
**可能原因**:
- 语言包不完整
- 占位符名称不匹配

**解决方案**:
1. 检查语言包中所有命令都有对应翻译
2. 确保占位符名称一致

### 问题3: 设置语言后没有提示重启
**可能原因**:
- setLanguage命令没有更新

**解决方案**:
1. 重新编译插件
2. 检查 extension.ts 中的提示逻辑

## 📊 测试检查清单

### 基础功能测试
- [ ] 英文环境下命令面板显示英文
- [ ] 中文环境下命令面板显示中文
- [ ] 语言切换功能正常工作
- [ ] 重启VSCode后语言生效
- [ ] 状态栏语言立即更新

### 命令完整性测试
- [ ] "🌐 网页自动登录" / "🌐 Web Login (Auto)"
- [ ] "显示使用详情" / "Show Usage Details"
- [ ] "🔄 手动刷新" / "🔄 Manual Refresh"
- [ ] "🌐 设置语言" / "🌐 Set Language"
- [ ] "🍪 检查Cookie状态" / "🍪 Check Cookie Status"
- [ ] "🔄 刷新Cookie" / "🔄 Refresh Cookie"
- [ ] "🚪 退出登录" / "🚪 Logout"

### 用户体验测试
- [ ] 设置语言时有明确的重启提示
- [ ] 提供"立即重启"和"稍后重启"选项
- [ ] 重启后所有界面元素语言一致
- [ ] 语言设置持久化保存

## 🚀 发布验证

### 发布前检查
1. **文件完整性**
   - [ ] package.json 使用占位符
   - [ ] package.nls.json 英文完整
   - [ ] package.nls.zh-cn.json 中文完整
   - [ ] extension.ts 包含重启提示

2. **编译测试**
   - [ ] TypeScript编译无错误
   - [ ] 打包成功生成.vsix文件
   - [ ] 本地安装测试正常

3. **功能验证**
   - [ ] 所有命令在两种语言下都正常工作
   - [ ] 语言切换流程顺畅
   - [ ] 用户提示清晰明确

### 发布后验证
1. **商店更新**
   - [ ] 插件版本已更新
   - [ ] 用户可以正常下载安装
   - [ ] 更新说明包含国际化修复

2. **用户反馈**
   - [ ] 监控用户反馈
   - [ ] 收集语言切换使用情况
   - [ ] 记录任何新的问题报告

## 📝 发布说明

### 更新内容
- ✅ 修复命令面板国际化问题
- ✅ 支持命令标题根据语言设置动态显示
- ✅ 增强语言切换用户体验
- ✅ 明确重启VSCode的必要性提示

### 用户指南
1. 使用 `Ctrl+Shift+P` → "🌐 设置语言" 切换界面语言
2. 选择所需语言后，点击"立即重启VSCode"
3. 重启后命令面板将显示对应语言的命令标题
4. 状态栏和其他界面元素会立即更新

---

## 🎯 总结

这次修复解决了VSCode插件命令面板国际化的核心问题：

1. **技术层面**: 将硬编码的命令标题改为占位符引用
2. **用户体验**: 明确告知用户重启的必要性
3. **功能完整**: 确保所有命令都支持中英文显示
4. **测试覆盖**: 提供完整的测试指南和检查清单

**修复后，用户设置语言并重启VSCode后，命令面板中的所有Augment相关命令都会显示为对应的语言！** 🎉

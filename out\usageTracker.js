"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageTracker = void 0;
const vscode = __importStar(require("vscode"));
class UsageTracker {
    constructor(storageManager, configManager) {
        this.disposables = [];
        this.currentUsage = 0;
        this.lastResetDate = '';
        this.hasRealData = false;
        this.realDataSource = 'simulation';
        this.storageManager = storageManager;
        this.configManager = configManager;
        this.loadCurrentUsage();
    }
    async loadCurrentUsage() {
        const data = await this.storageManager.getUsageData();
        this.currentUsage = data.totalUsage;
        this.lastResetDate = data.lastResetDate;
    }
    startTracking() {
        if (!this.configManager.isEnabled()) {
            return;
        }
        // Track text document changes (simulating AI assistance usage)
        const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument(event => {
            if (event.contentChanges.length > 0) {
                this.trackUsage('textChange');
            }
        });
        // Track when documents are saved (potential AI-assisted completion)
        const onDidSaveTextDocument = vscode.workspace.onDidSaveTextDocument(() => {
            this.trackUsage('documentSave');
        });
        // Track completion item selections and other editor events
        const onDidChangeSelection = vscode.window.onDidChangeTextEditorSelection(event => {
            if (event.selections.length > 0) {
                this.trackUsage('selection', 0.05); // Very small increment for selections
            }
        });
        // Track completion item selections (simulating AI completions)
        // Note: This is a simplified simulation since we can't directly access completion providers
        const onDidChangeActiveTextEditor = vscode.window.onDidChangeActiveTextEditor(() => {
            this.trackUsage('editorChange', 0.1); // Small increment for editor changes
        });
        this.disposables.push(onDidChangeTextDocument, onDidSaveTextDocument, onDidChangeSelection, onDidChangeActiveTextEditor);
        // Periodic cleanup of old data
        setInterval(() => {
            this.storageManager.cleanOldData();
        }, 24 * 60 * 60 * 1000); // Daily cleanup
    }
    async trackUsage(type, increment = 1) {
        if (!this.configManager.isEnabled()) {
            return;
        }
        // Apply different weights based on action type
        let actualIncrement = increment;
        switch (type) {
            case 'textChange':
                actualIncrement = 0.1; // Small increment for text changes
                break;
            case 'documentSave':
                actualIncrement = 1; // Standard increment for saves
                break;
            case 'selection':
                actualIncrement = increment; // Use provided increment for selections
                break;
            case 'editorChange':
                actualIncrement = increment; // Use provided increment
                break;
            default:
                actualIncrement = increment;
        }
        try {
            const data = await this.storageManager.incrementUsage(actualIncrement);
            this.currentUsage = data.totalUsage;
            // Check if usage limit is reached
            const limit = this.configManager.getUsageLimit();
            if (this.currentUsage >= limit) {
                this.showUsageLimitWarning();
            }
        }
        catch (error) {
            console.error('Error tracking usage:', error);
        }
    }
    showUsageLimitWarning() {
        const limit = this.configManager.getUsageLimit();
        vscode.window.showWarningMessage(`Augment usage limit reached (${limit}). Consider upgrading your plan.`, 'View Details', 'Reset Usage', 'Open Settings').then(selection => {
            switch (selection) {
                case 'View Details':
                    vscode.commands.executeCommand('augmentTracker.showDetails');
                    break;
                case 'Reset Usage':
                    this.resetUsage();
                    break;
                case 'Open Settings':
                    vscode.commands.executeCommand('augmentTracker.openSettings');
                    break;
            }
        });
    }
    async resetUsage() {
        await this.storageManager.resetUsage();
        const data = await this.storageManager.getUsageData();
        this.currentUsage = data.totalUsage;
        this.lastResetDate = data.lastResetDate;
        this.hasRealData = false;
        this.realDataSource = 'simulation';
    }
    getCurrentUsage() {
        return Math.round(this.currentUsage);
    }
    getLastResetDate() {
        return new Date(this.lastResetDate).toLocaleDateString();
    }
    hasRealUsageData() {
        return this.hasRealData;
    }
    getDataSource() {
        return this.realDataSource;
    }
    async updateWithRealData(realData) {
        try {
            if (realData.totalUsage !== undefined) {
                // Update with real total usage
                this.currentUsage = realData.totalUsage;
                this.hasRealData = true;
                this.realDataSource = 'augment_api';
                // Store the real data
                const data = await this.storageManager.getUsageData();
                data.totalUsage = realData.totalUsage;
                if (realData.lastUpdate) {
                    data.lastUpdateDate = realData.lastUpdate;
                }
                await this.storageManager.saveUsageData(data);
                console.log(`Updated with real Augment data: ${realData.totalUsage}`);
            }
            else if (realData.dailyUsage !== undefined) {
                // Update with daily usage increment
                await this.trackUsage('real_data', realData.dailyUsage);
                this.hasRealData = true;
                this.realDataSource = 'augment_daily';
            }
        }
        catch (error) {
            console.error('Error updating with real data:', error);
        }
    }
    async promptUserForRealData() {
        const choice = await vscode.window.showInformationMessage('Would you like to manually input your Augment usage data?', 'Enter Usage', 'Enter Limit', 'Cancel');
        if (choice === 'Enter Usage') {
            const usageInput = await vscode.window.showInputBox({
                prompt: 'Enter your current Augment usage count',
                placeHolder: 'e.g., 150',
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num < 0) {
                        return 'Please enter a valid positive number';
                    }
                    return null;
                }
            });
            if (usageInput) {
                const usage = parseInt(usageInput);
                await this.updateWithRealData({ totalUsage: usage });
                this.realDataSource = 'user_input';
                vscode.window.showInformationMessage(`Usage updated to ${usage}`);
            }
        }
        else if (choice === 'Enter Limit') {
            const limitInput = await vscode.window.showInputBox({
                prompt: 'Enter your Augment usage limit',
                placeHolder: 'e.g., 1000',
                validateInput: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num) || num <= 0) {
                        return 'Please enter a valid positive number';
                    }
                    return null;
                }
            });
            if (limitInput) {
                const limit = parseInt(limitInput);
                await this.configManager.updateConfig('usageLimit', limit);
                vscode.window.showInformationMessage(`Usage limit updated to ${limit}`);
            }
        }
    }
    async getTodayUsage() {
        return await this.storageManager.getTodayUsage();
    }
    async getWeeklyUsage() {
        return await this.storageManager.getWeeklyUsage();
    }
    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.UsageTracker = UsageTracker;
//# sourceMappingURL=usageTracker.js.map
import * as vscode from 'vscode';
import { UsageTracker } from './usageTracker';
import { ConfigManager } from './config';

export class StatusBarManager implements vscode.Disposable {
    private statusBarItem: vscode.StatusBarItem;
    private usageTracker: UsageTracker;
    private configManager: ConfigManager;
    private refreshTimer: NodeJS.Timeout | undefined;

    constructor(usageTracker: UsageTracker, configManager: ConfigManager) {
        this.usageTracker = usageTracker;
        this.configManager = configManager;
        
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            100
        );
        
        this.setupStatusBarItem();
        this.startRefreshTimer();
    }

    private setupStatusBarItem() {
        this.statusBarItem.tooltip = 'Augment Usage Tracker - Click for more options';
        this.updateDisplay();
        
        // Set click command based on configuration
        this.updateClickCommand();
    }

    private updateClickCommand() {
        const clickAction = this.configManager.getClickAction();
        
        switch (clickAction) {
            case 'openWebsite':
                this.statusBarItem.command = {
                    command: 'vscode.open',
                    arguments: [vscode.Uri.parse('https://www.augmentcode.com')],
                    title: 'Open Augment Website'
                };
                break;
            case 'showDetails':
                this.statusBarItem.command = 'augmentTracker.showDetails';
                break;
            case 'openSettings':
                this.statusBarItem.command = 'augmentTracker.openSettings';
                break;
            default:
                this.statusBarItem.command = 'augmentTracker.showDetails';
        }
    }

    updateDisplay() {
        if (!this.configManager.isEnabled() || !this.configManager.shouldShowInStatusBar()) {
            this.statusBarItem.hide();
            return;
        }

        const usage = this.usageTracker.getCurrentUsage();
        const limit = this.configManager.getUsageLimit();
        const percentage = Math.round((usage / limit) * 100);

        // Update text with usage information
        this.statusBarItem.text = `$(pulse) Augment: ${usage}/${limit}`;
        
        // Update tooltip with more details
        this.statusBarItem.tooltip = `Augment Usage Tracker
Current: ${usage}
Limit: ${limit}
Usage: ${percentage}%
Remaining: ${limit - usage}

Click to ${this.getClickActionDescription()}`;

        // Change color based on usage percentage
        if (percentage >= 90) {
            this.statusBarItem.color = new vscode.ThemeColor('statusBarItem.errorForeground');
        } else if (percentage >= 75) {
            this.statusBarItem.color = new vscode.ThemeColor('statusBarItem.warningForeground');
        } else {
            this.statusBarItem.color = undefined;
        }

        this.updateClickCommand();
        this.statusBarItem.show();
    }

    private getClickActionDescription(): string {
        const action = this.configManager.getClickAction();
        switch (action) {
            case 'openWebsite': return 'open Augment website';
            case 'showDetails': return 'show usage details';
            case 'openSettings': return 'open settings';
            default: return 'show details';
        }
    }

    private startRefreshTimer() {
        const interval = this.configManager.getRefreshInterval() * 1000;
        
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        this.refreshTimer = setInterval(() => {
            this.updateDisplay();
        }, interval);
    }

    show() {
        this.updateDisplay();
    }

    hide() {
        this.statusBarItem.hide();
    }

    dispose() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        this.statusBarItem.dispose();
    }
}

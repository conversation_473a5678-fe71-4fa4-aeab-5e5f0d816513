"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AugmentDetector = void 0;
const vscode = __importStar(require("vscode"));
class AugmentDetector {
    constructor() {
        this.AUGMENT_EXTENSION_ID = 'augment.vscode-augment';
    }
    async detectAugmentPlugin() {
        try {
            const extension = vscode.extensions.getExtension(this.AUGMENT_EXTENSION_ID);
            return extension !== undefined && extension.isActive;
        }
        catch (error) {
            console.error('Error detecting Augment plugin:', error);
            return false;
        }
    }
    async getAugmentExtension() {
        return vscode.extensions.getExtension(this.AUGMENT_EXTENSION_ID);
    }
    async isAugmentActive() {
        const extension = await this.getAugmentExtension();
        return extension?.isActive || false;
    }
    async getAugmentVersion() {
        const extension = await this.getAugmentExtension();
        return extension?.packageJSON?.version;
    }
    async getAugmentStatus() {
        const extension = await this.getAugmentExtension();
        return {
            installed: extension !== undefined,
            active: extension?.isActive || false,
            version: extension?.packageJSON?.version
        };
    }
    // Monitor Augment extension state changes
    onAugmentStateChange(callback) {
        return vscode.extensions.onDidChange(() => {
            this.isAugmentActive().then(callback);
        });
    }
    // Try to detect Augment LSP communication (experimental)
    async tryDetectAugmentLSP() {
        try {
            // This is experimental - try to detect if Augment LSP is running
            // by checking for common LSP-related processes or network connections
            // For now, we'll just check if the extension is active
            // In the future, this could be enhanced to monitor actual LSP traffic
            return await this.isAugmentActive();
        }
        catch (error) {
            console.error('Error detecting Augment LSP:', error);
            return false;
        }
    }
    // Attempt to get real usage data from Augment (if possible)
    async tryGetRealUsageData() {
        try {
            const isActive = await this.isAugmentActive();
            if (!isActive) {
                return {
                    success: false,
                    error: 'Augment extension is not active'
                };
            }
            // TODO: This would require reverse engineering Augment's internal API
            // For now, return a placeholder response
            return {
                success: false,
                error: 'Real usage data integration not yet implemented'
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Error getting real usage data: ${error}`
            };
        }
    }
    // Check if we can integrate with Augment's status bar or UI
    async checkAugmentIntegrationPossibility() {
        const status = await this.getAugmentStatus();
        const methods = [];
        const limitations = [];
        if (status.installed) {
            methods.push('Extension state monitoring');
            if (status.active) {
                methods.push('Active extension detection');
                // TODO: Add more integration methods as they're discovered
            }
            else {
                limitations.push('Extension is installed but not active');
            }
        }
        else {
            limitations.push('Augment extension is not installed');
        }
        // Always add simulation as a fallback
        methods.push('Usage simulation based on editor events');
        return {
            canIntegrate: status.installed,
            methods,
            limitations
        };
    }
}
exports.AugmentDetector = AugmentDetector;
//# sourceMappingURL=augmentDetector.js.map
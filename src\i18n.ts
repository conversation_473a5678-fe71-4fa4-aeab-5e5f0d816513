import * as vscode from 'vscode';

// 国际化文本映射
const messages = {
    'en': {
        // Status messages
        'status.noAuth': 'No authentication available for real data fetch',
        'status.fetchingData': 'Fetching real usage data...',
        'status.apiSuccess': 'API connection successful!',
        'status.apiFailed': 'API connection failed',
        'status.cookiesConfigured': 'Augment cookies configured successfully!',
        'status.checkingAuth': 'Checking authentication status...',
        'status.authStatus': 'Authentication Status:',
        'status.apiToken': 'API Token',
        'status.browserCookies': 'Browser Cookies',
        'status.configured': 'Configured',
        'status.notConfigured': 'Not configured',
        'status.connectionTest': 'Connection test',
        'status.success': 'Success',
        'status.failed': 'Failed',
        'status.error': 'Error',
        'status.suggestion': 'Suggestion: Cookies may have expired, please get new ones',
        'status.pleaseConfigureAuth': 'Please configure authentication first',
        
        // Dialog messages
        'dialog.browserOpened': 'Browser opened! Please login to Augment, then use "Setup Browser Cookies" command.',
        'dialog.setupCookies': 'Setup Cookies',
        'dialog.cancel': 'Cancel',
        'dialog.webLoginError': 'Web login error',
        
        // Usage details
        'usage.title': 'Augment Usage Statistics:',
        'usage.currentUsage': 'Current Usage',
        'usage.monthlyLimit': 'Monthly Limit',
        'usage.usagePercentage': 'Usage Percentage',
        'usage.remaining': 'Remaining',
        'usage.lastReset': 'Last Reset',
        'usage.resetUsage': 'Reset Usage',
        'usage.openSettings': 'Open Settings',
        
        // Tooltip
        'tooltip.augmentUsageTracker': 'Augment Usage Tracker',
        'tooltip.current': 'Current',
        'tooltip.limit': 'Limit',
        'tooltip.usage': 'Usage',
        'tooltip.remaining': 'Remaining',
        'tooltip.plan': 'Plan',
        'tooltip.dataSource': 'Data Source',
        'tooltip.realDataFromApi': 'Real data from Augment API',
        'tooltip.simulatedData': 'Simulated data',
        
        // Credits
        'credits': 'credits',

        // Cookie management
        'cookie.welcome': 'Welcome to Augment Usage Tracker!',
        'cookie.firstInstall': 'To get real usage data, authentication is required.',
        'cookie.setupNow': 'Setup Now',
        'cookie.setupLater': 'Setup Later',
        'cookie.learnMore': 'Learn More',
        'cookie.expired': 'Augment authentication has expired',
        'cookie.nearExpiry': 'Augment authentication will expire soon',
        'cookie.refreshNow': 'Refresh Now',
        'cookie.refreshLater': 'Remind Later',
        'cookie.ignore': 'Ignore',
        'cookie.status': 'Cookie Status',
        'cookie.valid': 'Valid',
        'cookie.invalid': 'Invalid',
        'cookie.notConfigured': 'Not configured'
    },
    'zh-cn': {
        // Status messages
        'status.noAuth': '没有可用的认证信息获取真实数据',
        'status.fetchingData': '正在获取真实使用数据...',
        'status.apiSuccess': '✅ API连接成功！',
        'status.apiFailed': '❌ API连接失败',
        'status.cookiesConfigured': '🍪 Augment cookies 配置成功！',
        'status.checkingAuth': '🔍 检查认证状态...',
        'status.authStatus': '🔐 认证状态:',
        'status.apiToken': 'API Token',
        'status.browserCookies': 'Browser Cookies',
        'status.configured': '✅ 已配置',
        'status.notConfigured': '❌ 未配置',
        'status.connectionTest': '连接测试',
        'status.success': '✅ 成功',
        'status.failed': '❌ 失败',
        'status.error': '错误',
        'status.suggestion': '💡 建议: Cookie可能已过期，请重新获取',
        'status.pleaseConfigureAuth': '💡 请先配置认证信息',
        
        // Dialog messages
        'dialog.browserOpened': '🌐 浏览器已打开！请登录Augment，然后使用"设置浏览器Cookie"命令。',
        'dialog.setupCookies': '设置Cookie',
        'dialog.cancel': '取消',
        'dialog.webLoginError': '❌ 网页登录错误',
        
        // Usage details
        'usage.title': 'Augment 使用统计:',
        'usage.currentUsage': '当前使用量',
        'usage.monthlyLimit': '月度限额',
        'usage.usagePercentage': '使用百分比',
        'usage.remaining': '剩余',
        'usage.lastReset': '上次重置',
        'usage.resetUsage': '重置使用量',
        'usage.openSettings': '打开设置',
        
        // Tooltip
        'tooltip.augmentUsageTracker': 'Augment 使用量追踪器',
        'tooltip.current': '当前',
        'tooltip.limit': '限额',
        'tooltip.usage': '使用量',
        'tooltip.remaining': '剩余',
        'tooltip.plan': '计划',
        'tooltip.dataSource': '数据源',
        'tooltip.realDataFromApi': '来自Augment API的真实数据',
        'tooltip.simulatedData': '模拟数据',
        
        // Credits
        'credits': '积分',

        // Cookie management
        'cookie.welcome': '🎉 欢迎使用 Augment 使用量追踪器！',
        'cookie.firstInstall': '为了获取真实的使用数据，需要配置Augment认证。',
        'cookie.setupNow': '🚀 立即配置',
        'cookie.setupLater': '⏰ 稍后配置',
        'cookie.learnMore': '❓ 了解更多',
        'cookie.expired': '⚠️ Augment认证已过期',
        'cookie.nearExpiry': '⏰ Augment认证即将过期',
        'cookie.refreshNow': '🔄 现在刷新',
        'cookie.refreshLater': '⏰ 稍后提醒',
        'cookie.ignore': '❌ 忽略',
        'cookie.status': '🍪 Cookie状态',
        'cookie.valid': '✅ 有效',
        'cookie.invalid': '❌ 无效',
        'cookie.notConfigured': '❌ 未配置'
    }
};

export class I18n {
    private static locale: string = 'en';
    
    static init() {
        // 获取VSCode语言设置
        const vscodeLocale = vscode.env.language;
        
        // 支持的语言映射
        const supportedLocales: { [key: string]: string } = {
            'zh-cn': 'zh-cn',
            'zh-tw': 'zh-cn', // 繁体中文也使用简体中文
            'zh': 'zh-cn',
            'en': 'en',
            'en-us': 'en',
            'en-gb': 'en'
        };
        
        this.locale = supportedLocales[vscodeLocale.toLowerCase()] || 'en';
        console.log(`🌐 I18n initialized with locale: ${this.locale} (VSCode: ${vscodeLocale})`);
    }
    
    static t(key: string, ...args: any[]): string {
        const localeMessages = messages[this.locale as keyof typeof messages] || messages['en'];
        let message = localeMessages[key as keyof typeof localeMessages] || key;
        
        // 简单的参数替换
        if (args.length > 0) {
            args.forEach((arg, index) => {
                message = message.replace(`{${index}}`, String(arg));
            });
        }
        
        return message;
    }
    
    static getLocale(): string {
        return this.locale;
    }
    
    static isZhCn(): boolean {
        return this.locale === 'zh-cn';
    }
}

// 便捷函数
export function t(key: string, ...args: any[]): string {
    return I18n.t(key, ...args);
}

# 🔄 定时器调试指南

## 🎯 问题诊断

看到命令未找到的错误，这说明编译可能有问题。让我们用其他方法来验证定时器是否工作。

## 🔍 验证步骤

### 步骤1：重新启动插件
1. **完全关闭VSCode**
2. **重新打开项目**
3. **按F5启动Extension Development Host**

### 步骤2：检查控制台输出
1. 在Extension Development Host中按 `F12`
2. 切换到 `Console` 标签页
3. 查找以下关键日志：

#### 预期的启动日志：
```
Augment Usage Tracker is now active!
🔄 Starting real data refresh with 5s interval
🔧 Setting up real data fetcher...
✅ Real data fetcher configured
```

#### 预期的定时日志（每5秒）：
```
🔍 Fetching real usage data from Augment API...
🔄 [时间] Fetching real usage data...
```

### 步骤3：使用现有命令测试
如果新命令不工作，使用现有的命令：

```
Ctrl+Shift+P → "Debug API Calls" → "🪙 Credits Info"
```

这会手动触发一次API调用，验证认证是否正常。

### 步骤4：检查认证状态
```
Ctrl+Shift+P → "Check Authentication Status"
```

确保有有效的认证信息。

## 🔧 手动验证定时器

### 方法1：观察状态栏变化
- 状态栏应该每5秒更新一次
- 如果有真实数据，应该显示 `●` (实心圆)

### 方法2：检查时间戳
在控制台中查找带时间戳的日志：
```
🔄 [10:30:15] Fetching real usage data...
🔄 [10:30:20] Fetching real usage data...
🔄 [10:30:25] Fetching real usage data...
```

### 方法3：手动触发
在控制台中输入以下代码来手动测试：
```javascript
// 检查定时器状态
console.log('Testing timer manually...');
```

## 🛠️ 故障排除

### 如果没有看到定时日志：

#### 1. 检查配置
- 确认 `augmentTracker.refreshInterval` 设置为 5
- 确认 `augmentTracker.enabled` 为 true

#### 2. 检查认证
- 确保有有效的cookie或token
- 使用 "Check Authentication Status" 验证

#### 3. 检查错误
- 查看控制台是否有红色错误信息
- 特别注意认证相关错误

### 如果看到认证错误：

#### 重新配置认证：
```
Ctrl+Shift+P → "Setup Browser Cookies"
```

或者：
```
Ctrl+Shift+P → "🌐 Web Login (Auto)"
```

## 📊 预期的完整日志流程

### 启动时：
```
Augment Usage Tracker is now active!
🔄 Starting real data refresh with 5s interval
🔧 Setting up real data fetcher...
✅ Real data fetcher configured
🔍 Fetching real usage data from Augment API...
🪙 Trying Credits API...
📊 Credits API Raw Response: { ... }
✅ Parsed Credits Data: { ... }
```

### 每5秒：
```
🔄 [10:30:20] Fetching real usage data...
📊 Credits API Response: {
  "usageUnitsAvailable": 49,
  "usageUnitsUsedThisBillingCycle": 7,
  "usageUnitsPending": 0
}
✅ Parsed usage data: {
  "totalUsage": 7,
  "usageLimit": 56,
  "dataSource": "Credits API",
  "timestamp": "10:30:20"
}
✅ Updated with real Augment data: 7
```

## 🎯 成功指标

### ✅ 定时器正常工作的标志：
1. 控制台每5秒有新的时间戳日志
2. 状态栏数据实时更新
3. JSON数据持续输出
4. 没有认证错误

### ✅ 真实数据集成成功：
1. 状态栏显示 `●` (实心圆)
2. 工具提示显示 "Real data from Augment API"
3. 数据与web界面一致

## 🔄 如果还是不工作

### 最后的调试方法：
1. **完全重启VSCode**
2. **删除out文件夹**
3. **重新编译**：`npx tsc`
4. **重新启动插件**

### 或者使用现有功能：
- 使用 "Debug API Calls" 手动测试
- 检查 "Check Authentication Status"
- 确保认证配置正确

**请按照这个指南重新启动插件并观察控制台输出！**

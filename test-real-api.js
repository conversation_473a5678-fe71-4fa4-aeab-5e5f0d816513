// 基于HAR文件分析的真实Augment API测试脚本
const https = require('https');

// 从HAR文件中提取的真实cookie
const REAL_COOKIES = 'intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D';

console.log('🔍 基于HAR分析的真实Augment API测试');
console.log('='.repeat(60));

// 真实API端点（从HAR文件中发现）
const API_ENDPOINTS = [
    { name: '👤 用户信息', path: '/api/user', description: '获取用户账户信息' },
    { name: '🪙 使用统计', path: '/api/credits', description: '获取真实的credits使用情况' },
    { name: '💳 订阅信息', path: '/api/subscription', description: '获取订阅和计费信息' },
    { name: '📋 计划信息', path: '/api/plans', description: '获取可用的订阅计划' },
    { name: '💰 支付信息', path: '/api/payment', description: '获取支付方式信息' },
    { name: '🔄 计划变更', path: '/api/team/plan-change-pending', description: '检查计划变更状态' }
];

// 测试单个API端点
async function testApiEndpoint(endpoint) {
    console.log(`\n${endpoint.name} - ${endpoint.description}`);
    console.log(`请求: GET https://app.augmentcode.com${endpoint.path}`);
    
    return new Promise((resolve) => {
        const options = {
            hostname: 'app.augmentcode.com',
            path: endpoint.path,
            method: 'GET',
            headers: {
                'Cookie': REAL_COOKIES,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Referer': 'https://app.augmentcode.com/account/subscription',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Priority': 'u=4',
                'TE': 'trailers'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应长度: ${data.length} 字符`);
                
                if (res.statusCode === 200) {
                    try {
                        const json = JSON.parse(data);
                        console.log(`   ✅ 成功获取数据`);
                        console.log(`   📊 数据结构:`);
                        
                        // 显示数据的关键字段
                        Object.entries(json).forEach(([key, value]) => {
                            if (typeof value === 'object' && value !== null) {
                                console.log(`      ${key}: [对象]`);
                            } else {
                                const displayValue = String(value).length > 50 
                                    ? String(value).substring(0, 50) + '...'
                                    : value;
                                console.log(`      ${key}: ${displayValue}`);
                            }
                        });
                        
                        // 特殊处理credits数据
                        if (endpoint.path === '/api/credits') {
                            console.log(`   🎯 使用统计分析:`);
                            console.log(`      已使用: ${json.usageUnitsUsedThisBillingCycle || 'N/A'}`);
                            console.log(`      可用: ${json.usageUnitsAvailable || 'N/A'}`);
                            console.log(`      待处理: ${json.usageUnitsPending || 'N/A'}`);
                            const total = (json.usageUnitsUsedThisBillingCycle || 0) + (json.usageUnitsAvailable || 0);
                            const usage = total > 0 ? ((json.usageUnitsUsedThisBillingCycle || 0) / total * 100).toFixed(1) : 0;
                            console.log(`      使用率: ${usage}%`);
                        }
                        
                        // 特殊处理订阅数据
                        if (endpoint.path === '/api/subscription') {
                            console.log(`   🎯 订阅信息分析:`);
                            console.log(`      计划类型: ${json.augmentPlanType || 'N/A'}`);
                            console.log(`      计划名称: ${json.planName || 'N/A'}`);
                            console.log(`      每周期credits: ${json.creditsRenewingEachBillingCycle || 'N/A'}`);
                            console.log(`      本周期credits: ${json.creditsIncludedThisBillingCycle || 'N/A'}`);
                            console.log(`      计费周期结束: ${json.billingPeriodEnd || 'N/A'}`);
                        }
                        
                        resolve({ success: true, data: json });
                    } catch (e) {
                        console.log(`   ✅ 成功 (非JSON响应)`);
                        console.log(`   响应内容: ${data.substring(0, 200)}...`);
                        resolve({ success: true, data: data });
                    }
                } else {
                    console.log(`   ❌ 请求失败`);
                    console.log(`   错误信息: ${data.substring(0, 200)}...`);
                    resolve({ success: false, error: data });
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ 网络错误: ${err.message}`);
            resolve({ success: false, error: err.message });
        });
        
        req.end();
    });
}

// 主测试函数
async function runRealApiTests() {
    console.log(`开始时间: ${new Date().toISOString()}`);
    console.log(`测试端点数量: ${API_ENDPOINTS.length}`);
    
    const results = {};
    
    for (const endpoint of API_ENDPOINTS) {
        const result = await testApiEndpoint(endpoint);
        results[endpoint.path] = result;
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('📋 测试结果汇总:');
    
    let successCount = 0;
    Object.entries(results).forEach(([path, result]) => {
        const endpoint = API_ENDPOINTS.find(e => e.path === path);
        const status = result.success ? '✅ 成功' : '❌ 失败';
        console.log(`   ${endpoint.name}: ${status}`);
        if (result.success) successCount++;
    });
    
    console.log(`\n🎯 总体结果: ${successCount}/${API_ENDPOINTS.length} 个端点测试成功`);
    
    // 生成插件集成建议
    console.log('\n💡 插件集成建议:');
    if (results['/api/credits']?.success) {
        console.log('   ✅ 使用 /api/credits 获取真实使用统计');
        console.log('   ✅ 数据格式: usageUnitsUsedThisBillingCycle, usageUnitsAvailable');
    }
    if (results['/api/subscription']?.success) {
        console.log('   ✅ 使用 /api/subscription 获取订阅信息和限额');
        console.log('   ✅ 数据格式: creditsRenewingEachBillingCycle, augmentPlanType');
    }
    if (results['/api/user']?.success) {
        console.log('   ✅ 使用 /api/user 进行健康检查和用户验证');
    }
    
    console.log(`\n完成时间: ${new Date().toISOString()}`);
}

// 运行测试
runRealApiTests().catch(console.error);

@echo off
chcp 65001 > nul
echo ========================================
echo   添加状态栏邮箱显示功能
echo ========================================
echo.

echo 🎯 新功能描述:
echo 根据HAR抓包数据优化，在状态栏显示用户邮箱信息，
echo 提供更个性化的使用体验。
echo.

echo 📊 功能特性:
echo [1] 状态栏显示邮箱前缀 (如: john: 7/300 ●)
echo [2] Tooltip显示完整用户信息 (邮箱、姓名、计划)
echo [3] 同时获取使用数据和用户信息
echo [4] 退出登录时清除用户信息
echo [5] 支持多种用户API响应格式
echo.

echo 🔧 实现内容:
echo [1] 添加AugmentUserInfo接口
echo [2] 实现parseUserResponse方法
echo [3] 修改StatusBarManager支持用户信息
echo [4] 更新数据获取逻辑
echo [5] 优化状态栏显示格式
echo.

echo [1/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [2/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [3/4] 发布更新版本...
set /p confirm="确定要发布新功能版本吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo ❌ 发布失败
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo [4/4] 生成功能说明...

echo ========================================
echo   新功能使用指南
echo ========================================
echo.

echo 📋 状态栏显示变化:
echo.
echo 修改前:
echo   $(pulse) Augment: 7/300 ●
echo.
echo 修改后 (有用户信息):
echo   $(pulse) john: 7/300 ●
echo   (显示邮箱前缀而不是"Augment")
echo.
echo 修改后 (无用户信息):
echo   $(pulse) Augment: 7/300 ●
echo   (保持原有显示)
echo.

echo 🔍 Tooltip信息增强:
echo.
echo 新增用户信息部分:
echo   用户信息:
echo   • 邮箱: <EMAIL>
echo   • 姓名: John Doe
echo   • 计划: Pro
echo.
echo   使用情况:
echo   • 当前使用量: 7 积分
echo   • 月度限额: 300 积分
echo   • 使用百分比: 2%
echo   • 剩余: 293 积分
echo.

echo ========================================
echo   技术实现说明
echo ========================================
echo.

echo 🔧 API数据获取:
echo.
echo 并行获取数据:
echo   Promise.all([
echo     apiClient.getCreditsInfo(),    // 使用数据
echo     apiClient.getUserInfo()        // 用户信息
echo   ])
echo.

echo 📊 用户信息解析:
echo.
echo parseUserResponse支持多种格式:
echo   • email / emailAddress / userEmail
echo   • name / displayName / fullName / username
echo   • id / userId / user_id
echo   • plan / planType / subscriptionType
echo   • avatar / avatarUrl / profileImage
echo   • verified / emailVerified
echo.

echo 🎨 状态栏显示逻辑:
echo.
echo 显示优先级:
echo   1. 有用户邮箱 → 显示邮箱前缀
echo   2. 无用户邮箱 → 显示"Augment"
echo   3. 未登录 → 显示"未登录"
echo.

echo ========================================
echo   测试验证步骤
echo ========================================
echo.

echo 🧪 测试1: 邮箱显示功能
echo 1. 确保已登录并获取到用户信息
echo 2. 查看状态栏显示
echo 3. 应该显示邮箱前缀而不是"Augment"
echo 4. 鼠标悬停查看完整用户信息
echo.

echo 🧪 测试2: 数据刷新功能
echo 1. 运行 Ctrl+Shift+P → "🔄 手动刷新"
echo 2. 验证同时获取使用数据和用户信息
echo 3. 确认状态栏和tooltip都正确更新
echo.

echo 🧪 测试3: 退出登录功能
echo 1. 运行 Ctrl+Shift+P → "🚪 退出登录"
echo 2. 验证用户信息被清除
echo 3. 状态栏恢复显示"Augment: 未登录"
echo.

echo 🧪 测试4: 重新登录功能
echo 1. 重新配置认证信息
echo 2. 验证用户信息重新获取
echo 3. 状态栏重新显示邮箱前缀
echo.

echo ========================================
echo   HAR数据适配说明
echo ========================================
echo.

echo 📋 支持的用户API响应格式:
echo.
echo 标准格式:
echo   {
echo     "email": "<EMAIL>",
echo     "name": "John Doe",
echo     "id": "12345",
echo     "plan": "Pro"
echo   }
echo.

echo 兼容格式:
echo   {
echo     "emailAddress": "<EMAIL>",
echo     "displayName": "John Doe",
echo     "userId": "12345",
echo     "planType": "Pro"
echo   }
echo.

echo 🔄 自动适配机制:
echo parseUserResponse方法会自动尝试多种字段名，
echo 确保与不同版本的Augment API兼容。
echo.

echo ========================================
echo   用户体验改进
echo ========================================
echo.

echo ✅ 个性化显示:
echo - 状态栏显示用户邮箱前缀
echo - 更直观的身份识别
echo - 个性化的使用体验
echo.

echo ✅ 信息丰富:
echo - Tooltip显示完整用户信息
echo - 包含邮箱、姓名、计划等
echo - 一目了然的账户状态
echo.

echo ✅ 数据一致性:
echo - 同时获取使用数据和用户信息
echo - 确保信息同步更新
echo - 避免数据不一致问题
echo.

echo ✅ 状态管理:
echo - 登录时获取用户信息
echo - 退出时清除用户信息
echo - 状态切换流畅自然
echo.

echo ✅ 邮箱显示功能添加完成！
echo.
echo 现在状态栏会显示用户邮箱前缀，
echo 提供更个性化的使用体验。
echo.
echo 示例显示:
echo   john: 7/300 ● (2%)
echo.

pause

@echo off
chcp 65001 > nul
echo ========================================
echo   VSCode插件国际化诊断工具
echo ========================================
echo.

echo 🔍 诊断步骤:
echo [1] 检查语言包文件
echo [2] 验证package.json占位符
echo [3] 检查VSCode语言设置
echo [4] 验证打包内容
echo [5] 生成诊断报告
echo.

echo [1/5] 检查语言包文件...
echo.

if exist package.nls.json (
    echo ✅ 英文语言包存在: package.nls.json
    echo 内容预览:
    type package.nls.json | head -10
    echo.
) else (
    echo ❌ 缺少英文语言包: package.nls.json
)

if exist package.nls.zh-cn.json (
    echo ✅ 中文语言包存在: package.nls.zh-cn.json
    echo 内容预览:
    type package.nls.zh-cn.json | head -10
    echo.
) else (
    echo ❌ 缺少中文语言包: package.nls.zh-cn.json
)

echo [2/5] 验证package.json占位符...
echo.
echo 检查commands部分:
findstr /C:"title.*%" package.json
echo.

echo [3/5] 检查当前VSCode语言设置...
echo.
echo 请手动检查以下设置:
echo 1. VSCode界面语言: File → Preferences → Settings → "locale"
echo 2. 插件语言设置: augmentTracker.language
echo.

echo [4/5] 验证打包内容...
echo.
if exist augment-usage-tracker-*.vsix (
    echo ✅ 找到插件包文件
    for %%f in (augment-usage-tracker-*.vsix) do (
        echo 文件: %%f
        echo 大小: 
        dir "%%f" | findstr "%%f"
    )
) else (
    echo ❌ 未找到插件包文件
    echo 请先运行: npx @vscode/vsce package --no-dependencies
)
echo.

echo [5/5] 生成诊断报告...
echo.

echo ========================================
echo   诊断报告
echo ========================================
echo.

echo 📋 文件检查:
if exist package.nls.json (echo ✅ package.nls.json) else (echo ❌ package.nls.json)
if exist package.nls.zh-cn.json (echo ✅ package.nls.zh-cn.json) else (echo ❌ package.nls.zh-cn.json)
if exist package.json (echo ✅ package.json) else (echo ❌ package.json)
echo.

echo 📋 占位符检查:
findstr /C:"title.*%" package.json >nul
if %errorlevel%==0 (
    echo ✅ package.json使用占位符
) else (
    echo ❌ package.json未使用占位符
)
echo.

echo 🔧 可能的解决方案:
echo.
echo 方案1: VSCode语言设置问题
echo - 检查VSCode是否设置为中文: Ctrl+Shift+P → "Configure Display Language"
echo - 确保VSCode重启后生效
echo.
echo 方案2: 插件语言包问题
echo - 确保语言包文件正确打包
echo - 检查占位符名称是否匹配
echo.
echo 方案3: 强制重新安装
echo - 卸载插件: code --uninstall-extension augment-usage-tracker.augment-usage-tracker
echo - 重新安装: code --install-extension augment-usage-tracker-1.0.0.vsix
echo.
echo 方案4: 手动测试
echo - 创建测试插件验证国际化机制
echo - 对比其他支持国际化的插件
echo.

echo ========================================
echo   下一步建议
echo ========================================
echo.
echo 1. 首先确认VSCode本身的语言设置
echo 2. 检查是否需要重启VSCode
echo 3. 验证语言包文件是否正确打包
echo 4. 考虑使用vscode-nls库
echo.

pause

{"displayName": "Augment Usage Tracker", "description": "Track and display Augment AI usage statistics in VSCode status bar", "command.resetUsage": "Reset Usage Statistics", "command.openSettings": "Open Settings", "command.showDetails": "Show Usage Details", "command.setupCookies": "Setup Browser Cookies", "command.checkAuthStatus": "Check Authentication Status", "command.webLogin": "🌐 Web Login (Auto)", "command.manualRefresh": "🔄 Manual Refresh", "command.setLanguage": "🌐 Set Language", "command.checkCookieStatus": "🍪 Check Cookie Status", "command.refreshCookie": "🔄 Refresh <PERSON><PERSON>", "config.title": "Augment Usage Tracker", "config.enabled": "Enable/disable the Augment usage tracker", "config.usageLimit": "Monthly usage limit for tracking", "config.refreshInterval": "Status bar refresh interval in seconds", "config.showInStatusBar": "Show usage statistics in status bar", "config.cookies": "Augment browser session cookies", "status.noAuth": "No authentication available for real data fetch", "status.fetchingData": "Fetching real usage data...", "status.apiSuccess": "API connection successful!", "status.apiFailed": "API connection failed", "status.cookiesConfigured": "Augment cookies configured successfully!", "status.checkingAuth": "Checking authentication status...", "status.authStatus": "Authentication Status:", "status.apiToken": "API Token", "status.browserCookies": "Browser Cookies", "status.configured": "Configured", "status.notConfigured": "Not configured", "status.connectionTest": "Connection test", "status.success": "Success", "status.failed": "Failed", "status.error": "Error", "status.suggestion": "Suggestion: Cookies may have expired, please get new ones", "status.pleaseConfigureAuth": "Please configure authentication first", "dialog.browserOpened": "Browser opened! Please login to Augment, then use \"Setup Browser Cookies\" command.", "dialog.setupCookies": "Setup Cookies", "dialog.cancel": "Cancel", "dialog.webLoginError": "Web login error", "usage.currentUsage": "Current Usage", "usage.monthlyLimit": "Monthly Limit", "usage.usagePercentage": "Usage Percentage", "usage.remaining": "Remaining", "usage.lastReset": "Last Reset", "usage.resetUsage": "Reset Usage", "usage.openSettings": "Open Settings", "tooltip.augmentUsageTracker": "Augment Usage Tracker", "tooltip.current": "Current", "tooltip.limit": "Limit", "tooltip.usage": "Usage", "tooltip.remaining": "Remaining", "tooltip.plan": "Plan", "tooltip.dataSource": "Data Source", "tooltip.realDataFromApi": "Real data from Augment API", "tooltip.simulatedData": "Simulated data"}
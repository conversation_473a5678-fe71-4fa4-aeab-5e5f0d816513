import * as vscode from 'vscode';

export interface WebAuthResult {
    success: boolean;
    cookies?: string;
    error?: string;
}

export class WebAuthManager {
    private readonly AUGMENT_LOGIN_URL = 'https://app.augmentcode.com';
    private readonly AUGMENT_DASHBOARD_URL = 'https://app.augmentcode.com/dashboard';

    async authenticateWithWebLogin(): Promise<WebAuthResult> {
        try {
            // 显示进度提示
            return await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "Augment Web Authentication",
                cancellable: true
            }, async (progress, token) => {
                
                progress.report({ increment: 0, message: "Opening Augment login page..." });
                
                // 打开登录页面
                const loginUri = vscode.Uri.parse(this.AUGMENT_LOGIN_URL);
                await vscode.env.openExternal(loginUri);
                
                progress.report({ increment: 25, message: "Please login in your browser..." });
                
                // 等待用户登录
                const loginChoice = await vscode.window.showInformationMessage(
                    '🌐 Please login to Augment in your browser, then click "Continue" when you reach the dashboard.',
                    { modal: false },
                    'Continue',
                    'Cancel'
                );
                
                if (loginChoice !== 'Continue') {
                    return { success: false, error: 'User cancelled authentication' };
                }
                
                progress.report({ increment: 50, message: "Waiting for cookie input..." });
                
                // 提示用户获取cookie
                const cookieInstructions = await vscode.window.showInformationMessage(
                    '🍪 Now we need to get your session cookies:\n\n' +
                    '1. In your browser, press F12 to open Developer Tools\n' +
                    '2. Go to Application tab → Cookies → app.augmentcode.com\n' +
                    '3. Copy all cookie values\n\n' +
                    'Or use our automatic cookie extractor!',
                    'Auto Extract',
                    'Manual Input',
                    'Cancel'
                );
                
                if (cookieInstructions === 'Cancel') {
                    return { success: false, error: 'User cancelled cookie extraction' };
                }
                
                progress.report({ increment: 75, message: "Getting cookies..." });
                
                if (cookieInstructions === 'Auto Extract') {
                    return await this.autoExtractCookies();
                } else {
                    return await this.manualCookieInput();
                }
            });
            
        } catch (error) {
            return {
                success: false,
                error: `Authentication failed: ${error}`
            };
        }
    }

    private async autoExtractCookies(): Promise<WebAuthResult> {
        try {
            // 显示JavaScript代码让用户在浏览器控制台执行
            const jsCode = `
// Augment Cookie Extractor
(function() {
    const cookies = document.cookie;
    if (cookies.includes('_session=')) {
        console.log('✅ Cookies extracted successfully!');
        console.log('📋 Copy the following line:');
        console.log('COOKIES_START');
        console.log(cookies);
        console.log('COOKIES_END');
        
        // 尝试复制到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(cookies).then(() => {
                console.log('✅ Cookies copied to clipboard!');
            }).catch(() => {
                console.log('⚠️ Please manually copy the cookies above');
            });
        }
        
        alert('✅ Cookies extracted! Check console and paste in VSCode.');
        return cookies;
    } else {
        console.log('❌ No valid session found. Please make sure you are logged in.');
        alert('❌ Please login first, then run this script again.');
        return null;
    }
})();`;

            // 显示JavaScript代码
            const doc = await vscode.workspace.openTextDocument({
                content: jsCode,
                language: 'javascript'
            });
            await vscode.window.showTextDocument(doc);
            
            const instruction = await vscode.window.showInformationMessage(
                '🔧 Auto Cookie Extractor:\n\n' +
                '1. Copy the JavaScript code shown above\n' +
                '2. In your browser (on app.augmentcode.com), press F12\n' +
                '3. Go to Console tab\n' +
                '4. Paste and press Enter\n' +
                '5. Copy the cookies output\n' +
                '6. Click "Paste Cookies" below',
                'Paste Cookies',
                'Cancel'
            );
            
            if (instruction === 'Paste Cookies') {
                return await this.manualCookieInput();
            } else {
                return { success: false, error: 'User cancelled auto extraction' };
            }
            
        } catch (error) {
            return {
                success: false,
                error: `Auto extraction failed: ${error}`
            };
        }
    }

    private async manualCookieInput(): Promise<WebAuthResult> {
        const cookies = await vscode.window.showInputBox({
            prompt: 'Paste your Augment cookies here',
            placeHolder: 'intercom-id-oiuh4kg0=...; _session=...; ajs_user_id=...',
            password: true,
            ignoreFocusOut: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Cookies cannot be empty';
                }
                if (!value.includes('_session=')) {
                    return 'Invalid cookies - should contain _session=';
                }
                if (!value.includes('ajs_user_id=')) {
                    return 'Invalid cookies - should contain ajs_user_id=';
                }
                return null;
            }
        });

        if (cookies) {
            return {
                success: true,
                cookies: cookies.trim()
            };
        } else {
            return {
                success: false,
                error: 'No cookies provided'
            };
        }
    }

    async quickLogin(): Promise<WebAuthResult> {
        // 简化的登录流程
        const choice = await vscode.window.showQuickPick([
            {
                label: '🌐 Web Login',
                description: 'Open browser and login automatically',
                detail: 'Recommended for first-time setup'
            },
            {
                label: '🍪 Paste Cookies',
                description: 'Manually paste cookies from browser',
                detail: 'Quick option if you already have cookies'
            },
            {
                label: '🔑 API Token',
                description: 'Use API token instead',
                detail: 'For advanced users'
            }
        ], {
            placeHolder: 'Choose authentication method',
            ignoreFocusOut: true
        });

        if (!choice) {
            return { success: false, error: 'No authentication method selected' };
        }

        switch (choice.label) {
            case '🌐 Web Login':
                return await this.authenticateWithWebLogin();
            case '🍪 Paste Cookies':
                return await this.manualCookieInput();
            case '🔑 API Token':
                // 这里可以调用API token设置
                return { success: false, error: 'API token setup not implemented in this method' };
            default:
                return { success: false, error: 'Unknown authentication method' };
        }
    }

    async validateCookies(cookies: string): Promise<boolean> {
        // 简单的cookie格式验证
        const requiredCookies = ['_session', 'ajs_user_id'];
        return requiredCookies.every(cookieName => 
            cookies.includes(cookieName + '=')
        );
    }

    generateCookieExtractionScript(): string {
        return `
// 🍪 Augment Cookie Extractor Script
// Run this in browser console on app.augmentcode.com

(function() {
    console.log('🔍 Extracting Augment cookies...');
    
    const cookies = document.cookie;
    const domain = window.location.hostname;
    
    console.log('🌐 Domain:', domain);
    console.log('📊 Total cookies:', document.cookie.split(';').length);
    
    if (!cookies.includes('_session=')) {
        console.error('❌ No session cookie found. Please login first.');
        alert('❌ Please login to Augment first, then run this script again.');
        return null;
    }
    
    console.log('✅ Session cookie found!');
    console.log('📋 Cookies to copy:');
    console.log('--- COPY BELOW ---');
    console.log(cookies);
    console.log('--- COPY ABOVE ---');
    
    // Try to copy to clipboard
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(cookies).then(() => {
            console.log('✅ Cookies copied to clipboard!');
            alert('✅ Cookies copied to clipboard! Paste them in VSCode.');
        }).catch(err => {
            console.log('⚠️ Could not copy to clipboard:', err);
            alert('⚠️ Please manually copy the cookies from console.');
        });
    } else {
        console.log('⚠️ Clipboard not available. Please copy manually.');
        alert('⚠️ Please manually copy the cookies from console.');
    }
    
    return cookies;
})();`;
    }
}

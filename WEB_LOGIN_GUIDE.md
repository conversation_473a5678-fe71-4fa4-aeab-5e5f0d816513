# 🌐 网页自动登录指南

## 🎯 功能概述

现在您可以通过自动化的网页登录流程来获取Augment的认证信息，无需手动复制cookie！

## 🚀 使用方法

### 方法1：一键网页登录 ⭐推荐

```
Ctrl+Shift+P → "🌐 Web Login (Auto)"
```

**完整流程**：
1. **选择认证方式**：
   - 🌐 Web Login - 自动化网页登录
   - 🍪 Paste Cookies - 手动粘贴cookie
   - 🔑 API Token - 使用API token

2. **自动打开浏览器**：
   - 插件会自动打开 `https://app.augmentcode.com`
   - 在浏览器中正常登录您的账户

3. **确认登录完成**：
   - 登录后点击插件中的 "Continue" 按钮
   - 插件会引导您获取认证信息

4. **自动提取Cookie**：
   - 选择 "Auto Extract" 使用自动提取工具
   - 或选择 "Manual Input" 手动输入

### 方法2：自动Cookie提取器

如果选择 "Auto Extract"，插件会：

1. **显示JavaScript代码**：
   ```javascript
   // 🍪 Augment Cookie Extractor Script
   // 在浏览器控制台运行此代码
   ```

2. **执行步骤**：
   - 复制显示的JavaScript代码
   - 在浏览器中按 `F12` 打开开发者工具
   - 切换到 `Console` 标签页
   - 粘贴代码并按回车
   - 代码会自动提取并复制cookie

3. **完成认证**：
   - 返回VSCode点击 "Paste Cookies"
   - 粘贴提取的cookie
   - 插件自动验证并保存

## 🔧 自动提取器功能

### JavaScript提取器特性
- ✅ **自动检测**：验证是否已登录
- ✅ **智能提取**：只提取必要的认证cookie
- ✅ **剪贴板复制**：自动复制到剪贴板
- ✅ **错误提示**：未登录时给出明确提示
- ✅ **安全验证**：检查关键session信息

### 提取器输出示例
```
🔍 Extracting Augment cookies...
🌐 Domain: app.augmentcode.com
📊 Total cookies: 12
✅ Session cookie found!
📋 Cookies to copy:
--- COPY BELOW ---
intercom-id-oiuh4kg0=...; _session=...; ajs_user_id=...
--- COPY ABOVE ---
✅ Cookies copied to clipboard!
```

## 🎯 认证方式对比

| 方式 | 难度 | 自动化程度 | 推荐指数 |
|------|------|------------|----------|
| **🌐 Web Login** | ⭐ 简单 | ⭐⭐⭐⭐⭐ 全自动 | ⭐⭐⭐⭐⭐ |
| **🍪 Manual Cookies** | ⭐⭐ 中等 | ⭐⭐ 半自动 | ⭐⭐⭐ |
| **🔑 API Token** | ⭐⭐⭐ 复杂 | ⭐ 手动 | ⭐⭐ |

## 📋 完整使用流程

### 步骤1：启动网页登录
```
F5 启动插件 → Ctrl+Shift+P → "🌐 Web Login (Auto)"
```

### 步骤2：选择登录方式
- 首次使用选择 "🌐 Web Login"
- 已有cookie选择 "🍪 Paste Cookies"

### 步骤3：浏览器登录
- 自动打开 `app.augmentcode.com`
- 输入您的邮箱和密码
- 等待跳转到dashboard

### 步骤4：提取认证信息
- 选择 "Auto Extract" 使用自动提取
- 按照提示在浏览器控制台运行代码
- 复制提取的cookie

### 步骤5：完成认证
- 返回VSCode粘贴cookie
- 插件自动验证连接
- 开始显示真实使用数据

## 🔍 验证成功标志

### 插件状态变化
- ✅ 状态栏显示 `Augment: X/Y ●` (实心圆)
- ✅ 工具提示显示 "Real data from Augment API"
- ✅ 控制台显示 "✅ Authentication successful!"

### 测试连接
```
Ctrl+Shift+P → "Debug API Calls" → "🪙 Credits Info"
```
应该返回真实的使用数据而不是认证错误。

## 🛠️ 故障排除

### 常见问题

#### 1. 浏览器没有自动打开
- 手动访问 `https://app.augmentcode.com`
- 确保VSCode有权限打开外部链接

#### 2. JavaScript代码执行失败
- 确保在正确的网站 (app.augmentcode.com)
- 确保已经登录到dashboard
- 检查浏览器控制台是否有错误

#### 3. Cookie提取失败
- 刷新页面重新登录
- 检查是否有广告拦截器干扰
- 尝试使用隐身模式

#### 4. 认证仍然失败
- 检查cookie是否完整
- 确认没有遗漏关键部分
- 尝试重新获取cookie

### 调试命令
```
Ctrl+Shift+P → "Check Authentication Status"
```
查看详细的认证状态和错误信息。

## 🔒 安全说明

### 数据安全
- ✅ **本地存储**：所有认证信息仅存储在本地
- ✅ **不传输**：不向任何第三方服务器发送数据
- ✅ **用户控制**：用户完全控制认证信息
- ✅ **自动过期**：cookie会自然过期，无需手动清理

### 最佳实践
1. **定期更新**：建议每天重新登录一次
2. **安全环境**：在安全的网络环境中使用
3. **及时清理**：不使用时可以清除认证信息
4. **监控状态**：定期检查认证状态

## 🎉 优势总结

### 用户体验
- 🚀 **一键登录**：最简单的认证方式
- 🤖 **自动化**：减少手动操作
- 🔄 **智能回退**：多种认证方式可选
- 📱 **友好提示**：详细的操作指导

### 技术优势
- 🛡️ **安全可靠**：使用官方登录流程
- 🔧 **智能检测**：自动验证认证状态
- 📊 **真实数据**：获取准确的使用统计
- 🔄 **自动同步**：实时更新使用信息

现在您可以轻松地通过网页登录获取真实的Augment使用数据了！🎉

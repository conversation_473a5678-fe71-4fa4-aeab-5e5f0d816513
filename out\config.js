"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const vscode = __importStar(require("vscode"));
class ConfigManager {
    constructor() {
        this.reloadConfig();
    }
    reloadConfig() {
        this.config = vscode.workspace.getConfiguration('augmentTracker');
    }
    isEnabled() {
        return this.config.get('enabled', true);
    }
    getUsageLimit() {
        return this.config.get('usageLimit', 1000);
    }
    getRefreshInterval() {
        return this.config.get('refreshInterval', 30);
    }
    shouldShowInStatusBar() {
        return this.config.get('showInStatusBar', true);
    }
    getClickAction() {
        return this.config.get('clickAction', 'openWebsite');
    }
    async updateConfig(key, value) {
        await this.config.update(key, value, vscode.ConfigurationTarget.Global);
        this.reloadConfig();
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=config.js.map
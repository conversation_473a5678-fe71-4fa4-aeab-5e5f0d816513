# Augment Usage Tracker - 使用指南

## 快速开始

### 安装插件
1. 在VSCode中按 `F5` 启动Extension Development Host
2. 或者打包为VSIX文件进行安装

### 基本使用
插件安装后会自动激活，您将在状态栏右下角看到：
```
$(pulse) Augment: 0/1000
```

## 功能详解

### 状态栏显示
- **格式**：`Augment: 当前使用量/限额`
- **颜色指示**：
  - 正常：使用率 < 75%
  - 警告：使用率 75-89% (黄色)
  - 错误：使用率 ≥ 90% (红色)

### 使用统计规则
插件会根据以下编辑器活动计算使用量：

| 活动类型 | 计分规则 | 说明 |
|---------|---------|------|
| 文本修改 | +0.1 | 每次文档内容变化 |
| 文档保存 | +1.0 | 每次保存文件 |
| AI相关命令 | +2.0 | 触发AI辅助功能 |
| 编辑器切换 | +0.1 | 切换活动编辑器 |

### 点击行为配置
状态栏项目支持三种点击行为：

1. **打开网站** (默认)：跳转到 augmentcode.com
2. **显示详情**：弹出详细使用统计
3. **打开设置**：直接进入插件设置

## 命令使用

### 通过命令面板 (Ctrl+Shift+P)

#### `Augment Tracker: Show Usage Details`
显示详细的使用统计信息，包括：
- 当前使用量和限额
- 使用百分比
- 剩余可用量
- 上次重置日期

#### `Augment Tracker: Reset Usage Statistics`
重置所有使用统计数据，包括：
- 总使用量归零
- 清空每日使用记录
- 更新重置时间戳

#### `Augment Tracker: Open Settings`
快速打开插件配置页面

## 配置选项

### 在设置中搜索 "augmentTracker"

#### `augmentTracker.enabled`
- **类型**：布尔值
- **默认**：true
- **说明**：启用/禁用整个插件功能

#### `augmentTracker.usageLimit`
- **类型**：数字
- **默认**：1000
- **说明**：月度使用限额

#### `augmentTracker.refreshInterval`
- **类型**：数字
- **默认**：30
- **说明**：状态栏刷新间隔（秒）

#### `augmentTracker.showInStatusBar`
- **类型**：布尔值
- **默认**：true
- **说明**：是否在状态栏显示

#### `augmentTracker.clickAction`
- **类型**：枚举
- **选项**：openWebsite | showDetails | openSettings
- **默认**：openWebsite
- **说明**：点击状态栏的行为

## 数据管理

### 数据存储
- 使用VSCode的全局状态存储
- 数据在重启后保持
- 自动清理30天前的历史数据

### 数据结构
```json
{
  "totalUsage": 123,
  "dailyUsage": {
    "2025-06-17": 45,
    "2025-06-16": 32
  },
  "lastResetDate": "2025-06-01T00:00:00.000Z",
  "lastUpdateDate": "2025-06-17T10:30:00.000Z"
}
```

## Augment集成

### 自动检测
插件会自动检测是否安装了官方Augment扩展：
- 检测扩展ID：`augment.vscode-augment`
- 监控扩展状态变化
- 在控制台输出检测结果

### 集成状态
- ✅ **已安装且激活**：尝试集成真实数据
- ⚠️ **已安装未激活**：使用模拟模式
- ❌ **未安装**：完全模拟模式

## 故障排除

### 常见问题

#### 状态栏不显示
1. 检查 `augmentTracker.enabled` 设置
2. 确认 `augmentTracker.showInStatusBar` 为 true
3. 重启VSCode

#### 使用计数不增加
1. 确认插件已激活（查看控制台日志）
2. 检查是否有TypeScript编译错误
3. 尝试重置使用统计

#### 点击状态栏无反应
1. 检查 `augmentTracker.clickAction` 设置
2. 确认网络连接（如果设置为打开网站）
3. 查看开发者控制台错误信息

### 调试模式
1. 打开开发者工具：`Help > Toggle Developer Tools`
2. 查看Console标签页的日志信息
3. 搜索 "Augment Usage Tracker" 相关日志

## 开发和贡献

### 本地开发
```bash
# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 监听模式
npm run watch

# 打包插件
npm run package
```

### 项目结构
```
src/
├── extension.ts      # 主入口文件
├── statusBar.ts      # 状态栏管理
├── usageTracker.ts   # 使用统计
├── storage.ts        # 数据存储
├── config.ts         # 配置管理
└── augmentDetector.ts # Augment检测
```

# 退出功能指南

## 🚪 功能概述

退出功能允许用户完全清空所有认证数据和使用统计，将插件重置为未登录状态。这是一个安全的数据清理功能，适用于需要重新配置认证或完全重置插件状态的场景。

## 🎯 使用方法

### 方法1：通过命令面板
```
Ctrl+Shift+P → "🚪 退出登录"
```

### 方法2：通过使用详情
```
Ctrl+Shift+P → "显示使用详情" → 点击 "🚪 退出登录"
```

### 方法3：通过状态栏（未来版本）
- 右键点击状态栏项目
- 选择 "🚪 退出登录"

## 🔧 功能详情

### 清空的数据
退出功能将清空以下所有数据：

#### 认证信息
- ✅ **VSCode配置中的cookies**: `augment.cookies` 设置
- ✅ **Cookie管理器数据**: 存储的cookie信息和过期时间
- ✅ **首次安装标记**: 重置首次安装状态
- ✅ **API客户端认证**: 清空API令牌和cookies

#### 使用统计
- ✅ **当前使用量**: 重置为0
- ✅ **使用限额**: 重置为默认值(56积分)
- ✅ **每日使用记录**: 清空所有历史记录
- ✅ **最后重置日期**: 更新为当前时间

#### 状态显示
- ✅ **状态栏**: 显示未登录状态
- ✅ **数据源**: 切换为模拟数据
- ✅ **Cookie状态**: 显示未配置

### 操作流程
```
1. 用户触发退出命令
2. 显示确认对话框
3. 用户确认后开始清理
4. 清空所有认证数据
5. 重置使用统计
6. 更新状态栏显示
7. 显示成功消息
```

## 🛡️ 安全确认

### 确认对话框
```
🚪 确定要退出登录吗？

这将清空所有认证数据和使用统计，状态栏将显示未登录状态。

[确定退出] [取消]
```

### 安全特性
- **模态对话框**: 防止意外操作
- **明确说明**: 详细说明操作后果
- **可取消**: 用户可以随时取消操作
- **操作日志**: 详细记录清理过程

## 📊 状态变化

### 退出前状态
```
状态栏: $(pulse) Augment: 7/56 ● (12%)
Cookie状态: ✅ 20小时后过期
数据源: 来自Augment API的真实数据
认证状态: 已配置
```

### 退出后状态
```
状态栏: $(circle-slash) Augment: 未登录
Cookie状态: ❌ 未配置
数据源: 模拟数据
认证状态: 未配置
```

### 使用详情变化
#### 退出前
```
Augment 使用统计:
• 当前使用量: 7 积分
• 月度限额: 56 积分
• 使用百分比: 12%
• 剩余: 49 积分
• 数据源: 来自Augment API的真实数据
• 🍪 Cookie状态: ✅ 20小时后过期

上次重置: 2024-01-01
```

#### 退出后
```
Augment 使用统计:
• 当前使用量: 0 积分
• 月度限额: 56 积分
• 使用百分比: 0%
• 剩余: 56 积分
• 数据源: 模拟数据
• 🍪 Cookie状态: ❌ 未配置

上次重置: 2024-01-02 (刚刚)
```

## 🔄 重新配置

### 退出后的配置选项
退出后，用户可以通过以下方式重新配置：

#### 1. 点击状态栏
- 状态栏显示 "$(circle-slash) Augment: 未登录"
- 点击后自动启动配置流程

#### 2. 使用命令
```
Ctrl+Shift+P → "🌐 网页自动登录"
```

#### 3. 首次安装向导
- 退出后会重置首次安装标记
- 下次启动VSCode时会显示欢迎向导

## 🔍 技术实现

### 核心函数
```typescript
async function clearAllAuthData() {
    // 清空VSCode配置
    await vscode.workspace.getConfiguration()
        .update('augment.cookies', '', vscode.ConfigurationTarget.Global);
    
    // 清空Cookie管理器
    if (cookieManager) {
        cookieManager.clearAllData();
    }
    
    // 清空API客户端
    const apiClient = (augmentDetector as any).apiClient;
    if (apiClient) {
        apiClient.clearAuthToken?.();
        apiClient.clearCookies?.();
    }
    
    // 重置存储数据
    if (storageManager) {
        await storageManager.resetUsageData();
    }
}
```

### 状态栏更新
```typescript
updateLogoutStatus() {
    this.statusBarItem.text = '$(circle-slash) Augment: 未登录';
    this.statusBarItem.tooltip = 'Augment 使用量追踪器\n状态: 未登录\n点击配置认证';
    this.statusBarItem.command = 'augmentTracker.webLogin';
    this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
    this.statusBarItem.show();
}
```

### Cookie管理器清理
```typescript
clearAllData(): void {
    // 清空globalState中的cookie信息
    this.context.globalState.update(this.COOKIE_STORAGE_KEY, undefined);
    this.context.globalState.update(this.FIRST_INSTALL_KEY, undefined);
    
    // 停止定期检查
    if (this.checkTimer) {
        clearInterval(this.checkTimer);
        this.checkTimer = null;
    }
}
```

## 🎨 用户界面

### 命令列表更新
退出功能添加后，完整的命令列表：

```
基础功能 (6个):
├── 重置使用统计
├── 打开设置
├── 显示使用详情 (包含退出选项)
├── 设置浏览器Cookie
├── 检查认证状态
└── 🌐 网页自动登录

手动功能 (2个):
├── 🔄 手动刷新
└── 🌐 设置语言

Cookie管理 (2个):
├── 🍪 检查Cookie状态
└── 🔄 刷新Cookie

退出功能 (1个) - 新增:
└── 🚪 退出登录
```

### 多语言支持
```json
// 英文
"command.logout": "🚪 Logout"

// 中文
"command.logout": "🚪 退出登录"
```

## 📝 使用场景

### 1. 重新配置认证
**场景**: Cookie过期，需要重新配置
**操作**: 退出登录 → 重新配置认证

### 2. 切换账户
**场景**: 需要使用不同的Augment账户
**操作**: 退出登录 → 使用新账户登录

### 3. 故障排除
**场景**: 认证出现问题，数据异常
**操作**: 退出登录 → 清理状态 → 重新配置

### 4. 隐私保护
**场景**: 在共享设备上使用后清理数据
**操作**: 退出登录 → 确保数据清空

### 5. 插件重置
**场景**: 插件状态异常，需要完全重置
**操作**: 退出登录 → 重启VSCode → 重新配置

## ⚠️ 注意事项

### 数据丢失警告
- **不可恢复**: 退出后的数据无法恢复
- **历史记录**: 所有使用历史将被清空
- **配置信息**: 需要重新配置所有认证信息

### 最佳实践
1. **确认操作**: 仔细阅读确认对话框
2. **备份重要信息**: 如有需要，记录当前使用统计
3. **网络环境**: 确保在稳定网络环境下重新配置
4. **账户信息**: 准备好Augment账户登录信息

### 故障排除
#### 退出失败
**可能原因**: 权限问题、文件锁定
**解决方案**: 重启VSCode后重试

#### 状态栏未更新
**可能原因**: 显示缓存问题
**解决方案**: 手动刷新或重启VSCode

#### 重新配置失败
**可能原因**: 网络问题、认证服务异常
**解决方案**: 检查网络连接，稍后重试

## 🔄 版本更新

### v1.3.0 新增功能
- ✅ 完整的退出功能
- ✅ 安全确认机制
- ✅ 状态栏未登录显示
- ✅ 多语言支持

### 改进内容
- 🔧 更安全的数据清理
- 🎨 更友好的用户界面
- 🛡️ 更强的确认机制
- 📱 更好的状态反馈

---

**安全地管理您的认证状态，享受灵活的配置体验！** 🚪✨

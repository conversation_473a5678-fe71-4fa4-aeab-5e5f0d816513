// Cookie验证和测试工具
const https = require('https');

// 测试cookie是否有效
async function validate<PERSON><PERSON>ie(cookieString) {
    console.log('🍪 验证Cookie有效性...');
    console.log(`Cookie长度: ${cookieString.length} 字符`);
    
    // 检查关键cookie是否存在
    const requiredCookies = ['_session', 'ajs_user_id'];
    const missingCookies = [];
    
    requiredCookies.forEach(cookieName => {
        if (!cookieString.includes(cookieName + '=')) {
            missingCookies.push(cookieName);
        }
    });
    
    if (missingCookies.length > 0) {
        console.log(`❌ 缺少必需的cookie: ${missingCookies.join(', ')}`);
        return false;
    }
    
    console.log('✅ Cookie格式检查通过');
    
    // 测试API调用
    return new Promise((resolve) => {
        const options = {
            hostname: 'app.augmentcode.com',
            path: '/api/user',
            method: 'GET',
            headers: {
                'Cookie': cookieString,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0',
                'Accept': '*/*',
                'Referer': 'https://app.augmentcode.com/account/subscription',
                'Sec-Fetch-Site': 'same-origin'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`API测试结果: ${res.statusCode}`);
                
                if (res.statusCode === 200) {
                    try {
                        const json = JSON.parse(data);
                        console.log('✅ Cookie有效！用户信息:');
                        console.log(`   邮箱: ${json.email}`);
                        console.log(`   计划: ${json.plan?.name}`);
                        console.log(`   管理员: ${json.isAdmin ? '是' : '否'}`);
                        resolve(true);
                    } catch (e) {
                        console.log('❌ 响应解析失败');
                        resolve(false);
                    }
                } else if (res.statusCode === 401) {
                    console.log('❌ Cookie已过期或无效');
                    resolve(false);
                } else {
                    console.log(`❌ API调用失败: ${res.statusCode}`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`❌ 网络错误: ${err.message}`);
            resolve(false);
        });
        
        req.end();
    });
}

// 从命令行参数获取cookie或使用示例
const cookieFromArgs = process.argv[2];

if (cookieFromArgs) {
    console.log('🧪 测试提供的Cookie...');
    validateCookie(cookieFromArgs);
} else {
    console.log('📋 Cookie验证工具使用方法:');
    console.log('');
    console.log('1. 获取Cookie:');
    console.log('   - 访问 https://app.augmentcode.com');
    console.log('   - 登录您的账户');
    console.log('   - 按F12打开开发者工具');
    console.log('   - Application → Cookies → app.augmentcode.com');
    console.log('   - 复制所有cookie值');
    console.log('');
    console.log('2. 测试Cookie:');
    console.log('   node cookie-validator.js "your-cookie-string"');
    console.log('');
    console.log('3. 在插件中使用:');
    console.log('   Ctrl+Shift+P → "Setup Browser Cookies"');
    console.log('');
    console.log('💡 提示: Cookie通常在24小时后过期，需要定期更新');
}

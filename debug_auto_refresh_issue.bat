@echo off
chcp 65001 > nul
echo ========================================
echo   调试Cookie配置后自动刷新问题
echo ========================================
echo.

echo 🔍 问题分析:
echo 用户反馈配置cookies成功后状态栏仍然不自动刷新。
echo 我们已经添加了详细的调试日志来定位问题。
echo.

echo 🛠️ 调试增强:
echo [1] 添加详细的控制台日志输出
echo [2] 验证hasRealData标志状态
echo [3] 检查currentUsage和currentLimit值
echo [4] 直接调用updateDisplay而不通过updateAugmentStatus
echo [5] 添加延迟刷新确保状态栏更新
echo [6] 等待配置保存完成（500ms延迟）
echo.

echo [1/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [2/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [3/4] 发布调试版本...
set /p confirm="确定要发布调试版本吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo ❌ 发布失败
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo [4/4] 生成调试指南...

echo ========================================
echo   调试版本测试指南
echo ========================================
echo.

echo 📋 测试步骤:
echo.
echo 1. 打开VSCode开发者控制台
echo    - 按 Ctrl+Shift+I 或 F12
echo    - 切换到 Console 标签页
echo.
echo 2. 运行 setupCookies 命令
echo    - 按 Ctrl+Shift+P
echo    - 输入 "Setup Browser Cookies"
echo    - 输入有效的cookies
echo.
echo 3. 观察控制台输出
echo    查找以下日志信息:
echo    🔄 [setupCookies] 开始获取数据...
echo    🔐 [setupCookies] 认证状态: true/false
echo    📊 [setupCookies] Credits结果: true/false
echo    👤 [setupCookies] 用户结果: true/false
echo    📈 [setupCookies] 解析的使用数据: {...}
echo    ✅ [setupCookies] UsageTracker已更新
echo    📊 [setupCookies] hasRealData: true/false
echo    📊 [setupCookies] currentUsage: 数字
echo    📊 [setupCookies] currentLimit: 数字
echo    👤 [setupCookies] 用户信息已更新: 邮箱
echo    🔄 [setupCookies] 直接刷新状态栏
echo    🔄 [setupCookies] 延迟刷新状态栏
echo.

echo 4. 检查状态栏显示
echo    - 应该显示使用数据而不是"未登录"
echo    - 如果有用户信息，应该显示邮箱前缀
echo.

echo ========================================
echo   可能的问题点分析
echo ========================================
echo.

echo 🔍 问题1: 认证状态检查失败
echo 症状: 🔐 [setupCookies] 认证状态: false
echo 原因: setCookies方法可能没有正确保存cookies
echo 解决: 检查augmentApi.ts的setCookies实现
echo.

echo 🔍 问题2: API调用失败
echo 症状: 📊 [setupCookies] Credits结果: false
echo 原因: cookies无效或API请求失败
echo 解决: 检查cookies格式和API端点
echo.

echo 🔍 问题3: 数据解析失败
echo 症状: 📈 [setupCookies] 解析的使用数据: null
echo 原因: API响应格式不匹配
echo 解决: 检查parseUsageResponse方法
echo.

echo 🔍 问题4: hasRealData标志未设置
echo 症状: 📊 [setupCookies] hasRealData: false
echo 原因: updateWithRealData方法执行失败
echo 解决: 检查usageTracker.ts的updateWithRealData
echo.

echo 🔍 问题5: 状态栏更新逻辑问题
echo 症状: 所有数据正确但状态栏不更新
echo 原因: updateDisplay方法的条件判断问题
echo 解决: 检查statusBar.ts的updateDisplay方法
echo.

echo ========================================
echo   调试数据收集
echo ========================================
echo.

echo 📊 请收集以下信息:
echo.
echo 1. 控制台完整日志输出
echo 2. 配置前后的状态栏显示
echo 3. hasRealData、currentUsage、currentLimit的值
echo 4. 用户信息是否正确获取
echo 5. 是否有任何错误信息
echo.

echo 📋 常见问题排查:
echo.
echo Q: 认证状态为false
echo A: 检查cookies格式，确保包含_session=
echo.
echo Q: API调用失败
echo A: 检查网络连接和cookies有效性
echo.
echo Q: 数据解析为null
echo A: 检查API响应格式是否匹配预期
echo.
echo Q: hasRealData为false
echo A: 检查updateWithRealData是否正确执行
echo.
echo Q: 状态栏仍显示"未登录"
echo A: 检查updateDisplay方法的条件判断
echo.

echo ========================================
echo   下一步修复方向
echo ========================================
echo.

echo 根据调试结果，可能的修复方向:
echo.
echo 1. 如果认证状态检查失败
echo    - 修复setCookies方法
echo    - 增加配置保存确认
echo.
echo 2. 如果API调用失败
echo    - 检查API端点和请求格式
echo    - 增加重试机制
echo.
echo 3. 如果数据解析失败
echo    - 更新parseUsageResponse方法
echo    - 增加更多API响应格式支持
echo.
echo 4. 如果状态栏更新失败
echo    - 修改updateDisplay条件判断
echo    - 增加强制更新机制
echo.

echo 🔧 调试版本已准备就绪！
echo.
echo 请按照测试指南进行测试，并收集控制台日志信息。
echo 这将帮助我们精确定位问题所在并提供针对性修复。
echo.

pause

{"displayName": "Augment 使用量追踪器", "description": "在VSCode状态栏追踪和显示Augment AI使用统计", "command.resetUsage": "重置使用统计", "command.openSettings": "打开设置", "command.showDetails": "显示使用详情", "command.setupCookies": "设置浏览器Cookie", "command.checkAuthStatus": "检查认证状态", "command.webLogin": "🌐 网页自动登录", "command.manualRefresh": "🔄 手动刷新", "command.setLanguage": "🌐 设置语言", "command.checkCookieStatus": "🍪 检查Cookie状态", "command.refreshCookie": "🔄 刷新Cookie", "command.logout": "🚪 退出登录", "config.title": "Augment 使用量追踪器", "config.enabled": "启用/禁用 Augment 使用量追踪器", "config.usageLimit": "月度使用限额", "config.refreshInterval": "状态栏刷新间隔（秒）", "config.showInStatusBar": "在状态栏显示使用统计", "config.cookies": "Augment 浏览器会话Cookie", "config.language": "界面语言设置", "config.language.auto": "自动（跟随VSCode）", "config.language.en": "英文", "config.language.zhCn": "简体中文", "status.noAuth": "没有可用的认证信息获取真实数据", "status.fetchingData": "正在获取真实使用数据...", "status.apiSuccess": "API连接成功！", "status.apiFailed": "API连接失败", "status.cookiesConfigured": "🍪 Augment cookies 配置成功！", "status.checkingAuth": "🔍 检查认证状态...", "status.authStatus": "🔐 认证状态:", "status.apiToken": "API Token", "status.browserCookies": "Browser Cookies", "status.configured": "✅ 已配置", "status.notConfigured": "❌ 未配置", "status.connectionTest": "连接测试", "status.success": "✅ 成功", "status.failed": "❌ 失败", "status.error": "错误", "status.suggestion": "💡 建议: <PERSON><PERSON>可能已过期，请重新获取", "status.pleaseConfigureAuth": "💡 请先配置认证信息", "dialog.browserOpened": "🌐 浏览器已打开！请登录Augment，然后使用\"设置浏览器Cookie\"命令。", "dialog.setupCookies": "设置<PERSON><PERSON>", "dialog.cancel": "取消", "dialog.webLoginError": "❌ 网页登录错误", "usage.currentUsage": "当前使用量", "usage.monthlyLimit": "月度限额", "usage.usagePercentage": "使用百分比", "usage.remaining": "剩余", "usage.lastReset": "上次重置", "usage.resetUsage": "重置使用量", "usage.openSettings": "打开设置", "tooltip.augmentUsageTracker": "Augment 使用量追踪器", "tooltip.current": "当前", "tooltip.limit": "限额", "tooltip.usage": "使用量", "tooltip.remaining": "剩余", "tooltip.plan": "计划", "tooltip.dataSource": "数据源", "tooltip.realDataFromApi": "来自Augment API的真实数据", "tooltip.simulatedData": "模拟数据"}
@echo off
echo Fixing README.md encoding issue...

REM Delete corrupted README.md if exists
if exist README.md (
    echo Deleting corrupted README.md...
    del /f README.md
)

REM Copy the new correct README
if exist README_NEW.md (
    echo Copying new README...
    copy README_NEW.md README.md
    echo README.md has been fixed!
) else (
    echo ERROR: README_NEW.md not found!
)

echo Done!
pause

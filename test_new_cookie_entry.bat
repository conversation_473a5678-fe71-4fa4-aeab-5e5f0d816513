@echo off
chcp 65001 > nul
echo ========================================
echo   🧪 新Cookie入口功能测试
echo ========================================
echo.

echo 🎯 测试目标:
echo • 验证新的统一入口功能
echo • 测试Webview页面显示
echo • 验证Cookie验证和解析
echo • 测试错误处理机制
echo • 确认状态反馈功能
echo.

echo [1/5] 编译测试版本...
call npx tsc
if errorlevel 1 (
    echo ❌ 编译失败，需要修复代码
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo [2/5] 打包测试版本...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包成功

echo.
echo [3/5] 功能测试清单...

echo ========================================
echo   📋 测试清单
echo ========================================
echo.

echo 🧪 基础功能测试:
echo.

echo ✅ 测试1: 命令注册
echo • 测试项: 检查命令是否正确注册
echo • 验证方法: Ctrl+Shift+P → 搜索 "🍪 超简单Cookie配置"
echo • 预期结果: 命令出现在列表中
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试2: Webview页面显示
echo • 测试项: 检查配置页面是否正确显示
echo • 验证方法: 执行命令后查看弹出的页面
echo • 预期结果: 显示完整的配置界面
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试3: 主题适配
echo • 测试项: 检查页面是否适配VSCode主题
echo • 验证方法: 切换深色/浅色主题查看效果
echo • 预期结果: 页面颜色自动适配主题
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试4: 帮助功能
echo • 测试项: 检查帮助按钮是否正常工作
echo • 验证方法: 点击"📖 查看详细指导"按钮
echo • 预期结果: 在新标签页显示详细指导
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试5: 外部链接
echo • 测试项: 检查打开网站功能
echo • 验证方法: 点击"🔗 打开网站"按钮
echo • 预期结果: 在浏览器中打开app.augmentcode.com
echo • 测试状态: [ ] 待测试
echo.

echo 🧪 Cookie验证测试:
echo.

echo ✅ 测试6: 空输入验证
echo • 测试项: 提交空的Cookie输入
echo • 验证方法: 不输入任何内容直接点击配置
echo • 预期结果: 显示"❌ Cookie不能为空"
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试7: 无效格式验证
echo • 测试项: 提交无效的Cookie格式
echo • 验证方法: 输入"invalid_cookie"并提交
echo • 预期结果: 显示格式错误提示
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试8: 完整Cookie格式
echo • 测试项: 提交完整的Cookie字符串
echo • 验证方法: 输入"_session=eyJhbGciOiJIUzI1NiJ9..."
echo • 预期结果: 正确解析并提取session值
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试9: 单独session值
echo • 测试项: 提交单独的session值
echo • 验证方法: 输入"eyJhbGciOiJIUzI1NiJ9..."
echo • 预期结果: 正确识别并格式化为完整Cookie
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试10: JWT解析
echo • 测试项: 解析JWT格式的session
echo • 验证方法: 提交有效的JWT token
echo • 预期结果: 正确解析用户信息
echo • 测试状态: [ ] 待测试
echo.

echo 🧪 状态反馈测试:
echo.

echo ✅ 测试11: 进度显示
echo • 测试项: 配置过程中的进度提示
echo • 验证方法: 提交有效Cookie观察状态变化
echo • 预期结果: 显示"🔄 正在配置Cookie..."
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试12: 成功反馈
echo • 测试项: 配置成功后的反馈
echo • 验证方法: 使用有效Cookie完成配置
echo • 预期结果: 显示"✅ Cookie配置成功！"
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试13: 错误反馈
echo • 测试项: 配置失败后的错误信息
echo • 验证方法: 使用无效Cookie尝试配置
echo • 预期结果: 显示具体的错误信息
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试14: 自动关闭
echo • 测试项: 成功后页面自动关闭
echo • 验证方法: 完成成功配置后等待
echo • 预期结果: 2秒后页面自动关闭
echo • 测试状态: [ ] 待测试
echo.

echo 🧪 集成测试:
echo.

echo ✅ 测试15: API集成
echo • 测试项: 与API客户端的集成
echo • 验证方法: 配置后检查API调用
echo • 预期结果: 成功调用API获取数据
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试16: 状态栏更新
echo • 测试项: 配置后状态栏更新
echo • 验证方法: 配置成功后查看状态栏
echo • 预期结果: 状态栏显示真实使用数据
echo • 测试状态: [ ] 待测试
echo.

echo ✅ 测试17: 数据持久化
echo • 测试项: 配置数据的保存
echo • 验证方法: 重启VSCode后检查配置
echo • 预期结果: 配置信息正确保存
echo • 测试状态: [ ] 待测试
echo.

echo [4/5] 手动测试指导...

echo ========================================
echo   📱 手动测试步骤
echo ========================================
echo.

echo 🎯 请按照以下步骤进行手动测试:
echo.

echo 步骤1: 基础功能测试
echo 1. 在VSCode中按 Ctrl+Shift+P
echo 2. 输入 "🍪 超简单Cookie配置"
echo 3. 确认命令出现并可以执行
echo 4. 检查是否弹出配置页面
echo 5. 验证页面布局和样式
echo.

echo 步骤2: 界面交互测试
echo 1. 点击"📖 查看详细指导"按钮
echo 2. 确认在新标签页显示指导文档
echo 3. 点击"🔗 打开网站"按钮
echo 4. 确认在浏览器中打开Augment网站
echo 5. 测试文本框输入功能
echo.

echo 步骤3: 验证功能测试
echo 1. 不输入任何内容，点击"✅ 配置Cookie"
echo 2. 确认显示空输入错误提示
echo 3. 输入无效内容（如"test"），点击配置
echo 4. 确认显示格式错误提示
echo 5. 输入有效的session值，观察验证过程
echo.

echo 步骤4: 状态反馈测试
echo 1. 提交有效Cookie
echo 2. 观察状态变化：配置中 → 验证中 → 成功/失败
echo 3. 检查错误情况下的详细提示
echo 4. 验证成功情况下的自动关闭
echo.

echo 步骤5: 集成功能测试
echo 1. 使用真实的Augment Cookie进行配置
echo 2. 检查API调用是否成功
echo 3. 验证状态栏是否更新
echo 4. 确认数据是否正确保存
echo.

echo [5/5] 测试结果记录...

echo ========================================
echo   📊 测试结果记录模板
echo ========================================
echo.

echo 请将测试结果记录在以下格式中:
echo.

echo 测试日期: [填写日期]
echo 测试环境: [VSCode版本 + 操作系统]
echo 测试人员: [测试人员姓名]
echo.

echo 基础功能测试:
echo [ ] 测试1: 命令注册 - 通过/失败 - [备注]
echo [ ] 测试2: Webview页面显示 - 通过/失败 - [备注]
echo [ ] 测试3: 主题适配 - 通过/失败 - [备注]
echo [ ] 测试4: 帮助功能 - 通过/失败 - [备注]
echo [ ] 测试5: 外部链接 - 通过/失败 - [备注]
echo.

echo Cookie验证测试:
echo [ ] 测试6: 空输入验证 - 通过/失败 - [备注]
echo [ ] 测试7: 无效格式验证 - 通过/失败 - [备注]
echo [ ] 测试8: 完整Cookie格式 - 通过/失败 - [备注]
echo [ ] 测试9: 单独session值 - 通过/失败 - [备注]
echo [ ] 测试10: JWT解析 - 通过/失败 - [备注]
echo.

echo 状态反馈测试:
echo [ ] 测试11: 进度显示 - 通过/失败 - [备注]
echo [ ] 测试12: 成功反馈 - 通过/失败 - [备注]
echo [ ] 测试13: 错误反馈 - 通过/失败 - [备注]
echo [ ] 测试14: 自动关闭 - 通过/失败 - [备注]
echo.

echo 集成测试:
echo [ ] 测试15: API集成 - 通过/失败 - [备注]
echo [ ] 测试16: 状态栏更新 - 通过/失败 - [备注]
echo [ ] 测试17: 数据持久化 - 通过/失败 - [备注]
echo.

echo 总体评价:
echo 通过率: [X/17]
echo 主要问题: [列出主要问题]
echo 改进建议: [列出改进建议]
echo 发布建议: [是否建议发布]
echo.

echo ========================================
echo   🎉 测试准备完成
echo ========================================
echo.

echo ✅ 新Cookie入口功能测试准备完成！
echo.

echo 📋 测试要点:
echo • 17个详细测试用例
echo • 覆盖基础功能、验证、反馈、集成
echo • 完整的手动测试指导
echo • 标准化的结果记录模板
echo.

echo 🎯 测试重点:
echo • Webview页面正确显示
echo • Cookie验证和解析功能
echo • 状态反馈和错误处理
echo • 与现有系统的集成
echo.

echo 🚀 开始测试:
echo 1. 安装编译好的插件
echo 2. 按照测试步骤逐项验证
echo 3. 记录测试结果
echo 4. 根据结果决定是否发布
echo.

echo 现在可以开始全面测试新的Cookie获取入口功能！
echo.

pause

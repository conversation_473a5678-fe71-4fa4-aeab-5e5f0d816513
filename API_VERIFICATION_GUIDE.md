# API调用验证指南

## 🔍 如何确认接口调用成功

### 方法1：使用插件内置调试命令 ⭐推荐

#### 步骤1：启动插件
1. 在VSCode中按 `F5` 启动Extension Development Host
2. 等待插件加载完成

#### 步骤2：使用调试命令
```
Ctrl+Shift+P → "Augment Tracker: Debug API Calls"
```

选择要测试的API端点：
- 🏥 **Health Check** - 测试基础连接（无需token）
- 👤 **User Info** - 获取用户信息（需要token）
- 📊 **Usage Data** - 获取使用统计（需要token）
- 💳 **Subscription** - 获取订阅信息（需要token）

#### 步骤3：查看详细日志
1. 打开开发者控制台：`Help > Toggle Developer Tools`
2. 切换到 `Console` 标签页
3. 查看详细的API调用日志

### 方法2：查看开发者控制台日志

#### 详细日志格式
每次API调用都会产生以下日志：

```javascript
🌐 Augment API Request: {
  url: "https://i1.api.augmentcode.com/usage",
  method: "GET",
  headers: { "Content-Type": "application/json", "Authorization": "Bearer [HIDDEN]" },
  timestamp: "2025-01-17T10:30:00.000Z"
}

📡 Augment API Response: {
  url: "https://i1.api.augmentcode.com/usage",
  status: 200,
  statusText: "OK",
  duration: "245ms",
  headers: { "content-type": "application/json", "content-length": "156" },
  timestamp: "2025-01-17T10:30:00.245Z"
}

✅ API request successful: {
  url: "https://i1.api.augmentcode.com/usage",
  dataKeys: ["totalUsage", "usageLimit", "dailyUsage"],
  dataSize: 156,
  timestamp: "2025-01-17T10:30:00.245Z"
}
```

#### 错误日志格式
如果API调用失败，会看到：

```javascript
❌ API request failed: {
  status: 401,
  statusText: "Unauthorized",
  url: "https://i1.api.augmentcode.com/usage"
}

🔒 Authentication failed - token may be invalid or expired

🚨 Network error: {
  endpoint: "/usage",
  error: "Failed to fetch",
  stack: "TypeError: Failed to fetch...",
  timestamp: "2025-01-17T10:30:00.000Z"
}
```

### 方法3：使用网络监控工具

#### 在Chrome DevTools中监控
1. 打开Extension Development Host
2. 按 `F12` 打开DevTools
3. 切换到 `Network` 标签页
4. 过滤器中输入 `augmentcode.com`
5. 执行API相关操作
6. 查看网络请求详情

#### 预期的网络请求
- **URL**: `https://i1.api.augmentcode.com/*`
- **Method**: GET/POST
- **Headers**: 包含 `Authorization: Bearer [token]`
- **Status**: 200 (成功) 或 401 (认证失败)

### 方法4：状态栏指示器验证

#### 数据源指示器
- **● (实心圆)**：表示使用真实API数据
- **○ (空心圆)**：表示使用模拟数据

#### 工具提示信息
鼠标悬停在状态栏上查看详细信息：
```
Augment Usage Tracker
Current: 123
Limit: 1000
Usage: 12%
Remaining: 877
Data Source: Real data from Augment API  ← 确认数据源

Augment Plugin:
• Installed: Yes
• Active: Yes
• Version: 1.2.3
• Integration: api  ← 确认集成方式
• API Status: Connected  ← 确认API状态
```

## 🧪 完整测试流程

### 测试1：健康检查（无需token）
```
1. Ctrl+Shift+P → "Augment Tracker: Debug API Calls"
2. 选择 "🏥 Health Check"
3. 查看控制台日志
4. 预期结果：status: 200, data: { status: 'healthy' }
```

### 测试2：配置API Token
```
1. Ctrl+Shift+P → "Augment Tracker: Setup API Token"
2. 输入有效的Bearer token
3. 查看成功提示
```

### 测试3：测试认证
```
1. Ctrl+Shift+P → "Augment Tracker: Test API Connection"
2. 查看连接结果
3. 预期结果：✅ API connection successful
```

### 测试4：获取使用数据
```
1. Ctrl+Shift+P → "Augment Tracker: Debug API Calls"
2. 选择 "📊 Usage Data"
3. 查看控制台详细日志
4. 检查状态栏是否变为 ● (实心圆)
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 网络连接失败
**症状**: `🚨 Network error: Failed to fetch`
**解决**: 
- 检查网络连接
- 确认防火墙设置
- 验证API服务器状态

#### 2. 认证失败
**症状**: `🔒 Authentication failed - token may be invalid`
**解决**:
- 重新生成API token
- 检查token格式
- 确认账户权限

#### 3. 数据解析错误
**症状**: API返回200但数据为空
**解决**:
- 检查API响应格式
- 验证数据解析逻辑
- 查看完整响应内容

#### 4. 状态栏不更新
**症状**: 仍显示 ○ (模拟数据)
**解决**:
- 重新启动插件
- 清除缓存重新检测
- 检查API调用是否成功

## 📊 成功指标

### API调用成功的标志
✅ 控制台显示 `✅ API request successful`
✅ 状态栏显示 ● (实心圆)
✅ 工具提示显示 "Real data from Augment API"
✅ 网络标签页显示200状态码
✅ 使用数据实时更新

### 完整集成成功的标志
✅ 所有API端点测试通过
✅ 数据源自动切换到API
✅ 使用统计实时同步
✅ 错误处理正常工作
✅ 用户体验流畅

通过以上方法，您可以完全确认API接口是否被正确调用和集成。

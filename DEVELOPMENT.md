# 开发指南 | Development Guide

## 快速开始 | Quick Start

### 环境要求 | Prerequisites
- Node.js 16+
- VSCode 1.74.0+
- TypeScript 4.9+

### 安装依赖 | Install Dependencies
```bash
npm install
```

### 开发命令 | Development Commands

#### 编译 | Compile
```bash
# 单次编译 | Compile once
npm run compile

# 监视模式 | Watch mode
npm run watch
# 或 | or
npm run dev
```

#### 运行和测试 | Run & Test
```bash
# 方法1：VSCode调试 | Method 1: VSCode Debug
# 按F5启动扩展开发主机 | Press F5 to launch Extension Development Host

# 方法2：打包安装 | Method 2: Package & Install
npm run build
code --install-extension augment-usage-tracker-1.0.0.vsix
```

#### 打包 | Package
```bash
# 仅打包 | Package only
npm run package

# 编译并打包 | Compile and package
npm run build

# 清理输出 | Clean output
npm run clean
```

## 项目结构 | Project Structure

```
augment-status/
├── src/                    # TypeScript源码 | TypeScript source
│   ├── extension.ts        # 主入口 | Main entry
│   ├── augmentApi.ts      # API客户端 | API client
│   ├── augmentDetector.ts # Augment检测 | Detection
│   ├── statusBar.ts       # 状态栏 | Status bar
│   ├── usageTracker.ts    # 使用追踪 | Usage tracking
│   ├── storage.ts         # 数据存储 | Data storage
│   ├── config.ts          # 配置管理 | Configuration
│   └── webAuth.ts         # 网页认证 | Web auth
├── out/                   # 编译输出 | Compiled output
├── package.json           # 扩展清单 | Extension manifest
├── tsconfig.json          # TS配置 | TypeScript config
├── README.md              # 说明文档 | Documentation
└── .vscodeignore          # 打包忽略 | Package ignore
```

## 开发流程 | Development Workflow

### 1. 修改代码 | Modify Code
编辑 `src/` 目录下的TypeScript文件

### 2. 编译 | Compile
```bash
npm run compile
# 或使用监视模式 | or use watch mode
npm run watch
```

### 3. 测试 | Test
按F5启动扩展开发主机，在新窗口中测试功能

### 4. 打包 | Package
```bash
npm run package
```

## 调试技巧 | Debugging Tips

### VSCode调试 | VSCode Debugging
1. 在代码中添加断点 | Add breakpoints in code
2. 按F5启动调试 | Press F5 to start debugging
3. 在新窗口中触发功能 | Trigger features in new window
4. 查看调试控制台 | Check Debug Console

### 控制台日志 | Console Logging
```typescript
console.log('🔄 Debug info:', data);
console.error('❌ Error:', error);
```

### 常见问题 | Common Issues

#### 编译错误 | Compilation Errors
```bash
# 清理并重新编译 | Clean and recompile
npm run clean
npm run compile
```

#### 扩展未加载 | Extension Not Loading
1. 检查package.json语法 | Check package.json syntax
2. 确保main字段正确 | Ensure main field is correct
3. 验证编译成功 | Verify compilation success

#### API连接问题 | API Connection Issues
1. 检查认证设置 | Check authentication
2. 验证Cookie有效性 | Verify cookie validity
3. 使用"检查认证状态"命令 | Use "Check Auth Status" command

## 发布流程 | Release Process

### 1. 更新版本 | Update Version
```bash
# 更新package.json中的version字段
# Update version field in package.json
```

### 2. 编译和打包 | Compile & Package
```bash
npm run build
```

### 3. 测试 | Test
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

### 4. 分发 | Distribute
分享生成的.vsix文件 | Share the generated .vsix file

## 贡献指南 | Contributing

### 贡献流程 | Contribution Process
1. Fork项目 | Fork the project
2. 创建功能分支 | Create feature branch: `git checkout -b feature/amazing-feature`
3. 提交更改 | Commit changes: `git commit -m 'Add amazing feature'`
4. 推送到分支 | Push to branch: `git push origin feature/amazing-feature`
5. 创建Pull Request | Create Pull Request

### 代码规范 | Code Standards
- 使用TypeScript | Use TypeScript
- 遵循ESLint规则 | Follow ESLint rules
- 添加适当的注释 | Add appropriate comments
- 保持代码简洁 | Keep code clean and simple

### 提交规范 | Commit Convention
```
feat: 新功能 | new feature
fix: 修复bug | bug fix
docs: 文档更新 | documentation update
style: 代码格式 | code formatting
refactor: 重构 | refactoring
test: 测试 | testing
chore: 构建/工具 | build/tools
```

## 许可证 | License

MIT License

### 许可证详情 | License Details
本项目采用MIT许可证，这意味着：
This project is licensed under MIT License, which means:

- ✅ 可以自由使用、修改和分发 | Free to use, modify and distribute
- ✅ 可以用于商业项目 | Can be used in commercial projects
- ✅ 可以创建衍生作品 | Can create derivative works
- ❌ 不提供任何保证 | No warranty provided
- ❌ 作者不承担责任 | Authors not liable

详情请查看 [LICENSE](LICENSE) 文件。
For details, see the [LICENSE](LICENSE) file.

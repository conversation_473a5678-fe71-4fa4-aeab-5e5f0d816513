import * as vscode from 'vscode';

export interface AugmentStatus {
    installed: boolean;
    active: boolean;
    version?: string;
    hasRealData: boolean;
    usageData?: {
        totalUsage?: number;
        dailyUsage?: number;
        lastUpdate?: string;
    };
    integrationMethod?: 'none' | 'detection' | 'user_input' | 'api';
}

export class AugmentDetector {
    private readonly AUGMENT_EXTENSION_ID = 'augment.vscode-augment';
    private lastDetectionTime: number = 0;
    private cachedStatus: AugmentStatus | null = null;
    private readonly CACHE_DURATION = 30000; // 30 seconds

    async detectAugmentPlugin(): Promise<boolean> {
        const status = await this.getAugmentStatus();
        return status.installed && status.active;
    }

    async getAugmentExtension(): Promise<vscode.Extension<any> | undefined> {
        return vscode.extensions.getExtension(this.AUGMENT_EXTENSION_ID);
    }

    async isAugmentActive(): Promise<boolean> {
        const extension = await this.getAugmentExtension();
        return extension?.isActive || false;
    }

    async getAugmentVersion(): Promise<string | undefined> {
        const extension = await this.getAugmentExtension();
        return extension?.packageJSON?.version;
    }

    async getAugmentStatus(): Promise<AugmentStatus> {
        // Use cached result if still valid
        const now = Date.now();
        if (this.cachedStatus && (now - this.lastDetectionTime) < this.CACHE_DURATION) {
            return this.cachedStatus;
        }

        const extension = await this.getAugmentExtension();
        const installed = extension !== undefined;
        const active = extension?.isActive || false;
        const version = extension?.packageJSON?.version;

        let hasRealData = false;
        let usageData: AugmentStatus['usageData'] = undefined;
        let integrationMethod: AugmentStatus['integrationMethod'] = 'none';

        if (installed && active) {
            // Try to get real usage data through various methods
            const realDataResult = await this.tryGetRealUsageData();
            hasRealData = realDataResult.success;
            usageData = realDataResult.data;
            integrationMethod = realDataResult.method;
        }

        const status: AugmentStatus = {
            installed,
            active,
            version,
            hasRealData,
            usageData,
            integrationMethod
        };

        // Cache the result
        this.cachedStatus = status;
        this.lastDetectionTime = now;

        return status;
    }

    // Monitor Augment extension state changes
    onAugmentStateChange(callback: (status: AugmentStatus) => void): vscode.Disposable {
        return vscode.extensions.onDidChange(async () => {
            // Clear cache to force fresh detection
            this.cachedStatus = null;
            const status = await this.getAugmentStatus();
            callback(status);
        });
    }

    // Try to get real usage data through various safe methods
    private async tryGetRealUsageData(): Promise<{
        success: boolean;
        data?: AugmentStatus['usageData'];
        method: AugmentStatus['integrationMethod'];
    }> {
        // Method 1: Try to call public Augment commands
        try {
            const commands = await vscode.commands.getCommands();
            const augmentCommands = commands.filter(cmd => cmd.startsWith('augment.'));

            if (augmentCommands.length > 0) {
                // Try to get status through public commands
                for (const command of ['augment.getStatus', 'augment.status', 'augment.info']) {
                    try {
                        const result = await vscode.commands.executeCommand(command);
                        if (result && typeof result === 'object') {
                            return {
                                success: true,
                                data: this.parseAugmentResult(result),
                                method: 'api'
                            };
                        }
                    } catch {
                        // Command doesn't exist or failed, try next
                        continue;
                    }
                }
            }
        } catch (error) {
            console.log('Could not access Augment commands:', error);
        }

        // Method 2: Monitor workspace configuration changes
        try {
            const config = vscode.workspace.getConfiguration();
            const augmentConfig = config.get('augment');
            if (augmentConfig) {
                return {
                    success: true,
                    data: {
                        lastUpdate: new Date().toISOString()
                    },
                    method: 'detection'
                };
            }
        } catch (error) {
            console.log('Could not access Augment configuration:', error);
        }

        return {
            success: false,
            method: 'none'
        };
    }

    private parseAugmentResult(result: any): AugmentStatus['usageData'] {
        try {
            // Try to extract usage data from Augment's response
            if (result.usage || result.statistics || result.stats) {
                const usage = result.usage || result.statistics || result.stats;
                return {
                    totalUsage: usage.total || usage.count || usage.requests,
                    dailyUsage: usage.daily || usage.today,
                    lastUpdate: usage.lastUpdate || new Date().toISOString()
                };
            }
        } catch (error) {
            console.log('Could not parse Augment result:', error);
        }
        return undefined;
    }

    // Attempt to get real usage data from Augment (if possible)
    async tryGetRealUsageData(): Promise<{
        success: boolean;
        usage?: number;
        limit?: number;
        error?: string;
    }> {
        try {
            const isActive = await this.isAugmentActive();
            if (!isActive) {
                return {
                    success: false,
                    error: 'Augment extension is not active'
                };
            }

            // TODO: This would require reverse engineering Augment's internal API
            // For now, return a placeholder response
            return {
                success: false,
                error: 'Real usage data integration not yet implemented'
            };
        } catch (error) {
            return {
                success: false,
                error: `Error getting real usage data: ${error}`
            };
        }
    }

    // Check if we can integrate with Augment's status bar or UI
    async checkAugmentIntegrationPossibility(): Promise<{
        canIntegrate: boolean;
        methods: string[];
        limitations: string[];
    }> {
        const status = await this.getAugmentStatus();
        
        const methods: string[] = [];
        const limitations: string[] = [];

        if (status.installed) {
            methods.push('Extension state monitoring');
            
            if (status.active) {
                methods.push('Active extension detection');
                // TODO: Add more integration methods as they're discovered
            } else {
                limitations.push('Extension is installed but not active');
            }
        } else {
            limitations.push('Augment extension is not installed');
        }

        // Always add simulation as a fallback
        methods.push('Usage simulation based on editor events');

        return {
            canIntegrate: status.installed,
            methods,
            limitations
        };
    }
}

import * as vscode from 'vscode';
import { AugmentApiClient, AugmentUsageData } from './augmentApi';

export interface AugmentStatus {
    installed: boolean;
    active: boolean;
    version?: string;
    hasRealData: boolean;
    usageData?: {
        totalUsage?: number;
        dailyUsage?: number;
        lastUpdate?: string;
    };
    integrationMethod?: 'none' | 'detection' | 'user_input' | 'api';
}

export class AugmentDetector {
    private readonly AUGMENT_EXTENSION_ID = 'augment.vscode-augment';
    private lastDetectionTime: number = 0;
    private cachedStatus: AugmentStatus | null = null;
    private readonly CACHE_DURATION = 30000; // 30 seconds
    private apiClient: AugmentApiClient;

    constructor() {
        this.apiClient = new AugmentApiClient();
    }

    async detectAugmentPlugin(): Promise<boolean> {
        const status = await this.getAugmentStatus();
        return status.installed && status.active;
    }

    async getAugmentExtension(): Promise<vscode.Extension<any> | undefined> {
        return vscode.extensions.getExtension(this.AUGMENT_EXTENSION_ID);
    }

    async isAugmentActive(): Promise<boolean> {
        const extension = await this.getAugmentExtension();
        return extension?.isActive || false;
    }

    async getAugmentVersion(): Promise<string | undefined> {
        const extension = await this.getAugmentExtension();
        return extension?.packageJSON?.version;
    }

    async getAugmentStatus(): Promise<AugmentStatus> {
        // Use cached result if still valid
        const now = Date.now();
        if (this.cachedStatus && (now - this.lastDetectionTime) < this.CACHE_DURATION) {
            return this.cachedStatus;
        }

        const extension = await this.getAugmentExtension();
        const installed = extension !== undefined;
        const active = extension?.isActive || false;
        const version = extension?.packageJSON?.version;

        let hasRealData = false;
        let usageData: AugmentStatus['usageData'] = undefined;
        let integrationMethod: AugmentStatus['integrationMethod'] = 'none';

        if (installed && active) {
            // Try to get real usage data through various methods
            const realDataResult = await this.tryGetRealUsageData();
            hasRealData = realDataResult.success;
            usageData = realDataResult.data;
            integrationMethod = realDataResult.method;
        }

        const status: AugmentStatus = {
            installed,
            active,
            version,
            hasRealData,
            usageData,
            integrationMethod
        };

        // Cache the result
        this.cachedStatus = status;
        this.lastDetectionTime = now;

        return status;
    }

    // Monitor Augment extension state changes
    onAugmentStateChange(callback: (status: AugmentStatus) => void): vscode.Disposable {
        return vscode.extensions.onDidChange(async () => {
            // Clear cache to force fresh detection
            this.cachedStatus = null;
            const status = await this.getAugmentStatus();
            callback(status);
        });
    }

    // Try to get real usage data through various safe methods
    private async tryGetRealUsageData(): Promise<{
        success: boolean;
        data?: AugmentStatus['usageData'];
        method: AugmentStatus['integrationMethod'];
    }> {
        // Method 1: Try Augment Credits API if auth available
        if (this.apiClient.hasAuthToken() || this.apiClient.hasCookies()) {
            try {
                console.log('🪙 Trying Credits API...');
                const creditsResponse = await this.apiClient.getCreditsInfo();

                if (creditsResponse.success) {
                    console.log('📊 Credits API Raw Response:', JSON.stringify(creditsResponse.data, null, 2));

                    const usageData = await this.apiClient.parseUsageResponse(creditsResponse);
                    if (usageData) {
                        console.log('✅ Parsed Credits Data:', {
                            totalUsage: usageData.totalUsage,
                            usageLimit: usageData.usageLimit,
                            dailyUsage: usageData.dailyUsage,
                            subscriptionType: usageData.subscriptionType
                        });

                        return {
                            success: true,
                            data: {
                                totalUsage: usageData.totalUsage,
                                dailyUsage: usageData.dailyUsage,
                                lastUpdate: usageData.lastUpdate
                            },
                            method: 'api'
                        };
                    }
                } else {
                    console.log('❌ Credits API failed:', creditsResponse.error);
                }
            } catch (error) {
                console.log('❌ Augment Credits API call failed:', error);
            }
        }

        // Method 2: Try to call public Augment commands
        try {
            const commands = await vscode.commands.getCommands();
            const augmentCommands = commands.filter(cmd => cmd.startsWith('augment.'));

            if (augmentCommands.length > 0) {
                // Try to get status through public commands
                for (const command of ['augment.getStatus', 'augment.status', 'augment.info']) {
                    try {
                        const result = await vscode.commands.executeCommand(command);
                        if (result && typeof result === 'object') {
                            return {
                                success: true,
                                data: this.parseAugmentResult(result),
                                method: 'api'
                            };
                        }
                    } catch {
                        // Command doesn't exist or failed, try next
                        continue;
                    }
                }
            }
        } catch (error) {
            console.log('Could not access Augment commands:', error);
        }

        // Method 3: Monitor workspace configuration changes
        try {
            const config = vscode.workspace.getConfiguration();
            const augmentConfig = config.get('augment');
            if (augmentConfig) {
                return {
                    success: true,
                    data: {
                        lastUpdate: new Date().toISOString()
                    },
                    method: 'detection'
                };
            }
        } catch (error) {
            console.log('Could not access Augment configuration:', error);
        }

        return {
            success: false,
            method: 'none'
        };
    }

    private parseAugmentResult(result: any): AugmentStatus['usageData'] {
        try {
            // Try to extract usage data from Augment's response
            if (result.usage || result.statistics || result.stats) {
                const usage = result.usage || result.statistics || result.stats;
                return {
                    totalUsage: usage.total || usage.count || usage.requests,
                    dailyUsage: usage.daily || usage.today,
                    lastUpdate: usage.lastUpdate || new Date().toISOString()
                };
            }
        } catch (error) {
            console.log('Could not parse Augment result:', error);
        }
        return undefined;
    }

    // Attempt to get real usage data from Augment (if possible)
    async tryGetRealUsageData(): Promise<{
        success: boolean;
        usage?: number;
        limit?: number;
        error?: string;
    }> {
        try {
            const isActive = await this.isAugmentActive();
            if (!isActive) {
                return {
                    success: false,
                    error: 'Augment extension is not active'
                };
            }

            // TODO: This would require reverse engineering Augment's internal API
            // For now, return a placeholder response
            return {
                success: false,
                error: 'Real usage data integration not yet implemented'
            };
        } catch (error) {
            return {
                success: false,
                error: `Error getting real usage data: ${error}`
            };
        }
    }

    // Check if we can integrate with Augment's status bar or UI
    async checkAugmentIntegrationPossibility(): Promise<{
        canIntegrate: boolean;
        methods: string[];
        limitations: string[];
    }> {
        const status = await this.getAugmentStatus();
        
        const methods: string[] = [];
        const limitations: string[] = [];

        if (status.installed) {
            methods.push('Extension state monitoring');
            
            if (status.active) {
                methods.push('Active extension detection');
                // TODO: Add more integration methods as they're discovered
            } else {
                limitations.push('Extension is installed but not active');
            }
        } else {
            limitations.push('Augment extension is not installed');
        }

        // Always add simulation as a fallback
        methods.push('Usage simulation based on editor events');

        return {
            canIntegrate: status.installed,
            methods,
            limitations
        };
    }

    // API-related methods
    async promptForApiToken(): Promise<boolean> {
        const success = await this.apiClient.promptForAuthToken();
        if (success) {
            // Clear cache to force re-detection with new token
            this.cachedStatus = null;
        }
        return success;
    }

    async testApiConnection(): Promise<{
        success: boolean;
        error?: string;
        hasToken: boolean;
    }> {
        const hasToken = this.apiClient.hasAuthToken();

        if (!hasToken) {
            return {
                success: false,
                error: 'No authentication token provided',
                hasToken: false
            };
        }

        const testResult = await this.apiClient.testConnection();
        return {
            success: testResult.success,
            error: testResult.error,
            hasToken: true
        };
    }

    async getApiUsageData(): Promise<AugmentUsageData | null> {
        if (!this.apiClient.hasAuthToken()) {
            return null;
        }

        try {
            const response = await this.apiClient.getUsageData();
            if (response.success) {
                return await this.apiClient.parseUsageResponse(response);
            }
        } catch (error) {
            console.error('Error getting API usage data:', error);
        }

        return null;
    }

    clearApiToken(): void {
        this.apiClient.clearAuthToken();
        this.cachedStatus = null;
    }

    hasApiToken(): boolean {
        return this.apiClient.hasAuthToken();
    }
}

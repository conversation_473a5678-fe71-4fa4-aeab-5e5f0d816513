# 国际化指南 | Internationalization Guide

## 概述 | Overview

本插件支持中文和英文两种语言，会根据VSCode的语言设置自动切换。

This extension supports Chinese and English, automatically switching based on VSCode's language settings.

## 支持的语言 | Supported Languages

- **English (en)**: Default language | 默认语言
- **简体中文 (zh-cn)**: Simplified Chinese | 简体中文
- **繁体中文 (zh-tw)**: Uses Simplified Chinese | 使用简体中文

## 文件结构 | File Structure

```
├── package.nls.json        # 英文语言包 | English language pack
├── package.nls.zh-cn.json  # 中文语言包 | Chinese language pack
├── src/i18n.ts            # 国际化工具类 | I18n utility class
└── src/extension.ts       # 使用国际化 | Uses i18n
```

## 语言检测 | Language Detection

插件会自动检测VSCode的语言设置：

The extension automatically detects VSCode's language setting:

```typescript
// VSCode语言设置映射 | VSCode language mapping
const supportedLocales = {
    'zh-cn': 'zh-cn',
    'zh-tw': 'zh-cn',  // 繁体中文使用简体中文 | Traditional Chinese uses Simplified
    'zh': 'zh-cn',
    'en': 'en',
    'en-us': 'en',
    'en-gb': 'en'
};
```

## 测试国际化 | Testing Internationalization

### 切换到中文 | Switch to Chinese

1. 打开VSCode设置 | Open VSCode Settings: `Ctrl+,`
2. 搜索 "locale" | Search for "locale"
3. 设置 `"locale": "zh-cn"` | Set `"locale": "zh-cn"`
4. 重启VSCode | Restart VSCode
5. 重新安装插件 | Reinstall extension

### 切换到英文 | Switch to English

1. 打开VSCode设置 | Open VSCode Settings: `Ctrl+,`
2. 搜索 "locale" | Search for "locale"  
3. 设置 `"locale": "en"` | Set `"locale": "en"`
4. 重启VSCode | Restart VSCode
5. 重新安装插件 | Reinstall extension

## 国际化内容 | Internationalized Content

### 命令标题 | Command Titles
- ✅ 重置使用统计 | Reset Usage Statistics
- ✅ 打开设置 | Open Settings
- ✅ 显示使用详情 | Show Usage Details
- ✅ 设置浏览器Cookie | Setup Browser Cookies
- ✅ 检查认证状态 | Check Authentication Status
- ✅ 网页自动登录 | Web Login (Auto)

### 配置选项 | Configuration Options
- ✅ 启用/禁用追踪器 | Enable/disable tracker
- ✅ 月度使用限额 | Monthly usage limit
- ✅ 状态栏刷新间隔 | Status bar refresh interval
- ✅ 在状态栏显示 | Show in status bar
- ✅ 浏览器会话Cookie | Browser session cookies

### 状态消息 | Status Messages
- ✅ 正在获取真实使用数据 | Fetching real usage data
- ✅ API连接成功 | API connection successful
- ✅ 认证状态检查 | Authentication status check
- ✅ Cookie配置成功 | Cookies configured successfully

### 工具提示 | Tooltips
- ✅ 当前使用量 | Current usage
- ✅ 使用限额 | Usage limit
- ✅ 剩余积分 | Remaining credits
- ✅ 数据源 | Data source
- ✅ 来自API的真实数据 | Real data from API

### 对话框 | Dialogs
- ✅ 浏览器已打开 | Browser opened
- ✅ 设置Cookie | Setup Cookies
- ✅ 取消 | Cancel
- ✅ 网页登录错误 | Web login error

## 添加新语言 | Adding New Languages

### 1. 创建语言包 | Create Language Pack
```bash
# 创建新的语言包文件 | Create new language pack file
cp package.nls.json package.nls.fr.json  # 法语示例 | French example
```

### 2. 翻译内容 | Translate Content
编辑新的语言包文件，翻译所有文本。
Edit the new language pack file and translate all text.

### 3. 更新i18n.ts | Update i18n.ts
```typescript
// 添加新语言到messages对象 | Add new language to messages object
const messages = {
    'en': { /* English */ },
    'zh-cn': { /* Chinese */ },
    'fr': { /* French */ }  // 新语言 | New language
};

// 添加语言映射 | Add language mapping
const supportedLocales = {
    'fr': 'fr',
    'fr-fr': 'fr'
};
```

## 开发指南 | Development Guide

### 使用国际化文本 | Using Internationalized Text

```typescript
import { t } from './i18n';

// 简单文本 | Simple text
const message = t('status.success');

// 带参数的文本 | Text with parameters
const message = t('usage.current', usage, limit);
```

### 添加新的文本键 | Adding New Text Keys

1. **在package.nls.json中添加英文** | Add English in package.nls.json:
```json
{
  "newKey": "New English text"
}
```

2. **在package.nls.zh-cn.json中添加中文** | Add Chinese in package.nls.zh-cn.json:
```json
{
  "newKey": "新的中文文本"
}
```

3. **在i18n.ts中添加到messages对象** | Add to messages object in i18n.ts:
```typescript
const messages = {
    'en': {
        'newKey': 'New English text'
    },
    'zh-cn': {
        'newKey': '新的中文文本'
    }
};
```

4. **在代码中使用** | Use in code:
```typescript
const text = t('newKey');
```

## 验证国际化 | Verifying Internationalization

### 检查清单 | Checklist

- ✅ 命令标题已国际化 | Command titles internationalized
- ✅ 配置描述已国际化 | Configuration descriptions internationalized  
- ✅ 状态消息已国际化 | Status messages internationalized
- ✅ 工具提示已国际化 | Tooltips internationalized
- ✅ 对话框文本已国际化 | Dialog text internationalized
- ✅ 错误消息已国际化 | Error messages internationalized

### 测试步骤 | Testing Steps

1. **英文环境测试** | Test in English:
   - 设置VSCode语言为英文 | Set VSCode language to English
   - 安装插件 | Install extension
   - 验证所有文本为英文 | Verify all text is in English

2. **中文环境测试** | Test in Chinese:
   - 设置VSCode语言为中文 | Set VSCode language to Chinese
   - 重新安装插件 | Reinstall extension
   - 验证所有文本为中文 | Verify all text is in Chinese

3. **功能测试** | Functional Testing:
   - 测试所有命令 | Test all commands
   - 检查状态栏显示 | Check status bar display
   - 验证工具提示 | Verify tooltips
   - 测试对话框 | Test dialogs

## 故障排除 | Troubleshooting

### 语言未切换 | Language Not Switching

1. **检查VSCode语言设置** | Check VSCode language setting
2. **重启VSCode** | Restart VSCode
3. **重新安装插件** | Reinstall extension
4. **检查控制台日志** | Check console logs

### 部分文本未翻译 | Partial Text Not Translated

1. **检查语言包文件** | Check language pack files
2. **验证i18n.ts中的映射** | Verify mapping in i18n.ts
3. **确保使用t()函数** | Ensure using t() function
4. **重新编译插件** | Recompile extension

**现在您的插件支持完整的中英文国际化！** 🌐

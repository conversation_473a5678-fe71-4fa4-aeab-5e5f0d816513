{"version": 3, "file": "statusBar.js", "sourceRoot": "", "sources": ["../src/statusBar.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAKjC,MAAa,gBAAgB;IAOzB,YAAY,YAA0B,EAAE,aAA4B;QAF5D,kBAAa,GAAyB,IAAI,CAAC;QAG/C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAClD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,GAAG,CACN,CAAC;QAEF,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,gDAAgD,CAAC;QAC9E,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,2CAA2C;QAC3C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,kBAAkB;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAExD,QAAQ,WAAW,EAAE;YACjB,KAAK,aAAa;gBACd,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG;oBACzB,OAAO,EAAE,aAAa;oBACtB,SAAS,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;oBAC5D,KAAK,EAAE,sBAAsB;iBAChC,CAAC;gBACF,MAAM;YACV,KAAK,aAAa;gBACd,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,4BAA4B,CAAC;gBAC1D,MAAM;YACV,KAAK,cAAc;gBACf,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,6BAA6B,CAAC;gBAC3D,MAAM;YACV;gBACI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,4BAA4B,CAAC;SACjE;IACL,CAAC;IAED,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,EAAE;YAChF,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACzD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QAErD,+DAA+D;QAC/D,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9C,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,qBAAqB,KAAK,IAAI,KAAK,IAAI,aAAa,EAAE,CAAC;QAEjF,yBAAyB;QACzB,IAAI,OAAO,GAAG;WACX,KAAK;SACP,KAAK;SACL,UAAU;aACN,KAAK,GAAG,KAAK;eACX,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,CAAC;QAEhE,kCAAkC;QAClC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,IAAI,qBAAqB,CAAC;YACjC,OAAO,IAAI,kBAAkB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC3E,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;gBAC9B,OAAO,IAAI,eAAe,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBACrE,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;oBAC5B,OAAO,IAAI,gBAAgB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;iBAC3D;gBACD,OAAO,IAAI,oBAAoB,IAAI,CAAC,aAAa,CAAC,iBAAiB,IAAI,MAAM,EAAE,CAAC;aACnF;SACJ;QAED,OAAO,IAAI,gBAAgB,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;QAErC,yDAAyD;QACzD,IAAI,UAAU,IAAI,EAAE,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;SACrF;aAAM,IAAI,UAAU,IAAI,EAAE,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;SACvF;aAAM,IAAI,WAAW,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;SACzF;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,SAAS,CAAC;SACxC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAEO,wBAAwB,CAAC,MAAc,EAAE,WAAoB;QACjE,IAAI,WAAW,EAAE;YACb,QAAQ,MAAM,EAAE;gBACZ,KAAK,aAAa,CAAC,CAAC,OAAO,4BAA4B,CAAC;gBACxD,KAAK,eAAe,CAAC,CAAC,OAAO,8BAA8B,CAAC;gBAC5D,KAAK,YAAY,CAAC,CAAC,OAAO,yBAAyB,CAAC;gBACpD,OAAO,CAAC,CAAC,OAAO,WAAW,CAAC;aAC/B;SACJ;aAAM;YACH,OAAO,2CAA2C,CAAC;SACtD;IACL,CAAC;IAED,mBAAmB,CAAC,MAAqB;QACrC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,yBAAyB;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QACnD,QAAQ,MAAM,EAAE;YACZ,KAAK,aAAa,CAAC,CAAC,OAAO,sBAAsB,CAAC;YAClD,KAAK,aAAa,CAAC,CAAC,OAAO,oBAAoB,CAAC;YAChD,KAAK,cAAc,CAAC,CAAC,OAAO,eAAe,CAAC;YAC5C,OAAO,CAAC,CAAC,OAAO,cAAc,CAAC;SAClC;IACL,CAAC;IAEO,iBAAiB;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC;QAEhE,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACpC;QAED,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,EAAE,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED,IAAI;QACA,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED,IAAI;QACA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACpC;QACD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AA/JD,4CA+JC"}
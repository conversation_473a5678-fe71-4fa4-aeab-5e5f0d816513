// Augment API 测试脚本
// 使用方法：node test-api.js [your-token]

const https = require('https');

const API_BASE = 'https://i1.api.augmentcode.com';
const token = process.argv[2];

console.log('🧪 Augment API 测试脚本');
console.log('='.repeat(50));

// 测试健康检查
async function testHealth() {
    console.log('\n🏥 测试健康检查...');
    
    return new Promise((resolve) => {
        const req = https.get(`${API_BASE}/health`, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应: ${data}`);
                console.log(`   ✅ 健康检查${res.statusCode === 200 ? '成功' : '失败'}`);
                resolve(res.statusCode === 200);
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ 错误: ${err.message}`);
            resolve(false);
        });
    });
}

// 测试认证端点
async function testAuth(endpoint, name) {
    console.log(`\n${name}...`);
    
    if (!token) {
        console.log('   ⚠️  跳过 - 未提供token');
        return false;
    }
    
    return new Promise((resolve) => {
        const options = {
            hostname: 'i1.api.augmentcode.com',
            path: endpoint,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应长度: ${data.length} 字符`);
                
                if (res.statusCode === 200) {
                    try {
                        const json = JSON.parse(data);
                        console.log(`   数据键: [${Object.keys(json).join(', ')}]`);
                        console.log(`   ✅ ${name}成功`);
                        resolve(true);
                    } catch (e) {
                        console.log(`   响应: ${data}`);
                        console.log(`   ✅ ${name}成功 (非JSON响应)`);
                        resolve(true);
                    }
                } else if (res.statusCode === 401) {
                    console.log(`   ❌ 认证失败 - token可能无效`);
                    resolve(false);
                } else {
                    console.log(`   ❌ ${name}失败`);
                    console.log(`   响应: ${data}`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ 网络错误: ${err.message}`);
            resolve(false);
        });
        
        req.end();
    });
}

// 主测试函数
async function runTests() {
    console.log(`开始时间: ${new Date().toISOString()}`);
    
    const results = {
        health: await testHealth(),
        user: await testAuth('/user', '👤 测试用户信息'),
        usage: await testAuth('/usage', '📊 测试使用数据'),
        subscription: await testAuth('/subscription', '💳 测试订阅信息')
    };
    
    console.log('\n' + '='.repeat(50));
    console.log('📋 测试结果汇总:');
    console.log(`   健康检查: ${results.health ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   用户信息: ${results.user ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   使用数据: ${results.usage ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   订阅信息: ${results.subscription ? '✅ 通过' : '❌ 失败'}`);
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 测试通过`);
    
    if (!token) {
        console.log('\n💡 提示: 使用 node test-api.js YOUR_TOKEN 来测试认证端点');
    }
    
    console.log(`完成时间: ${new Date().toISOString()}`);
}

// 运行测试
runTests().catch(console.error);

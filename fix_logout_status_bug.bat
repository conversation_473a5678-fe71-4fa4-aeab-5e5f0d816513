@echo off
chcp 65001 > nul
echo ========================================
echo   修复退出登录状态栏异常问题
echo ========================================
echo.

echo 🚨 问题描述:
echo 退出登录后状态栏会显示"未登录"然后又显示数量，
echo 然后又变成"未登录"，状态不稳定。
echo.

echo 🔍 问题根本原因:
echo [1] 定时器没有停止 - usageTracker的定时器仍在运行
echo [2] 数据获取器仍在工作 - realDataFetcher仍在定期执行
echo [3] 状态检查冲突 - 多个地方在更新状态栏
echo [4] 配置监听器 - 清空配置后触发状态更新
echo.

echo 🔧 修复内容:
echo [1] 修改logout命令，停止所有数据获取
echo [2] 添加clearRealDataFlag方法清除数据标志
echo [3] 修改fetchRealUsageData添加退出检查
echo [4] 优化配置监听器避免冲突
echo [5] 确保状态栏更新的原子性
echo.

echo [1/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [2/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [3/4] 发布修复版本...
set /p confirm="确定要发布修复版本吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo ❌ 发布失败
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo [4/4] 生成测试指南...

echo ========================================
echo   修复后的退出登录流程
echo ========================================
echo.

echo 📋 正确的退出登录行为:
echo.
echo 步骤1: 用户点击"🚪 退出登录"
echo 步骤2: 显示确认对话框
echo 步骤3: 用户确认后执行以下操作:
echo   - 停止数据获取器 (setRealDataFetcher(null))
echo   - 清空VSCode配置中的cookies
echo   - 清空API客户端认证数据
echo   - 重置存储数据
echo   - 重置使用追踪器
echo   - 清除真实数据标志
echo   - 立即更新状态栏为未登录状态
echo 步骤4: 状态栏稳定显示"$(circle-slash) Augment: 未登录"
echo.

echo ⚠️  修复的关键点:
echo.
echo 1. 数据获取器控制:
echo    - 退出登录时设置 realDataFetcher = null
echo    - fetchRealUsageData 检查获取器是否存在
echo    - 避免定时器继续获取数据
echo.
echo 2. 状态标志管理:
echo    - 添加 clearRealDataFlag() 方法
echo    - 确保 hasRealData = false
echo    - 重置 realDataSource = 'no_data'
echo.
echo 3. 配置监听器优化:
echo    - 检查cookies是否为空
echo    - 只在有认证数据时重新检测状态
echo    - 避免空配置触发状态更新
echo.
echo 4. 状态栏更新原子性:
echo    - 立即调用 updateLogoutStatus()
echo    - 确保状态栏显示一致性
echo    - 避免多次状态切换
echo.

echo ========================================
echo   测试验证步骤
echo ========================================
echo.

echo 🧪 测试1: 基本退出登录
echo 1. 确保已登录并显示使用数据
echo 2. 运行 Ctrl+Shift+P → "🚪 退出登录"
echo 3. 确认退出
echo 4. 验证状态栏立即显示"未登录"
echo 5. 等待10秒，确保状态栏保持"未登录"
echo.

echo 🧪 测试2: 退出后重新登录
echo 1. 完成测试1
echo 2. 运行 Ctrl+Shift+P → "🌐 网页自动登录"
echo 3. 重新配置认证
echo 4. 验证状态栏正常显示使用数据
echo.

echo 🧪 测试3: 多次退出登录
echo 1. 登录 → 退出 → 登录 → 退出
echo 2. 每次退出都验证状态栏稳定
echo 3. 确保没有状态闪烁
echo.

echo 🧪 测试4: 配置变更测试
echo 1. 退出登录后
echo 2. 手动修改VSCode设置中的augment.cookies
echo 3. 验证状态栏不会异常更新
echo.

echo ========================================
echo   预期修复效果
echo ========================================
echo.

echo ✅ 修复前的问题:
echo ❌ 退出登录 → 显示"未登录" → 显示数量 → 又显示"未登录"
echo ❌ 状态栏不稳定，来回切换
echo ❌ 定时器继续运行获取数据
echo.

echo ✅ 修复后的效果:
echo ✅ 退出登录 → 立即显示"未登录" → 保持稳定
echo ✅ 状态栏显示一致，不再闪烁
echo ✅ 所有数据获取停止
echo ✅ 重新登录正常工作
echo.

echo 🎯 技术改进:
echo - 更好的状态管理
echo - 清晰的数据获取控制
echo - 原子性的状态更新
echo - 避免竞态条件
echo.

echo ✅ 退出登录状态栏异常问题修复完成！
echo.
echo 现在退出登录后状态栏会稳定显示"未登录"状态，
echo 不会再出现数量显示和状态切换的问题。
echo.

pause

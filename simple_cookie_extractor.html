<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍪 Augment Cookie 一键提取器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .bookmark-button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 20px 0;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .bookmark-button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .highlight {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 3px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">🍪</span>Augment Cookie 一键提取器</h1>
        <p>最简单的方式获取Augment session cookie，支持HttpOnly cookie！</p>

        <div class="step">
            <h3><span class="emoji">📋</span>方法1：一键书签（推荐）</h3>
            <p>拖拽下面的按钮到浏览器书签栏，然后在Augment页面点击即可：</p>
            
            <a href="javascript:(function(){var steps=['🔍 正在检查cookie...'];if(document.cookie.includes('_session=')){var cookies=document.cookie;var sessionMatch=cookies.match(/_session=([^;]+)/);if(sessionMatch){var sessionValue=sessionMatch[1];steps.push('✅ 找到session cookie！');steps.push('📋 Cookie值: '+sessionValue.substring(0,20)+'...');if(navigator.clipboard){navigator.clipboard.writeText('_session='+sessionValue).then(()=>{alert('✅ Cookie已复制到剪贴板！\n\n请在VSCode中粘贴：\n_session='+sessionValue.substring(0,30)+'...');}).catch(()=>{prompt('📋 请复制下面的cookie：','_session='+sessionValue);});}else{prompt('📋 请复制下面的cookie：','_session='+sessionValue);}return;}}steps.push('⚠️ 未找到session cookie或cookie是HttpOnly');steps.push('');steps.push('📋 请手动获取：');steps.push('1. 按F12打开开发者工具');steps.push('2. Application → Cookies → '+location.hostname);steps.push('3. 找到_session，复制Value值');steps.push('4. 在VSCode中粘贴');alert(steps.join('\n'));})();" 
                   class="bookmark-button">
                🍪 拖我到书签栏
            </a>
            
            <div class="highlight">
                <strong>使用方法：</strong>
                <ol>
                    <li>拖拽上面的按钮到浏览器书签栏</li>
                    <li>登录到 <code>app.augmentcode.com</code></li>
                    <li>点击书签栏中的"🍪 拖我到书签栏"</li>
                    <li>如果是普通cookie会自动复制，如果是HttpOnly会显示手动指导</li>
                </ol>
            </div>
        </div>

        <div class="step">
            <h3><span class="emoji">🛠️</span>方法2：开发者工具（3步搞定）</h3>
            <div class="highlight">
                <strong>超简单3步：</strong>
                <ol>
                    <li><strong>F12</strong> 打开开发者工具</li>
                    <li><strong>Application</strong> → Cookies → app.augmentcode.com</li>
                    <li><strong>复制</strong> _session 的 Value 值</li>
                </ol>
            </div>
        </div>

        <div class="step">
            <h3><span class="emoji">🔧</span>方法3：控制台一键（如果不是HttpOnly）</h3>
            <p>在 <code>app.augmentcode.com</code> 页面按F12，控制台输入：</p>
            <div class="highlight">
                <code>
document.cookie.split(';').find(c=>c.includes('_session'))?.trim()
                </code>
            </div>
            <p>如果返回结果，直接复制；如果返回undefined，说明是HttpOnly，请用方法2。</p>
        </div>

        <div class="step">
            <h3><span class="emoji">📱</span>移动端/简化版</h3>
            <p>如果上述方法都不方便，最简单的方式：</p>
            <div class="highlight">
                <ol>
                    <li>手机或电脑打开 <code>app.augmentcode.com</code></li>
                    <li>长按页面 → 检查元素 → 找到Application</li>
                    <li>或者直接联系技术支持获取cookie</li>
                </ol>
            </div>
        </div>

        <div class="step">
            <h3><span class="emoji">❓</span>常见问题</h3>
            <div class="highlight">
                <p><strong>Q: 书签点击没反应？</strong><br>
                A: 确保在app.augmentcode.com页面使用，且已登录。</p>
                
                <p><strong>Q: 找不到_session cookie？</strong><br>
                A: 可能是HttpOnly cookie，请用开发者工具Application标签页。</p>
                
                <p><strong>Q: 复制的格式对吗？</strong><br>
                A: 格式应该是 <code>_session=eyJ...</code> 或者只要Value部分。</p>
            </div>
        </div>

        <div class="step">
            <h3><span class="emoji">🎯</span>推荐顺序</h3>
            <div class="highlight">
                <ol>
                    <li><strong>首选：</strong>拖拽书签，在Augment页面点击</li>
                    <li><strong>备选：</strong>F12 → Application → Cookies → 复制_session</li>
                    <li><strong>最后：</strong>控制台输入cookie查询代码</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 检测是否支持拖拽书签
        if (navigator.userAgent.includes('Mobile')) {
            document.querySelector('.bookmark-button').innerHTML = '📱 移动端请长按复制链接';
        }
    </script>
</body>
</html>

# 测试 Augment Usage Tracker 插件

## 测试步骤

### 1. 启动插件开发环境
1. 在VSCode中打开项目文件夹
2. 按 `F5` 启动Extension Development Host
3. 新窗口将打开用于测试插件

### 2. 验证状态栏显示
- 检查右下角状态栏是否显示 "$(pulse) Augment: 0/1000"
- 状态栏项目应该可见且可点击

### 3. 测试基本功能
1. **点击状态栏项目**：应该打开 augmentcode.com 网站
2. **编辑文档**：在测试窗口中创建新文件并输入文本，观察使用计数是否增加
3. **保存文档**：保存文件，使用计数应该增加更多

### 4. 测试命令
打开命令面板 (`Ctrl+Shift+P`) 并测试：
- `Augment Tracker: Show Usage Details` - 显示详细使用信息
- `Augment Tracker: Reset Usage Statistics` - 重置使用统计
- `Augment Tracker: Open Settings` - 打开设置页面

### 5. 测试配置
1. 打开设置 (`Ctrl+,`)
2. 搜索 "augmentTracker"
3. 测试修改以下设置：
   - `augmentTracker.usageLimit`: 改为 500
   - `augmentTracker.clickAction`: 改为 "showDetails"
   - `augmentTracker.refreshInterval`: 改为 10 秒

### 6. 验证数据持久化
1. 进行一些操作增加使用计数
2. 关闭Extension Development Host
3. 重新启动 (`F5`)
4. 验证使用计数是否保持

### 7. 测试Augment检测
- 检查开发者控制台 (`Help > Toggle Developer Tools`)
- 查看是否有关于Augment插件检测的日志信息

## 预期结果

✅ 状态栏显示正确的使用统计
✅ 点击行为按配置工作
✅ 使用计数随编辑活动增加
✅ 命令正常执行
✅ 配置更改立即生效
✅ 数据在重启后保持
✅ 无控制台错误

## 故障排除

如果遇到问题：
1. 检查开发者控制台的错误信息
2. 确认所有TypeScript文件已正确编译
3. 验证package.json中的配置正确
4. 重新编译：`npm run compile`

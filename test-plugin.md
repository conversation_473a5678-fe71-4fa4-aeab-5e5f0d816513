# 测试 Augment Usage Tracker 插件 - 增强版

## 🚀 快速测试指南

### 1. 启动插件开发环境
1. 在VSCode中打开项目文件夹
2. 按 `F5` 启动Extension Development Host
3. 新窗口将打开用于测试插件

### 2. 验证增强状态栏显示
- 检查右下角状态栏是否显示 "$(pulse) Augment: 0/1000 ○"
- 注意数据源指示器：○ = 模拟数据，● = 真实数据
- 鼠标悬停查看详细工具提示信息

### 3. 测试基本功能
1. **点击状态栏项目**：应该打开 augmentcode.com 网站
2. **编辑文档**：在测试窗口中创建新文件并输入文本，观察使用计数是否增加
3. **保存文档**：保存文件，使用计数应该增加更多
4. **选择文本**：选择文本内容，计数应该有微小增加

### 4. 测试所有命令
打开命令面板 (`Ctrl+Shift+P`) 并测试：
- `Augment Tracker: Show Usage Details` - 显示详细使用信息
- `Augment Tracker: Reset Usage Statistics` - 重置使用统计
- `Augment Tracker: Open Settings` - 打开设置页面
- `Augment Tracker: Input Real Usage Data` - **新增**：手动输入真实数据

### 5. 测试真实数据输入功能
1. 执行 `Augment Tracker: Input Real Usage Data` 命令
2. 选择 "Enter Usage"
3. 输入一个数字（如：150）
4. 观察状态栏变化：
   - 使用计数应该更新为150
   - 数据源指示器应该变为 ●
   - 工具提示应该显示 "User-provided real data"

### 6. 测试配置功能
1. 打开设置 (`Ctrl+,`)
2. 搜索 "augmentTracker"
3. 测试修改以下设置：
   - `augmentTracker.usageLimit`: 改为 500
   - `augmentTracker.clickAction`: 改为 "showDetails"
   - `augmentTracker.refreshInterval`: 改为 10 秒
4. 观察状态栏实时更新

### 7. 测试颜色编码
1. 设置较低的使用限额（如：10）
2. 增加使用计数直到接近限额
3. 观察状态栏颜色变化：
   - < 75%：正常颜色
   - 75-89%：警告颜色（黄色）
   - ≥ 90%：错误颜色（红色）

### 8. 验证数据持久化
1. 进行一些操作增加使用计数
2. 关闭Extension Development Host
3. 重新启动 (`F5`)
4. 验证使用计数和数据源类型是否保持

### 9. 测试Augment检测功能
- 检查开发者控制台 (`Help > Toggle Developer Tools`)
- 查看Console标签页的日志信息：
  - "Augment Status: ..." - 显示检测结果
  - "Augment plugin not detected, using simulation mode" - 未检测到时的信息

## 🎯 预期结果

### 基础功能
✅ 状态栏显示正确的使用统计格式
✅ 数据源指示器正确显示（○ 或 ●）
✅ 点击行为按配置工作
✅ 使用计数随编辑活动增加
✅ 所有命令正常执行
✅ 配置更改立即生效
✅ 数据在重启后保持

### 增强功能
✅ 真实数据输入功能正常工作
✅ 数据源类型正确标识和显示
✅ 颜色编码根据使用率正确变化
✅ 工具提示显示完整详细信息
✅ Augment检测日志正确输出
✅ 状态缓存机制正常工作
✅ 无控制台错误

### 性能指标
✅ 状态栏刷新间隔可配置
✅ 检测缓存减少性能开销
✅ 编辑器响应性不受影响

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 状态栏不显示
- 检查 `augmentTracker.enabled` 设置
- 确认 `augmentTracker.showInStatusBar` 为 true
- 重启VSCode Extension Development Host

#### 2. 使用计数不增加
- 确认插件已激活（查看控制台日志）
- 检查是否有TypeScript编译错误
- 尝试重置使用统计

#### 3. 真实数据输入无效
- 确认输入的是有效数字
- 检查控制台是否有错误信息
- 验证数据是否正确保存

#### 4. 颜色编码不工作
- 检查使用率是否达到阈值
- 确认VSCode主题支持颜色编码
- 重新加载窗口

#### 5. Augment检测失败
- 确认Augment插件已安装
- 检查插件ID是否正确
- 查看控制台检测日志

### 调试步骤
1. 打开开发者工具：`Help > Toggle Developer Tools`
2. 查看Console标签页的日志信息
3. 搜索 "Augment Usage Tracker" 相关日志
4. 检查错误堆栈信息
5. 重新编译：`npm run compile`

### 重置插件状态
如果插件行为异常，可以：
1. 执行 `Augment Tracker: Reset Usage Statistics`
2. 重启Extension Development Host
3. 清除VSCode工作区设置
4. 重新安装插件

{"name": "augment-usage-tracker", "displayName": "Augment Usage Tracker", "description": "Track and display Augment AI usage statistics in VSCode status bar", "version": "1.0.0", "publisher": "augment-tracker", "repository": {"type": "git", "url": "https://github.com/augment-tracker/vscode-extension"}, "license": "MIT", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["augment", "ai", "usage", "tracker", "statistics"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augmentTracker.resetUsage", "title": "Reset Usage Statistics", "category": "Augment Tracker"}, {"command": "augmentTracker.openSettings", "title": "Open Settings", "category": "Augment Tracker"}, {"command": "augmentTracker.showDetails", "title": "Show Usage Details", "category": "Augment Tracker"}, {"command": "augmentTracker.inputRealData", "title": "Input Real Usage Data", "category": "Augment Tracker"}, {"command": "augmentTracker.setupApiToken", "title": "Setup API Token", "category": "Augment Tracker"}, {"command": "augmentTracker.testApiConnection", "title": "Test API Connection", "category": "Augment Tracker"}, {"command": "augmentTracker.clearApiToken", "title": "Clear API Token", "category": "Augment Tracker"}, {"command": "augmentTracker.debugApiCalls", "title": "Debug API Calls", "category": "Augment Tracker"}, {"command": "augmentTracker.setupCookies", "title": "Setup Browser Cookies", "category": "Augment Tracker"}, {"command": "augmentTracker.checkAuthStatus", "title": "Check Authentication Status", "category": "Augment Tracker"}, {"command": "augmentTracker.webLogin", "title": "🌐 Web Login (Auto)", "category": "Augment Tracker"}, {"command": "augmentTracker.testTimer", "title": "🔄 Test Timer & Fetch", "category": "Augment Tracker"}], "configuration": {"title": "Augment Usage Tracker", "properties": {"augmentTracker.enabled": {"type": "boolean", "default": true, "description": "Enable/disable the Augment usage tracker"}, "augmentTracker.usageLimit": {"type": "number", "default": 1000, "description": "Monthly usage limit for tracking"}, "augmentTracker.refreshInterval": {"type": "number", "default": 5, "description": "Status bar refresh interval in seconds", "minimum": 1, "maximum": 300}, "augmentTracker.showInStatusBar": {"type": "boolean", "default": true, "description": "Show usage statistics in status bar"}, "augmentTracker.clickAction": {"type": "string", "enum": ["openWebsite", "showDetails", "openSettings"], "default": "openWebsite", "description": "Action when clicking the status bar item"}, "augment.authToken": {"type": "string", "default": "", "description": "Augment API authentication token", "scope": "application"}, "augment.cookies": {"type": "string", "default": "", "description": "Augment browser session cookies", "scope": "application"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "install-vsce": "npm install -g vsce"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {}}
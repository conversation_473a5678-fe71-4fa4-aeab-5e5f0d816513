{"name": "augment-usage-tracker", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "augment-tracker", "author": {"name": "Augment Tracker Team", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/augment-tracker/vscode-extension"}, "bugs": {"url": "https://github.com/augment-tracker/vscode-extension/issues"}, "homepage": "https://github.com/augment-tracker/vscode-extension#readme", "license": "MIT", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["augment", "ai", "usage", "tracker", "statistics"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augmentTracker.resetUsage", "title": "%command.resetUsage%", "category": "Augment Tracker"}, {"command": "augmentTracker.openSettings", "title": "%command.openSettings%", "category": "Augment Tracker"}, {"command": "augmentTracker.showDetails", "title": "%command.showDetails%", "category": "Augment Tracker"}, {"command": "augmentTracker.setupCookies", "title": "%command.setupCookies%", "category": "Augment Tracker"}, {"command": "augmentTracker.checkAuthStatus", "title": "%command.checkAuthStatus%", "category": "Augment Tracker"}, {"command": "augmentTracker.webLogin", "title": "%command.webLogin%", "category": "Augment Tracker"}, {"command": "augmentTracker.manualRefresh", "title": "%command.manualRefresh%", "category": "Augment Tracker"}, {"command": "augmentTracker.setLanguage", "title": "%command.setLanguage%", "category": "Augment Tracker"}, {"command": "augmentTracker.checkCookieStatus", "title": "%command.checkCookieStatus%", "category": "Augment Tracker"}, {"command": "augmentTracker.refreshCookie", "title": "%command.refreshCookie%", "category": "Augment Tracker"}], "configuration": {"title": "%config.title%", "properties": {"augmentTracker.enabled": {"type": "boolean", "default": true, "description": "%config.enabled%"}, "augmentTracker.refreshInterval": {"type": "number", "default": 5, "description": "%config.refreshInterval%", "minimum": 1, "maximum": 300}, "augmentTracker.showInStatusBar": {"type": "boolean", "default": true, "description": "%config.showInStatusBar%"}, "augment.cookies": {"type": "string", "default": "", "description": "%config.cookies%", "scope": "application"}, "augmentTracker.language": {"type": "string", "enum": ["auto", "en", "zh-cn"], "enumDescriptions": ["%config.language.auto%", "%config.language.en%", "%config.language.zhCn%"], "default": "auto", "description": "%config.language%", "scope": "application"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "npx @vscode/vsce package --no-dependencies", "install-vsce": "npm install -g @vscode/vsce", "dev": "tsc --watch", "build": "tsc && npm run package", "clean": "<PERSON><PERSON>f out *.vsix"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {}}
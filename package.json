{"name": "augment-usage-tracker", "displayName": "Augment Usage Tracker", "description": "Track and display Augment AI usage statistics in VSCode status bar", "version": "1.0.1", "publisher": "augment-usage-tracker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/akapril/augment-usage-tracker"}, "bugs": {"url": "https://github.com/akapril/augment-usage-tracker/issues"}, "homepage": "https://github.com/akapril/augment-usage-tracker#readme", "license": "MIT", "engines": {"vscode": "^1.74.0"}, "extensionKind": ["ui"], "categories": ["Other"], "keywords": ["augment", "ai", "usage", "tracker", "statistics"], "icon": "icon.png", "galleryBanner": {"color": "#007ACC", "theme": "dark"}, "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augmentTracker.resetUsage", "title": "%command.resetUsage%", "category": "Augment Tracker"}, {"command": "augmentTracker.openSettings", "title": "%command.openSettings%", "category": "Augment Tracker"}, {"command": "augmentTracker.showDetails", "title": "%command.showDetails%", "category": "Augment Tracker"}, {"command": "augmentTracker.setupCookies", "title": "%command.setupCookies%", "category": "Augment Tracker"}, {"command": "augmentTracker.checkAuthStatus", "title": "%command.checkAuthStatus%", "category": "Augment Tracker"}, {"command": "augmentTracker.webLogin", "title": "%command.webLogin%", "category": "Augment Tracker"}, {"command": "augmentTracker.manualRefresh", "title": "%command.manualRefresh%", "category": "Augment Tracker"}, {"command": "augmentTracker.setLanguage", "title": "%command.setLanguage%", "category": "Augment Tracker"}, {"command": "augmentTracker.checkCookieStatus", "title": "%command.checkCookieStatus%", "category": "Augment Tracker"}, {"command": "augmentTracker.refreshCookie", "title": "%command.refreshCookie%", "category": "Augment Tracker"}, {"command": "augmentTracker.logout", "title": "%command.logout%", "category": "Augment Tracker"}], "configuration": {"title": "Augment Usage Tracker", "properties": {"augmentTracker.enabled": {"type": "boolean", "default": true, "description": "Enable/disable the Augment usage tracker"}, "augmentTracker.refreshInterval": {"type": "number", "default": 5, "description": "Status bar refresh interval in seconds", "minimum": 1, "maximum": 300}, "augmentTracker.showInStatusBar": {"type": "boolean", "default": true, "description": "Show usage statistics in status bar"}, "augment.cookies": {"type": "string", "default": "", "description": "Augment browser session cookies", "scope": "application"}, "augmentTracker.language": {"type": "string", "enum": ["auto", "en", "zh-cn"], "enumDescriptions": ["Auto (Follow VSCode)", "English", "Simplified Chinese"], "default": "auto", "description": "Interface language setting", "scope": "application"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "package": "npx @vscode/vsce package --no-dependencies", "clean": "<PERSON><PERSON>f out *.vsix"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {}}
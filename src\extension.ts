import * as vscode from 'vscode';
import { StatusBarManager } from './statusBar';
import { UsageTracker } from './usageTracker';
import { StorageManager } from './storage';
import { AugmentDetector } from './augmentDetector';
import { ConfigManager } from './config';

let statusBarManager: StatusBarManager;
let usageTracker: UsageTracker;
let storageManager: StorageManager;
let augmentDetector: AugmentDetector;
let configManager: ConfigManager;

export function activate(context: vscode.ExtensionContext) {
    console.log('Augment Usage Tracker is now active!');

    // Initialize managers
    storageManager = new StorageManager(context);
    configManager = new ConfigManager();
    augmentDetector = new AugmentDetector();
    usageTracker = new UsageTracker(storageManager, configManager);
    statusBarManager = new StatusBarManager(usageTracker, configManager);

    // Register commands
    const resetUsageCommand = vscode.commands.registerCommand('augmentTracker.resetUsage', () => {
        usageTracker.resetUsage();
        vscode.window.showInformationMessage('Augment usage statistics have been reset.');
    });

    const openSettingsCommand = vscode.commands.registerCommand('augmentTracker.openSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'augmentTracker');
    });

    const showDetailsCommand = vscode.commands.registerCommand('augmentTracker.showDetails', () => {
        showUsageDetails();
    });

    const inputRealDataCommand = vscode.commands.registerCommand('augmentTracker.inputRealData', () => {
        usageTracker.promptUserForRealData();
    });

    // Start tracking
    usageTracker.startTracking();
    statusBarManager.show();

    // Check for Augment plugin and get detailed status
    augmentDetector.getAugmentStatus().then(status => {
        console.log('Augment Status:', status);

        if (status.installed) {
            if (status.active) {
                if (status.hasRealData) {
                    console.log(`Augment plugin detected with real data (${status.integrationMethod})`);
                    // Update usage tracker with real data if available
                    if (status.usageData) {
                        usageTracker.updateWithRealData(status.usageData);
                    }
                } else {
                    console.log('Augment plugin detected but no real data available');
                }
            } else {
                console.log('Augment plugin installed but not active');
            }
        } else {
            console.log('Augment plugin not detected, using simulation mode');
        }

        // Update status bar with integration info
        statusBarManager.updateAugmentStatus(status);
    });

    // Add to subscriptions
    context.subscriptions.push(
        resetUsageCommand,
        openSettingsCommand,
        showDetailsCommand,
        inputRealDataCommand,
        statusBarManager,
        usageTracker
    );

    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('augmentTracker')) {
            configManager.reloadConfig();
            statusBarManager.updateDisplay();
        }

        // Check if Augment configuration changed
        if (event.affectsConfiguration('augment')) {
            // Re-detect Augment status
            augmentDetector.getAugmentStatus().then(status => {
                statusBarManager.updateAugmentStatus(status);
                if (status.hasRealData && status.usageData) {
                    usageTracker.updateWithRealData(status.usageData);
                }
            });
        }
    });

    // Monitor Augment extension state changes
    const augmentStateWatcher = augmentDetector.onAugmentStateChange(status => {
        console.log('Augment state changed:', status);
        statusBarManager.updateAugmentStatus(status);

        if (status.hasRealData && status.usageData) {
            usageTracker.updateWithRealData(status.usageData);
        }
    });

    context.subscriptions.push(augmentStateWatcher);
}

function showUsageDetails() {
    const usage = usageTracker.getCurrentUsage();
    const limit = configManager.getUsageLimit();
    const percentage = Math.round((usage / limit) * 100);
    
    const message = `
Augment Usage Statistics:
• Current Usage: ${usage}
• Monthly Limit: ${limit}
• Usage Percentage: ${percentage}%
• Remaining: ${limit - usage}

Last Reset: ${usageTracker.getLastResetDate()}
    `.trim();

    vscode.window.showInformationMessage(message, 'Reset Usage', 'Open Settings').then(selection => {
        if (selection === 'Reset Usage') {
            vscode.commands.executeCommand('augmentTracker.resetUsage');
        } else if (selection === 'Open Settings') {
            vscode.commands.executeCommand('augmentTracker.openSettings');
        }
    });
}

export function deactivate() {
    if (statusBarManager) {
        statusBarManager.dispose();
    }
    if (usageTracker) {
        usageTracker.dispose();
    }
}

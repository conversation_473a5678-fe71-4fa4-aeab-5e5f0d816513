import * as vscode from 'vscode';
import { StatusBarManager } from './statusBar';
import { UsageTracker } from './usageTracker';
import { StorageManager } from './storage';
import { AugmentDetector } from './augmentDetector';
import { ConfigManager } from './config';
import { WebAuthManager } from './webAuth';
import { I18n, t } from './i18n';

let statusBarManager: StatusBarManager;
let usageTracker: UsageTracker;
let storageManager: StorageManager;
let augmentDetector: AugmentDetector;
let configManager: ConfigManager;

export function activate(context: vscode.ExtensionContext) {
    // 初始化国际化
    I18n.init();
    console.log('Augment Usage Tracker is now active!');

    // Initialize managers
    storageManager = new StorageManager(context);
    configManager = new ConfigManager();
    augmentDetector = new AugmentDetector();
    usageTracker = new UsageTracker(storageManager, configManager);
    statusBarManager = new StatusBarManager(usageTracker, configManager);
    cookieManager = new CookieManager(context);

    // Initialize cookie manager (this will handle first install)
    await cookieManager.initialize();

    // Register commands
    const resetUsageCommand = vscode.commands.registerCommand('augmentTracker.resetUsage', () => {
        usageTracker.resetUsage();
        vscode.window.showInformationMessage('Augment usage statistics have been reset.');
    });

    const openSettingsCommand = vscode.commands.registerCommand('augmentTracker.openSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'augmentTracker');
    });

    const showDetailsCommand = vscode.commands.registerCommand('augmentTracker.showDetails', () => {
        showUsageDetails();
    });

    const inputRealDataCommand = vscode.commands.registerCommand('augmentTracker.inputRealData', () => {
        usageTracker.promptUserForRealData();
    });

    const setupApiTokenCommand = vscode.commands.registerCommand('augmentTracker.setupApiToken', async () => {
        const success = await augmentDetector.promptForApiToken();
        if (success) {
            vscode.window.showInformationMessage('Augment API token configured successfully!');
            // Re-detect status with new token
            const status = await augmentDetector.getAugmentStatus();
            statusBarManager.updateAugmentStatus(status);
            if (status.hasRealData && status.usageData) {
                usageTracker.updateWithRealData(status.usageData);
            }
        }
    });

    const testApiConnectionCommand = vscode.commands.registerCommand('augmentTracker.testApiConnection', async () => {
        const result = await augmentDetector.testApiConnection();
        if (result.success) {
            vscode.window.showInformationMessage('✅ Augment API connection successful!');
        } else {
            const message = result.hasToken
                ? `❌ API connection failed: ${result.error}`
                : '❌ No API token configured. Use "Setup API Token" command first.';
            vscode.window.showErrorMessage(message);
        }
    });

    const clearApiTokenCommand = vscode.commands.registerCommand('augmentTracker.clearApiToken', () => {
        augmentDetector.clearApiToken();
        vscode.window.showInformationMessage('Augment API token cleared.');
        // Update status bar to reflect change
        statusBarManager.updateDisplay();
    });

    const debugApiCallsCommand = vscode.commands.registerCommand('augmentTracker.debugApiCalls', async () => {
        const choice = await vscode.window.showQuickPick([
            { label: '🏥 Health Check', description: 'Test basic API connectivity', value: 'health' },
            { label: '👤 User Info', description: 'Get user account information', value: 'user' },
            { label: '🪙 Credits Info', description: 'Get credits usage (REAL DATA)', value: 'credits' },
            { label: '📊 Usage Data', description: 'Get usage statistics', value: 'usage' },
            { label: '💳 Subscription', description: 'Get subscription information', value: 'subscription' },
            { label: '📋 Plans Info', description: 'Get available plans', value: 'plans' }
        ], {
            placeHolder: 'Select API endpoint to test',
            ignoreFocusOut: true
        });

        if (!choice) return;

        vscode.window.showInformationMessage(`🔍 Testing ${choice.label}... Check Developer Console for details.`);
        console.log(`\n🧪 === API Debug Test: ${choice.label} ===`);
        console.log(`⏰ Started at: ${new Date().toISOString()}`);

        try {
            const apiClient = (augmentDetector as any).apiClient;
            let result;

            switch (choice.value) {
                case 'health':
                    result = await apiClient.checkHealth();
                    break;
                case 'user':
                    result = await apiClient.getUserInfo();
                    break;
                case 'credits':
                    result = await apiClient.getCreditsInfo();
                    break;
                case 'usage':
                    result = await apiClient.getUsageData();
                    break;
                case 'subscription':
                    result = await apiClient.getSubscriptionInfo();
                    break;
                case 'plans':
                    result = await apiClient.getPlansInfo();
                    break;
            }

            console.log(`📋 API Response:`, result);

            if (result.success) {
                vscode.window.showInformationMessage(`✅ ${choice.label} successful! Check console for details.`);
            } else {
                const errorMsg = result.error || 'Unknown error';
                if (errorMsg.includes('Authentication required') || errorMsg.includes('auth token')) {
                    const action = await vscode.window.showErrorMessage(
                        `❌ ${choice.label} failed: Authentication required`,
                        'Setup Cookies',
                        'Setup Token',
                        'Help'
                    );

                    if (action === 'Setup Cookies') {
                        vscode.commands.executeCommand('augmentTracker.setupCookies');
                    } else if (action === 'Setup Token') {
                        vscode.commands.executeCommand('augmentTracker.setupApiToken');
                    } else if (action === 'Help') {
                        vscode.window.showInformationMessage(
                            '💡 认证帮助:\n' +
                            '1. Cookie认证: 从浏览器复制登录后的cookie\n' +
                            '2. Token认证: 从Augment dashboard获取API token\n' +
                            '3. Cookie通常24小时过期，需要定期更新'
                        );
                    }
                } else {
                    vscode.window.showErrorMessage(`❌ ${choice.label} failed: ${errorMsg}`);
                }
            }
        } catch (error) {
            console.error(`💥 Test failed:`, error);
            vscode.window.showErrorMessage(`💥 Test failed: ${error}`);
        }

        console.log(`⏰ Completed at: ${new Date().toISOString()}`);
        console.log(`🧪 === End API Debug Test ===\n`);
    });

    const setupCookiesCommand = vscode.commands.registerCommand('augmentTracker.setupCookies', async () => {
        const apiClient = (augmentDetector as any).apiClient;
        const success = await apiClient.promptForCookies();
        if (success) {
            vscode.window.showInformationMessage(t('status.cookiesConfigured'));
            // Re-detect status with new cookies
            const status = await augmentDetector.getAugmentStatus();
            statusBarManager.updateAugmentStatus(status);
            if (status.hasRealData && status.usageData) {
                usageTracker.updateWithRealData(status.usageData);
            }
        }
    });

    const checkAuthStatusCommand = vscode.commands.registerCommand('augmentTracker.checkAuthStatus', async () => {
        const apiClient = (augmentDetector as any).apiClient;

        vscode.window.showInformationMessage(t('status.checkingAuth'));

        const hasToken = apiClient.hasAuthToken();
        const hasCookies = apiClient.hasCookies();

        let statusMessage = t('status.authStatus') + '\n';
        statusMessage += `${t('status.apiToken')}: ${hasToken ? t('status.configured') : t('status.notConfigured')}\n`;
        statusMessage += `${t('status.browserCookies')}: ${hasCookies ? t('status.configured') : t('status.notConfigured')}\n`;

        if (hasToken || hasCookies) {
            statusMessage += `\n${t('status.connectionTest')}...`;
            const testResult = await augmentDetector.testApiConnection();
            statusMessage += `\n${t('status.connectionTest')}: ${testResult.success ? t('status.success') : t('status.failed')}`;

            if (!testResult.success && testResult.error) {
                statusMessage += `\n${t('status.error')}: ${testResult.error}`;

                if (testResult.error.includes('Authentication') || testResult.error.includes('401')) {
                    statusMessage += `\n\n${t('status.suggestion')}`;
                }
            }
        } else {
            statusMessage += `\n\n${t('status.pleaseConfigureAuth')}`;
        }

        vscode.window.showInformationMessage(statusMessage);
    });

    const webLoginCommand = vscode.commands.registerCommand('augmentTracker.webLogin', async () => {
        try {
            // 简化版本：直接打开浏览器并提示用户
            const loginUri = vscode.Uri.parse('https://app.augmentcode.com');
            await vscode.env.openExternal(loginUri);

            vscode.window.showInformationMessage(
                t('dialog.browserOpened'),
                t('dialog.setupCookies'),
                t('dialog.cancel')
            ).then(selection => {
                if (selection === t('dialog.setupCookies')) {
                    vscode.commands.executeCommand('augmentTracker.setupCookies');
                }
            });

        } catch (error) {
            vscode.window.showErrorMessage(`${t('dialog.webLoginError')}: ${error}`);
        }
    });

    const testTimerCommand = vscode.commands.registerCommand('augmentTracker.testTimer', async () => {
        const refreshInterval = configManager.getRefreshInterval();
        vscode.window.showInformationMessage(
            `🔄 Timer Test:\n` +
            `Refresh Interval: ${refreshInterval} seconds\n` +
            `Next refresh in: ${refreshInterval}s\n` +
            `Check console for timer logs.`
        );

        console.log('🧪 Timer Test - Current settings:');
        console.log(`   Refresh interval: ${refreshInterval} seconds`);
        console.log(`   Has real data fetcher: ${usageTracker.hasRealUsageData() ? 'Yes' : 'No'}`);
        console.log(`   Authentication status: ${(augmentDetector as any).apiClient?.hasAuthToken() || (augmentDetector as any).apiClient?.hasCookies() ? 'Available' : 'Not available'}`);

        // 手动触发一次数据获取
        console.log('🔄 Manually triggering data fetch...');
        try {
            const apiClient = (augmentDetector as any).apiClient;
            if (apiClient && (apiClient.hasAuthToken() || apiClient.hasCookies())) {
                const creditsResult = await apiClient.getCreditsInfo();
                console.log('📊 Manual fetch result:', creditsResult.success ? 'Success' : 'Failed');
                if (creditsResult.success) {
                    console.log('📊 Manual fetch data:', JSON.stringify(creditsResult.data, null, 2));
                }
            } else {
                console.log('❌ No authentication for manual fetch');
            }
        } catch (error) {
            console.log('❌ Manual fetch error:', error);
        }
    });

    // Start tracking first
    usageTracker.startTracking();
    statusBarManager.show();

    // 然后设置真实数据获取器
    console.log('🔧 Setting up real data fetcher...');
    usageTracker.setRealDataFetcher(async () => {
        try {
            console.log('🔄 [' + new Date().toLocaleTimeString() + '] Fetching real usage data...');
            const apiClient = (augmentDetector as any).apiClient;

            if (apiClient && (apiClient.hasAuthToken() || apiClient.hasCookies())) {
                // 获取Credits API数据
                const creditsResult = await apiClient.getCreditsInfo();

                if (creditsResult.success) {
                    console.log('📊 Credits API Response:', JSON.stringify(creditsResult.data, null, 2));

                    const usageData = await apiClient.parseUsageResponse(creditsResult);
                    if (usageData) {
                        console.log('✅ Parsed usage data:', {
                            totalUsage: usageData.totalUsage,
                            usageLimit: usageData.usageLimit,
                            dailyUsage: usageData.dailyUsage,
                            dataSource: 'Credits API',
                            timestamp: new Date().toLocaleTimeString()
                        });

                        await usageTracker.updateWithRealData(usageData);

                        // 更新状态栏
                        const status = await augmentDetector.getAugmentStatus();
                        status.hasRealData = true;
                        status.usageData = usageData;
                        statusBarManager.updateAugmentStatus(status);
                    }
                } else {
                    console.log('❌ Credits API failed:', creditsResult.error);
                }
            } else {
                console.log('⚠️ No authentication available for real data fetch');
            }
        } catch (error) {
            console.error('❌ Error in real data fetcher:', error);
        }
    });

    // Check for Augment plugin and get detailed status
    augmentDetector.getAugmentStatus().then(status => {
        console.log('Augment Status:', status);

        if (status.installed) {
            if (status.active) {
                if (status.hasRealData) {
                    console.log(`Augment plugin detected with real data (${status.integrationMethod})`);
                    // Update usage tracker with real data if available
                    if (status.usageData) {
                        usageTracker.updateWithRealData(status.usageData);
                    }
                } else {
                    console.log('Augment plugin detected but no real data available');
                }
            } else {
                console.log('Augment plugin installed but not active');
            }
        } else {
            console.log('Augment plugin not detected, using simulation mode');
        }

        // Update status bar with integration info
        statusBarManager.updateAugmentStatus(status);
    });

    // Add to subscriptions
    context.subscriptions.push(
        resetUsageCommand,
        openSettingsCommand,
        showDetailsCommand,
        setupCookiesCommand,
        checkAuthStatusCommand,
        webLoginCommand,
        statusBarManager,
        usageTracker
    );

    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('augmentTracker')) {
            configManager.reloadConfig();
            statusBarManager.updateDisplay();
        }

        // Check if Augment configuration changed
        if (event.affectsConfiguration('augment')) {
            // Re-detect Augment status
            augmentDetector.getAugmentStatus().then(status => {
                statusBarManager.updateAugmentStatus(status);
                if (status.hasRealData && status.usageData) {
                    usageTracker.updateWithRealData(status.usageData);
                }
            });
        }
    });

    // Monitor Augment extension state changes
    const augmentStateWatcher = augmentDetector.onAugmentStateChange(status => {
        console.log('Augment state changed:', status);
        statusBarManager.updateAugmentStatus(status);

        if (status.hasRealData && status.usageData) {
            usageTracker.updateWithRealData(status.usageData);
        }
    });

    context.subscriptions.push(augmentStateWatcher);
}

function showUsageDetails() {
    const usage = usageTracker.getCurrentUsage();
    const limit = configManager.getUsageLimit();
    const percentage = Math.round((usage / limit) * 100);

    const message = `
${t('usage.title')}
• ${t('usage.currentUsage')}: ${usage} ${t('credits')}
• ${t('usage.monthlyLimit')}: ${limit} ${t('credits')}
• ${t('usage.usagePercentage')}: ${percentage}%
• ${t('usage.remaining')}: ${limit - usage} ${t('credits')}

${t('usage.lastReset')}: ${usageTracker.getLastResetDate()}
    `.trim();

    vscode.window.showInformationMessage(message, t('usage.resetUsage'), t('usage.openSettings')).then(selection => {
        if (selection === t('usage.resetUsage')) {
            vscode.commands.executeCommand('augmentTracker.resetUsage');
        } else if (selection === t('usage.openSettings')) {
            vscode.commands.executeCommand('augmentTracker.openSettings');
        }
    });
}

export function deactivate() {
    if (statusBarManager) {
        statusBarManager.dispose();
    }
    if (usageTracker) {
        usageTracker.dispose();
    }
}

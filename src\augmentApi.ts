import * as vscode from 'vscode';

export interface AugmentApiResponse {
    success: boolean;
    data?: any;
    error?: string;
}

export interface AugmentUsageData {
    totalUsage?: number;
    usageLimit?: number;
    dailyUsage?: number;
    monthlyUsage?: number;
    lastUpdate?: string;
    subscriptionType?: string;
    renewalDate?: string;
}

export class AugmentApiClient {
    private readonly API_BASE_URL = 'https://i1.api.augmentcode.com';
    private authToken: string | null = null;

    constructor() {
        this.loadAuthToken();
    }

    private async loadAuthToken(): Promise<void> {
        try {
            // Try to get auth token from VSCode secrets
            const secrets = vscode.workspace.getConfiguration().get('augment.authToken') as string;
            if (secrets) {
                this.authToken = secrets;
            }
        } catch (error) {
            console.log('No Augment auth token found');
        }
    }

    async setAuthToken(token: string): Promise<void> {
        this.authToken = token;
        // Store in VSCode configuration (not recommended for production)
        await vscode.workspace.getConfiguration().update('augment.authToken', token, vscode.ConfigurationTarget.Global);
    }

    private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<AugmentApiResponse> {
        try {
            const url = `${this.API_BASE_URL}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (this.authToken) {
                headers['Authorization'] = `Bearer ${this.authToken}`;
            }

            // 详细日志记录
            console.log(`🌐 Augment API Request:`, {
                url,
                method: options.method || 'GET',
                headers: { ...headers, Authorization: this.authToken ? 'Bearer [HIDDEN]' : 'None' },
                timestamp: new Date().toISOString()
            });

            const startTime = Date.now();
            const response = await fetch(url, {
                ...options,
                headers
            });
            const duration = Date.now() - startTime;

            console.log(`📡 Augment API Response:`, {
                url,
                status: response.status,
                statusText: response.statusText,
                duration: `${duration}ms`,
                headers: Object.fromEntries(response.headers.entries()),
                timestamp: new Date().toISOString()
            });

            if (response.status === 401) {
                console.warn('🔒 Authentication failed - token may be invalid or expired');
                return {
                    success: false,
                    error: 'Authentication required. Please provide your Augment auth token.'
                };
            }

            if (!response.ok) {
                console.error(`❌ API request failed:`, {
                    status: response.status,
                    statusText: response.statusText,
                    url
                });
                return {
                    success: false,
                    error: `API request failed: ${response.status} ${response.statusText}`
                };
            }

            const data = await response.json();
            console.log(`✅ API request successful:`, {
                url,
                dataKeys: Object.keys(data),
                dataSize: JSON.stringify(data).length,
                timestamp: new Date().toISOString()
            });

            return {
                success: true,
                data
            };
        } catch (error) {
            console.error(`🚨 Network error:`, {
                endpoint,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
            return {
                success: false,
                error: `Network error: ${error}`
            };
        }
    }

    async checkHealth(): Promise<AugmentApiResponse> {
        try {
            const response = await fetch(`${this.API_BASE_URL}/health`);
            if (response.ok) {
                return {
                    success: true,
                    data: { status: 'healthy' }
                };
            }
            return {
                success: false,
                error: 'API health check failed'
            };
        } catch (error) {
            return {
                success: false,
                error: `Health check failed: ${error}`
            };
        }
    }

    async getUserInfo(): Promise<AugmentApiResponse> {
        return await this.makeRequest('/user');
    }

    async getUsageData(): Promise<AugmentApiResponse> {
        return await this.makeRequest('/usage');
    }

    async getSubscriptionInfo(): Promise<AugmentApiResponse> {
        return await this.makeRequest('/subscription');
    }

    async parseUsageResponse(response: AugmentApiResponse): Promise<AugmentUsageData | null> {
        if (!response.success || !response.data) {
            return null;
        }

        try {
            const data = response.data;
            return {
                totalUsage: data.totalUsage || data.usage || data.count,
                usageLimit: data.limit || data.quota || data.maxUsage,
                dailyUsage: data.dailyUsage || data.today,
                monthlyUsage: data.monthlyUsage || data.thisMonth,
                lastUpdate: data.lastUpdate || data.updatedAt || new Date().toISOString(),
                subscriptionType: data.plan || data.tier || data.subscriptionType,
                renewalDate: data.renewalDate || data.nextBilling
            };
        } catch (error) {
            console.error('Error parsing usage response:', error);
            return null;
        }
    }

    async promptForAuthToken(): Promise<boolean> {
        const token = await vscode.window.showInputBox({
            prompt: 'Enter your Augment authentication token',
            placeHolder: 'Bearer token from Augment dashboard',
            password: true,
            ignoreFocusOut: true,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Token cannot be empty';
                }
                return null;
            }
        });

        if (token) {
            await this.setAuthToken(token.trim());
            return true;
        }
        return false;
    }

    async testConnection(): Promise<AugmentApiResponse> {
        // First check health
        const healthCheck = await this.checkHealth();
        if (!healthCheck.success) {
            return healthCheck;
        }

        // Then try to get user info to test auth
        const userInfo = await this.getUserInfo();
        return userInfo;
    }

    hasAuthToken(): boolean {
        return this.authToken !== null && this.authToken.length > 0;
    }

    clearAuthToken(): void {
        this.authToken = null;
        vscode.workspace.getConfiguration().update('augment.authToken', undefined, vscode.ConfigurationTarget.Global);
    }
}

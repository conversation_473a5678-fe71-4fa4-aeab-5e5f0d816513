# 手动功能指南 | Manual Features Guide

## 新增功能 | New Features

### 🔄 手动刷新 | Manual Refresh

#### 功能描述 | Description
手动触发数据刷新，立即从Augment API获取最新的使用统计。

Manually trigger data refresh to immediately fetch the latest usage statistics from Augment API.

#### 使用方法 | Usage
```
Ctrl+Shift+P → "🔄 Manual Refresh" / "🔄 手动刷新"
```

#### 功能特点 | Features
- ✅ **立即刷新**: 不等待自动刷新间隔，立即获取最新数据
- ✅ **状态反馈**: 显示刷新结果和最新的使用统计
- ✅ **错误处理**: 显示详细的错误信息和建议
- ✅ **认证检查**: 自动检查认证状态

#### 预期输出 | Expected Output

##### 成功时 | On Success
```
手动刷新已触发
数据刷新成功: 7/56 积分
```

##### 失败时 | On Failure
```
数据刷新失败: Authentication failed
```

##### 无认证时 | No Authentication
```
未配置认证信息，无法刷新
```

### 🌐 语言设置 | Language Setting

#### 功能描述 | Description
手动设置插件界面语言，支持自动检测、英文和简体中文。

Manually set the plugin interface language, supporting auto-detection, English, and Simplified Chinese.

#### 使用方法 | Usage
```
Ctrl+Shift+P → "🌐 Set Language" / "🌐 设置语言"
```

#### 支持的语言 | Supported Languages
- **自动（跟随VSCode）| Auto (Follow VSCode)**: 根据VSCode语言设置自动选择
- **English**: 英文界面
- **简体中文**: 中文界面

#### 设置流程 | Setting Process

1. **打开语言选择** | Open Language Selection
   ```
   Ctrl+Shift+P → "🌐 Set Language"
   ```

2. **选择语言** | Select Language
   - 当前语言会显示 ✓ 标记
   - Current language shows ✓ mark

3. **应用更改** | Apply Changes
   - 立即生效部分功能
   - 建议重启VSCode以完全应用

#### 配置存储 | Configuration Storage

语言设置存储在VSCode配置中：
Language setting is stored in VSCode configuration:

```json
{
  "augmentTracker.language": "zh-cn"  // "auto" | "en" | "zh-cn"
}
```

## 配置选项 | Configuration Options

### 新增配置 | New Configuration

#### augmentTracker.language
- **类型 | Type**: `string`
- **默认值 | Default**: `"auto"`
- **选项 | Options**: `"auto"` | `"en"` | `"zh-cn"`
- **描述 | Description**: 界面语言设置 | Interface language setting

#### 配置示例 | Configuration Example
```json
{
  "augmentTracker.enabled": true,
  "augmentTracker.refreshInterval": 5,
  "augmentTracker.showInStatusBar": true,
  "augmentTracker.language": "zh-cn",
  "augment.cookies": "your-cookies-here"
}
```

## 使用场景 | Use Cases

### 手动刷新适用场景 | Manual Refresh Use Cases

1. **即时查看**: 刚完成Augment操作，想立即查看使用量变化
2. **故障排查**: 自动刷新出现问题时，手动触发检查
3. **演示展示**: 向他人展示插件功能时的即时刷新
4. **数据验证**: 验证API连接和数据准确性

### 语言设置适用场景 | Language Setting Use Cases

1. **多语言团队**: 团队成员使用不同语言的VSCode
2. **语言偏好**: 个人偏好与VSCode语言设置不同
3. **测试验证**: 开发者测试多语言功能
4. **演示需求**: 根据演示对象调整界面语言

## 技术实现 | Technical Implementation

### 手动刷新实现 | Manual Refresh Implementation

```typescript
// 手动刷新命令
const manualRefreshCommand = vscode.commands.registerCommand('augmentTracker.manualRefresh', async () => {
    // 1. 显示刷新提示
    vscode.window.showInformationMessage(t('refresh.manual'));
    
    // 2. 检查认证状态
    if (apiClient && (apiClient.hasAuthToken() || apiClient.hasCookies())) {
        // 3. 调用API获取数据
        const creditsResult = await apiClient.getCreditsInfo();
        
        // 4. 更新数据和状态栏
        if (creditsResult.success) {
            await usageTracker.updateWithRealData(usageData);
            statusBarManager.updateAugmentStatus(status);
        }
    }
});
```

### 语言设置实现 | Language Setting Implementation

```typescript
// 语言设置命令
const setLanguageCommand = vscode.commands.registerCommand('augmentTracker.setLanguage', async () => {
    // 1. 获取当前语言设置
    const currentLanguage = vscode.workspace.getConfiguration('augmentTracker').get<string>('language', 'auto');
    
    // 2. 显示语言选择列表
    const languageOptions = [
        { label: `自动（跟随VSCode） ${currentLanguage === 'auto' ? '✓' : ''}`, value: 'auto' },
        { label: `English ${currentLanguage === 'en' ? '✓' : ''}`, value: 'en' },
        { label: `简体中文 ${currentLanguage === 'zh-cn' ? '✓' : ''}`, value: 'zh-cn' }
    ];
    
    // 3. 更新配置和语言
    if (selection && selection.value !== currentLanguage) {
        await vscode.workspace.getConfiguration('augmentTracker').update('language', selection.value, vscode.ConfigurationTarget.Global);
        I18n.setLocale(selection.value === 'auto' ? 'en' : selection.value);
    }
});
```

## 故障排除 | Troubleshooting

### 手动刷新问题 | Manual Refresh Issues

#### 问题：显示"未配置认证信息"
**解决方案**:
1. 运行 `Ctrl+Shift+P` → "Check Authentication Status"
2. 如果未配置，运行 "🌐 Web Login (Auto)" 或 "Setup Browser Cookies"

#### 问题：刷新失败
**解决方案**:
1. 检查网络连接
2. 验证Cookie是否过期
3. 查看控制台错误信息

### 语言设置问题 | Language Setting Issues

#### 问题：语言未完全切换
**解决方案**:
1. 重启VSCode: `Ctrl+Shift+P` → "Developer: Reload Window"
2. 检查配置: `Ctrl+,` → 搜索 "augmentTracker.language"

#### 问题：部分文本仍为英文
**解决方案**:
1. 确认选择了正确的语言
2. 重新安装插件
3. 检查VSCode语言设置

## 命令列表 | Command List

### 新增命令 | New Commands
- **🔄 Manual Refresh | 🔄 手动刷新**: `augmentTracker.manualRefresh`
- **🌐 Set Language | 🌐 设置语言**: `augmentTracker.setLanguage`

### 完整命令列表 | Complete Command List
1. Reset Usage Statistics | 重置使用统计
2. Open Settings | 打开设置
3. Show Usage Details | 显示使用详情
4. Setup Browser Cookies | 设置浏览器Cookie
5. Check Authentication Status | 检查认证状态
6. 🌐 Web Login (Auto) | 🌐 网页自动登录
7. **🔄 Manual Refresh | 🔄 手动刷新** ← 新增
8. **🌐 Set Language | 🌐 设置语言** ← 新增

## 更新日志 | Changelog

### v1.1.0 新功能 | New Features
- ✅ 添加手动刷新功能
- ✅ 添加语言设置功能
- ✅ 支持运行时语言切换
- ✅ 增强用户体验和控制能力

**现在您可以手动控制数据刷新和界面语言！** 🎛️

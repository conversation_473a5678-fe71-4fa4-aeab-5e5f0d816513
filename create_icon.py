#!/usr/bin/env python3
"""
创建VSCode插件图标
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    # 创建128x128的图标
    size = 128
    img = Image.new('RGBA', (size, size), (0, 122, 204, 255))  # VSCode蓝色
    draw = ImageDraw.Draw(img)
    
    # 绘制圆角矩形背景
    corner_radius = 16
    draw.rounded_rectangle(
        [(8, 8), (size-8, size-8)], 
        radius=corner_radius, 
        fill=(0, 122, 204, 255),
        outline=(255, 255, 255, 100),
        width=2
    )
    
    # 绘制图表图标
    # 绘制柱状图
    bar_width = 12
    bar_spacing = 16
    start_x = 25
    start_y = 90
    
    bars = [30, 50, 40, 60, 45]  # 不同高度的柱子
    colors = [(255, 255, 255, 200)] * 5
    
    for i, height in enumerate(bars):
        x = start_x + i * (bar_width + bar_spacing)
        y = start_y - height
        draw.rectangle(
            [(x, y), (x + bar_width, start_y)],
            fill=colors[i]
        )
    
    # 绘制文字 "A"
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 36)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 绘制大写字母 A
    text = "A"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    text_x = (size - text_width) // 2
    text_y = 20
    
    draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # 保存图标
    img.save('icon.png', 'PNG')
    print("✅ 图标已创建: icon.png")

if __name__ == "__main__":
    create_icon()

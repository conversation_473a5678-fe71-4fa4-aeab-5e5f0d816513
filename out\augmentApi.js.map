{"version": 3, "file": "augmentApi.js", "sourceRoot": "", "sources": ["../src/augmentApi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAyCjC,MAAa,gBAAgB;IAMzB;QALiB,iBAAY,GAAG,iCAAiC,CAAC;QACjD,iBAAY,GAAG,6BAA6B,CAAC;QACtD,cAAS,GAAkB,IAAI,CAAC;QAChC,YAAO,GAAkB,IAAI,CAAC;QAGlC,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,IAAI;YACA,4CAA4C;YAC5C,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,mBAAmB,CAAW,CAAC;YACvF,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;aAC5B;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAC9C;IACL,CAAC;IAEO,KAAK,CAAC,WAAW;QACrB,IAAI;YACA,+CAA+C;YAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,iBAAiB,CAAW,CAAC;YACrF,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;aAC1B;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;SAC3C;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAa;QAC5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,iEAAiE;QACjE,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACpH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAe;QAC5B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,gCAAgC;QAChC,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACpH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,UAAuB,EAAE;QACjE,IAAI;YACA,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,QAAQ,EAAE,CAAC;YAC9C,MAAM,OAAO,GAA2B;gBACpC,cAAc,EAAE,kBAAkB;gBAClC,GAAG,OAAO,CAAC,OAAO;aACrB,CAAC;YAEF,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;aACzD;YAED,qBAAqB;YACrB,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;gBACjC,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;gBACvC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;aACzC;YAED,SAAS;YACT,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;gBACnC,GAAG;gBACH,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK;gBAC/B,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,EAAE;gBACnF,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAC9B,GAAG,OAAO;gBACV,OAAO;aACV,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE;gBACpC,GAAG;gBACH,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,QAAQ,EAAE,GAAG,QAAQ,IAAI;gBACzB,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;gBACzB,OAAO,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBAC3E,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,kEAAkE;iBAC5E,CAAC;aACL;YAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE;oBACnC,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,GAAG;iBACN,CAAC,CAAC;gBACH,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,uBAAuB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE;iBACzE,CAAC;aACL;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBACrC,GAAG;gBACH,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC3B,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM;gBACrC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;YAEH,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,IAAI;aACP,CAAC;SACL;QAAC,OAAO,KAAU,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBAC/B,QAAQ;gBACR,KAAK,EAAE,KAAK,EAAE,OAAO,IAAI,eAAe;gBACxC,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,gBAAgB;gBACvC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;YACH,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kBAAkB,KAAK,EAAE,OAAO,IAAI,KAAK,EAAE;aACrD,CAAC;SACL;IACL,CAAC;IAED,KAAK,CAAC,WAAW;QACb,IAAI;YACA,qBAAqB;YACrB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,QAAQ,CAAC,OAAO,EAAE;gBAClB,OAAO;oBACH,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,IAAI,EAAE;iBACnD,CAAC;aACL;YACD,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;aACnC,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB,KAAK,EAAE;aACzC,CAAC;SACL;IACL,CAAC;IAED,KAAK,CAAC,WAAW;QACb,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,YAAY;QACd,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,YAAY;QACd,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAA4B;QACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QAED,IAAI;YACA,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE3B,mBAAmB;YACnB,IAAI,IAAI,CAAC,8BAA8B,KAAK,SAAS,EAAE;gBACnD,mBAAmB;gBACnB,OAAO;oBACH,UAAU,EAAE,IAAI,CAAC,8BAA8B;oBAC/C,UAAU,EAAE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,8BAA8B;oBAC1E,UAAU,EAAE,IAAI,CAAC,8BAA8B;oBAC/C,YAAY,EAAE,IAAI,CAAC,8BAA8B;oBACjD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACpC,gBAAgB,EAAE,WAAW;oBAC7B,WAAW,EAAE,SAAS;iBACzB,CAAC;aACL;YAED,YAAY;YACZ,IAAI,IAAI,CAAC,+BAA+B,KAAK,SAAS,EAAE;gBACpD,OAAO;oBACH,UAAU,EAAE,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B;oBACvF,UAAU,EAAE,IAAI,CAAC,+BAA+B;oBAChD,UAAU,EAAE,SAAS;oBACrB,YAAY,EAAE,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B;oBACzF,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACpC,gBAAgB,EAAE,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ;oBACvD,WAAW,EAAE,IAAI,CAAC,gBAAgB;iBACrC,CAAC;aACL;YAED,UAAU;YACV,OAAO;gBACH,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;gBAC5D,UAAU,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;gBAC7D,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK;gBACzC,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS;gBACjD,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACzE,gBAAgB,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB;gBACjE,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW;aACpD,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB;QACpB,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3C,MAAM,EAAE,yCAAyC;YACjD,WAAW,EAAE,qCAAqC;YAClD,QAAQ,EAAE,IAAI;YACd,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrC,OAAO,uBAAuB,CAAC;iBAClC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;YACP,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,gBAAgB;QAClB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,MAAM,EAAE,oCAAoC;YAC5C,WAAW,EAAE,kEAAkE;YAC/E,QAAQ,EAAE,IAAI;YACd,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrC,OAAO,yBAAyB,CAAC;iBACpC;gBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;oBAC9B,OAAO,mDAAmD,CAAC;iBAC9D;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE;YACT,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,qBAAqB;QACrB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YACtB,OAAO,WAAW,CAAC;SACtB;QAED,yCAAyC;QACzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1C,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,YAAY;QACR,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAChE,CAAC;IAED,UAAU;QACN,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED,UAAU;QACN,OAAO,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;IACpD,CAAC;IAED,cAAc;QACV,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAClH,CAAC;IAED,YAAY;QACR,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAChH,CAAC;IAED,YAAY;QACR,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;CACJ;AAxTD,4CAwTC"}
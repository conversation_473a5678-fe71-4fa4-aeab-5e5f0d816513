# Augment Usage Tracker - 增强功能说明

## 🎯 新增功能概览

### 1. 真实数据集成支持
- **Augment检测增强**：智能检测Augment插件状态
- **多种数据源**：支持模拟数据、真实API数据、用户输入数据
- **数据源指示器**：状态栏显示数据来源（●真实数据 ○模拟数据）

### 2. 用户交互改进
- **手动数据输入**：允许用户输入真实使用数据
- **详细状态显示**：鼠标悬停显示完整信息
- **智能颜色编码**：基于使用率和数据源的颜色提示

### 3. 高级监控功能
- **实时状态监控**：监听Augment插件状态变化
- **配置变化检测**：自动响应Augment配置更新
- **缓存优化**：30秒缓存减少性能开销

## 🔧 技术实现详情

### 数据源类型
1. **simulation**：基于编辑器事件的模拟数据
2. **augment_api**：通过Augment公开API获取的真实数据
3. **augment_daily**：Augment每日使用数据
4. **user_input**：用户手动输入的真实数据

### 状态栏显示格式
```
$(pulse) Augment: 123/1000 ●
                           ↑
                    数据源指示器
● = 真实数据
○ = 模拟数据
```

### 详细工具提示信息
```
Augment Usage Tracker
Current: 123
Limit: 1000
Usage: 12%
Remaining: 877
Data Source: Real data from Augment API

Augment Plugin:
• Installed: Yes
• Active: Yes
• Version: 1.2.3
• Integration: api

Click to open Augment website
```

## 📋 新增命令

### `Augment Tracker: Input Real Usage Data`
允许用户手动输入真实的Augment使用数据：
- **输入使用量**：设置当前使用计数
- **输入限额**：设置使用限额
- **数据验证**：确保输入的是有效数字

## 🎨 视觉改进

### 颜色编码系统
- **正常状态**：默认颜色（使用率 < 75%）
- **警告状态**：黄色（使用率 75-89%）
- **错误状态**：红色（使用率 ≥ 90%）
- **真实数据**：突出显示（有真实数据时）

### 状态指示器
- **●**：表示使用真实数据
- **○**：表示使用模拟数据
- **颜色变化**：根据使用率动态调整

## 🔍 Augment集成检测

### 检测层级
1. **基础检测**：检查Augment扩展是否安装
2. **状态检测**：检查扩展是否激活
3. **版本检测**：获取扩展版本信息
4. **API检测**：尝试调用公开命令获取数据
5. **配置检测**：监听Augment配置变化

### 集成方法
- **扩展间通信**：尝试调用Augment公开命令
- **配置监听**：监听workspace配置变化
- **状态缓存**：30秒缓存避免频繁检测

## 🚀 使用方法

### 1. 基本使用
插件激活后自动开始工作，无需额外配置。

### 2. 输入真实数据
```
Ctrl+Shift+P → "Augment Tracker: Input Real Usage Data"
```

### 3. 查看详细信息
```
Ctrl+Shift+P → "Augment Tracker: Show Usage Details"
```

### 4. 配置设置
```
Ctrl+, → 搜索 "augmentTracker"
```

## 📊 数据流程

### 启动流程
1. 插件激活
2. 检测Augment状态
3. 尝试获取真实数据
4. 回退到模拟数据
5. 显示状态栏

### 数据更新流程
1. 监听编辑器事件
2. 检查是否有真实数据
3. 更新使用计数
4. 刷新状态栏显示
5. 保存到本地存储

### 真实数据集成流程
1. 检测Augment插件
2. 尝试调用公开API
3. 解析返回数据
4. 更新本地计数
5. 标记数据源类型

## 🔒 安全考虑

### 合规设计
- 不访问其他扩展的私有数据
- 仅使用公开的VSCode API
- 遵循扩展安全最佳实践

### 隐私保护
- 所有数据本地存储
- 不向外部服务器发送数据
- 用户完全控制数据

## 🎯 未来扩展

### 计划功能
1. **更多数据源**：支持更多AI编程助手
2. **统计分析**：提供使用趋势分析
3. **导出功能**：支持数据导出
4. **团队功能**：支持团队使用统计

### API预留
代码中预留了扩展接口，便于将来集成官方API。

{"version": 3, "file": "augmentDetector.js", "sourceRoot": "", "sources": ["../src/augmentDetector.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,6CAAkE;AAelE,MAAa,eAAe;IAOxB;QANiB,yBAAoB,GAAG,wBAAwB,CAAC;QACzD,sBAAiB,GAAW,CAAC,CAAC;QAC9B,iBAAY,GAAyB,IAAI,CAAC;QACjC,mBAAc,GAAG,KAAK,CAAC,CAAC,aAAa;QAIlD,IAAI,CAAC,SAAS,GAAG,IAAI,6BAAgB,EAAE,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,OAAO,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,eAAe;QACjB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnD,OAAO,SAAS,EAAE,QAAQ,IAAI,KAAK,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnD,OAAO,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,gBAAgB;QAClB,mCAAmC;QACnC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE;YAC3E,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnD,MAAM,SAAS,GAAG,SAAS,KAAK,SAAS,CAAC;QAC1C,MAAM,MAAM,GAAG,SAAS,EAAE,QAAQ,IAAI,KAAK,CAAC;QAC5C,MAAM,OAAO,GAAG,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;QAEhD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,SAAS,GAA+B,SAAS,CAAC;QACtD,IAAI,iBAAiB,GAAuC,MAAM,CAAC;QAEnE,IAAI,SAAS,IAAI,MAAM,EAAE;YACrB,qDAAqD;YACrD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACxD,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC;YACrC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC;YAChC,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC;SAC7C;QAED,MAAM,MAAM,GAAkB;YAC1B,SAAS;YACT,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS;YACT,iBAAiB;SACpB,CAAC;QAEF,mBAAmB;QACnB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;QAE7B,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,0CAA0C;IAC1C,oBAAoB,CAAC,QAAyC;QAC1D,OAAO,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC5C,uCAAuC;YACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7C,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,0DAA0D;IAClD,KAAK,CAAC,mBAAmB;QAK7B,sDAAsD;QACtD,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE;YAC9D,IAAI;gBACA,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACxC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;gBAE9D,IAAI,eAAe,CAAC,OAAO,EAAE;oBACzB,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;oBAE3F,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;oBAC3E,IAAI,SAAS,EAAE;wBACX,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;4BAClC,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,UAAU,EAAE,SAAS,CAAC,UAAU;4BAChC,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;yBAC/C,CAAC,CAAC;wBAEH,OAAO;4BACH,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACF,UAAU,EAAE,SAAS,CAAC,UAAU;gCAChC,UAAU,EAAE,SAAS,CAAC,UAAU;gCAChC,UAAU,EAAE,SAAS,CAAC,UAAU;6BACnC;4BACD,MAAM,EAAE,KAAK;yBAChB,CAAC;qBACL;iBACJ;qBAAM;oBACH,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;iBAC/D;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;aAC5D;SACJ;QAED,gDAAgD;QAChD,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAE3E,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC5B,4CAA4C;gBAC5C,KAAK,MAAM,OAAO,IAAI,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,cAAc,CAAC,EAAE;oBAC3E,IAAI;wBACA,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;wBAC7D,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;4BACtC,OAAO;gCACH,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gCACrC,MAAM,EAAE,KAAK;6BAChB,CAAC;yBACL;qBACJ;oBAAC,MAAM;wBACJ,4CAA4C;wBAC5C,SAAS;qBACZ;iBACJ;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;SAC5D;QAED,oDAAoD;QACpD,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,aAAa,EAAE;gBACf,OAAO;oBACH,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACF,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACvC;oBACD,MAAM,EAAE,WAAW;iBACtB,CAAC;aACL;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;SACjE;QAED,OAAO;YACH,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,MAAM;SACjB,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,MAAW;QAClC,IAAI;YACA,oDAAoD;YACpD,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,EAAE;gBACnD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC;gBAChE,OAAO;oBACH,UAAU,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ;oBACxD,UAAU,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK;oBACtC,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBAC3D,CAAC;aACL;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;SACzD;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAID,4DAA4D;IAC5D,KAAK,CAAC,kCAAkC;QAKpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE7C,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,SAAS,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAE3C,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC3C,2DAA2D;aAC9D;iBAAM;gBACH,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;aAC7D;SACJ;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;SAC1D;QAED,sCAAsC;QACtC,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAExD,OAAO;YACH,YAAY,EAAE,MAAM,CAAC,SAAS;YAC9B,OAAO;YACP,WAAW;SACd,CAAC;IACN,CAAC;IAED,sBAAsB;IACtB,KAAK,CAAC,iBAAiB;QACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;QAC1D,IAAI,OAAO,EAAE;YACT,mDAAmD;YACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,iBAAiB;QAKnB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAE/C,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC;gBACzC,QAAQ,EAAE,KAAK;aAClB,CAAC;SACL;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QACzD,OAAO;YACH,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,QAAQ,EAAE,IAAI;SACjB,CAAC;IACN,CAAC;IAED,KAAK,CAAC,eAAe;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE;YAChC,OAAO,IAAI,CAAC;SACf;QAED,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YACrD,IAAI,QAAQ,CAAC,OAAO,EAAE;gBAClB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;aAC5D;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;SACzD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,aAAa;QACT,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;IACzC,CAAC;CACJ;AA3RD,0CA2RC"}
{"version": 3, "file": "augmentDetector.js", "sourceRoot": "", "sources": ["../src/augmentDetector.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,MAAa,eAAe;IAA5B;QACqB,yBAAoB,GAAG,wBAAwB,CAAC;IA6HrE,CAAC;IA3HG,KAAK,CAAC,mBAAmB;QACrB,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC5E,OAAO,SAAS,KAAK,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC;SACxD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,OAAO,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,eAAe;QACjB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnD,OAAO,SAAS,EAAE,QAAQ,IAAI,KAAK,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnD,OAAO,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,gBAAgB;QAKlB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEnD,OAAO;YACH,SAAS,EAAE,SAAS,KAAK,SAAS;YAClC,MAAM,EAAE,SAAS,EAAE,QAAQ,IAAI,KAAK;YACpC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO;SAC3C,CAAC;IACN,CAAC;IAED,0CAA0C;IAC1C,oBAAoB,CAAC,QAAqC;QACtD,OAAO,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,yDAAyD;IACzD,KAAK,CAAC,mBAAmB;QACrB,IAAI;YACA,iEAAiE;YACjE,sEAAsE;YAEtE,uDAAuD;YACvD,sEAAsE;YACtE,OAAO,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;SACvC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,4DAA4D;IAC5D,KAAK,CAAC,mBAAmB;QAMrB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC9C,IAAI,CAAC,QAAQ,EAAE;gBACX,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,iCAAiC;iBAC3C,CAAC;aACL;YAED,sEAAsE;YACtE,yCAAyC;YACzC,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,iDAAiD;aAC3D,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC,KAAK,EAAE;aACnD,CAAC;SACL;IACL,CAAC;IAED,4DAA4D;IAC5D,KAAK,CAAC,kCAAkC;QAKpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE7C,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,SAAS,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAE3C,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC3C,2DAA2D;aAC9D;iBAAM;gBACH,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;aAC7D;SACJ;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;SAC1D;QAED,sCAAsC;QACtC,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAExD,OAAO;YACH,YAAY,EAAE,MAAM,CAAC,SAAS;YAC9B,OAAO;YACP,WAAW;SACd,CAAC;IACN,CAAC;CACJ;AA9HD,0CA8HC"}
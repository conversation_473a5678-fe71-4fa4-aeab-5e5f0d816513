# 📦 插件打包指南

## 🎯 打包步骤

### 方法1：使用vsce命令行工具

#### 1. 安装vsce工具
```bash
npm install -g @vscode/vsce
```

#### 2. 编译TypeScript代码
```bash
cd d:\workspace\augment-status
npx tsc
```

#### 3. 打包插件
```bash
vsce package
```

这会生成一个 `.vsix` 文件，例如：`augment-usage-tracker-1.0.0.vsix`

### 方法2：手动打包（如果vsce有问题）

#### 1. 确保所有文件都已编译
检查 `out` 文件夹是否包含所有 `.js` 文件：
- extension.js
- augmentApi.js
- augmentDetector.js
- statusBar.js
- usageTracker.js
- storage.js
- config.js
- webAuth.js

#### 2. 检查package.json格式
确保没有语法错误，所有逗号和括号都正确。

#### 3. 使用VSCode内置打包
1. 在VSCode中打开项目
2. 按 `Ctrl+Shift+P`
3. 输入 "Developer: Reload Window"
4. 然后按 `F5` 测试插件是否正常工作

## 📋 打包前检查清单

### ✅ 必需文件
- [x] `package.json` - 插件配置
- [x] `README.md` - 说明文档
- [x] `out/` 文件夹 - 编译后的JS文件
- [x] `src/` 文件夹 - TypeScript源码

### ✅ package.json检查
- [x] `name`: "augment-usage-tracker"
- [x] `version`: "1.0.0"
- [x] `publisher`: "augment-tracker"
- [x] `engines.vscode`: "^1.74.0"
- [x] `main`: "./out/extension.js"
- [x] `activationEvents`: ["*"]

### ✅ 功能检查
- [x] 所有命令都已注册
- [x] TypeScript编译无错误
- [x] 插件在开发模式下正常工作

## 🔧 故障排除

### 如果vsce命令失败：

#### 1. 检查Node.js版本
```bash
node --version
npm --version
```
确保使用Node.js 16+

#### 2. 清理并重新安装
```bash
npm cache clean --force
npm install -g @vscode/vsce
```

#### 3. 检查网络连接
确保可以访问npm registry

### 如果编译失败：

#### 1. 清理输出文件夹
```bash
rmdir /s out
npx tsc
```

#### 2. 检查TypeScript配置
确保 `tsconfig.json` 配置正确

#### 3. 检查依赖
```bash
npm install
```

## 📦 手动创建VSIX文件

如果自动打包工具不工作，可以手动创建：

### 1. 创建文件结构
```
augment-usage-tracker/
├── package.json
├── README.md
├── out/
│   ├── extension.js
│   ├── augmentApi.js
│   ├── augmentDetector.js
│   ├── statusBar.js
│   ├── usageTracker.js
│   ├── storage.js
│   ├── config.js
│   └── webAuth.js
└── [其他文件]
```

### 2. 压缩为ZIP文件
将所有文件压缩为ZIP格式

### 3. 重命名扩展名
将 `.zip` 改为 `.vsix`

## 🚀 安装打包后的插件

### 方法1：通过VSCode界面
1. 打开VSCode
2. 按 `Ctrl+Shift+X` 打开扩展面板
3. 点击 `...` 菜单
4. 选择 "Install from VSIX..."
5. 选择生成的 `.vsix` 文件

### 方法2：通过命令行
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

## 📊 验证安装

安装后验证插件是否正常工作：

1. **检查状态栏**：应该显示 Augment 使用统计
2. **测试命令**：`Ctrl+Shift+P` → "Augment Tracker"
3. **检查设置**：`Ctrl+,` → 搜索 "augmentTracker"

## 🎯 成功指标

### ✅ 打包成功的标志：
- 生成了 `.vsix` 文件
- 文件大小合理（通常几百KB到几MB）
- 可以在VSCode中安装
- 安装后功能正常

### ✅ 插件正常工作：
- 状态栏显示使用统计
- 所有命令都可以执行
- 设置页面可以访问
- API集成正常工作

## 📝 打包命令总结

```bash
# 完整打包流程
cd d:\workspace\augment-status
npm install
npx tsc
npx @vscode/vsce package

# 如果成功，会生成：
# augment-usage-tracker-1.0.0.vsix
```

**打包完成后，您就可以分发和安装这个插件了！** 🎉

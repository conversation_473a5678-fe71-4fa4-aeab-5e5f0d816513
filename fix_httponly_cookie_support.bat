@echo off
chcp 65001 > nul
echo ========================================
echo   修复HttpOnly Cookie支持问题
echo ========================================
echo.

echo 🚨 重大发现:
echo 用户提出了关键问题：如果Augment的_session cookie设置了HttpOnly标志，
echo 那么我们当前所有基于document.cookie的获取方式都会失败！
echo.

echo 🔍 HttpOnly Cookie问题分析:
echo [1] JavaScript无法访问HttpOnly cookies
echo [2] document.cookie不会返回HttpOnly cookies
echo [3] 自动提取器基于JavaScript的方法全部失效
echo [4] 这可能是自动刷新失败的根本原因
echo.

echo ✅ HttpOnly Cookie解决方案:
echo [1] 开发者工具Application标签页手动提取（推荐）
echo [2] Network标签页从请求头获取
echo [3] 书签工具辅助提取
echo [4] 增强的手动输入验证
echo [5] 多种提取方法集成
echo.

echo [1/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [2/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [3/4] 发布HttpOnly支持版本...
set /p confirm="确定要发布HttpOnly Cookie支持版本吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo ❌ 发布失败
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo [4/4] 生成HttpOnly Cookie使用指南...

echo ========================================
echo   HttpOnly Cookie提取指南
echo ========================================
echo.

echo 📋 方法1: Application标签页（推荐）
echo.
echo 详细步骤:
echo 1. 🌐 打开浏览器，访问 https://app.augmentcode.com
echo 2. 🔐 确保您已经登录到Augment
echo 3. 🛠️ 按F12打开开发者工具
echo 4. 📱 切换到"Application"标签页（Chrome）或"Storage"标签页（Firefox）
echo 5. 📂 在左侧面板中展开"Cookies"
echo 6. 🌐 点击"https://app.augmentcode.com"
echo 7. 🔍 在右侧找到名为"_session"的cookie
echo 8. 📋 双击"Value"列中的值，全选并复制
echo 9. ✅ 在VSCode中粘贴这个Value值
echo.

echo 🔧 方法2: Network标签页
echo.
echo 详细步骤:
echo 1. 🌐 打开浏览器，访问 https://app.augmentcode.com
echo 2. 🔐 确保您已经登录到Augment
echo 3. 🛠️ 按F12打开开发者工具
echo 4. 🌐 切换到"Network"标签页
echo 5. 🔄 刷新页面或访问任意API（如/api/user）
echo 6. 📋 点击任意请求（推荐选择/api/user）
echo 7. 📝 在右侧面板中找到"Request Headers"
echo 8. 🍪 找到"Cookie"行，复制_session=xxx部分
echo 9. ✅ 在VSCode中粘贴完整的cookie字符串
echo.

echo 📋 Cookie格式示例:
echo.
echo 正确格式1（完整cookie）:
echo _session=eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMjM0NX0.abc123
echo.
echo 正确格式2（只有Value）:
echo eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxMjM0NX0.abc123
echo.
echo 错误格式（包含其他信息）:
echo _session=eyJ...; Path=/; HttpOnly; Secure
echo.

echo ========================================
echo   为什么HttpOnly Cookie难以获取
echo ========================================
echo.

echo 🔒 HttpOnly标志的作用:
echo - 防止JavaScript访问cookie
echo - 提高安全性，防止XSS攻击
echo - 只能通过HTTP请求发送
echo - document.cookie无法读取
echo.

echo ❌ 失效的方法:
echo - document.cookie（返回空或不包含HttpOnly cookie）
echo - JavaScript自动提取脚本
echo - 浏览器控制台cookie获取
echo - 任何基于JavaScript的自动化方法
echo.

echo ✅ 有效的方法:
echo - 开发者工具Application/Storage标签页
echo - Network标签页查看请求头
echo - 浏览器扩展（需要特殊权限）
echo - 手动复制粘贴
echo.

echo ========================================
echo   插件更新内容
echo ========================================
echo.

echo 🆕 新增功能:
echo [1] HttpOnlyCookieHandler类
echo     - 专门处理HttpOnly cookie提取
echo     - 提供多种提取方法选择
echo     - 详细的图文指导
echo.
echo [2] 增强的提取方法:
echo     - 📋 开发者工具Application标签页
echo     - 🔧 Network标签页请求头
echo     - 📝 智能手动输入验证
echo     - 🔖 书签工具辅助
echo.
echo [3] 改进的用户指导:
echo     - 详细的步骤说明文档
echo     - Cookie格式自动识别
echo     - Value值自动标准化
echo     - 错误提示和格式验证
echo.

echo ========================================
echo   测试验证步骤
echo ========================================
echo.

echo 🧪 测试1: 检查Cookie类型
echo 1. 在app.augmentcode.com页面按F12
echo 2. 控制台输入: document.cookie
echo 3. 检查是否包含_session cookie
echo 4. 如果没有，说明是HttpOnly cookie
echo.

echo 🧪 测试2: Application标签页提取
echo 1. 运行 Ctrl+Shift+P → "🌐 Web Login (Auto)"
echo 2. 选择"📋 手动输入Cookie"
echo 3. 选择"📋 开发者工具 - Application标签页"
echo 4. 按照指导从Application标签页复制cookie
echo 5. 验证是否成功配置和刷新
echo.

echo 🧪 测试3: Network标签页提取
echo 1. 选择"🔧 开发者工具 - Network标签页"
echo 2. 按照指导从Network请求头复制cookie
echo 3. 验证cookie格式是否正确识别
echo 4. 检查状态栏是否自动更新
echo.

echo 🧪 测试4: 格式验证
echo 1. 测试完整cookie格式: _session=xxx
echo 2. 测试Value格式: xxx（自动添加_session=前缀）
echo 3. 测试错误格式的提示
echo 4. 验证自动标准化功能
echo.

echo ========================================
echo   常见问题解决
echo ========================================
echo.

echo ❓ Q: 为什么document.cookie获取不到_session？
echo ✅ A: _session可能是HttpOnly cookie，JavaScript无法访问。
echo    请使用开发者工具Application标签页手动获取。
echo.

echo ❓ Q: 自动提取器为什么不工作？
echo ✅ A: 如果cookie是HttpOnly，所有JavaScript方法都会失效。
echo    这是浏览器的安全机制，需要手动提取。
echo.

echo ❓ Q: 如何确认cookie是否为HttpOnly？
echo ✅ A: 在Application标签页查看cookie，如果HttpOnly列显示✓，
echo    则该cookie无法通过JavaScript获取。
echo.

echo ❓ Q: 复制的cookie格式不对怎么办？
echo ✅ A: 插件会自动识别和标准化格式：
echo    - 只有Value值会自动添加_session=前缀
echo    - 完整格式会直接使用
echo    - 错误格式会提示重新输入
echo.

echo ✅ HttpOnly Cookie支持已完成！
echo.
echo 现在插件支持HttpOnly cookie的提取和使用，
echo 解决了JavaScript无法访问HttpOnly cookie的根本问题。
echo.
echo 主要改进:
echo - 专门的HttpOnly cookie处理器
echo - 多种手动提取方法指导
echo - 智能格式识别和验证
echo - 详细的用户指导文档
echo.
echo 如果之前自动刷新失败，很可能就是因为HttpOnly cookie问题。
echo 现在请使用新的手动提取方法重新配置认证。
echo.

pause

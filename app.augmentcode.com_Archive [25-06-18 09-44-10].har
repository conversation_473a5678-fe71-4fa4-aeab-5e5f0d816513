{"log": {"version": "1.2", "creator": {"name": "Firefox", "version": "139.0.4"}, "browser": {"name": "Firefox", "version": "139.0.4"}, "pages": [{"id": "page_1", "pageTimings": {"onContentLoad": 1705, "onLoad": 3019}, "startedDateTime": "2025-06-18T09:43:56.924+08:00", "title": "https://app.augmentcode.com/account/subscription"}], "entries": [{"startedDateTime": "2025-06-18T09:43:56.924+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/account/subscription", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.augmentcode.com/"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "Sec-Fetch-Dest", "value": "document"}, {"name": "Sec-Fetch-Mode", "value": "navigate"}, {"name": "Sec-Fetch-Site", "value": "same-site"}, {"name": "Priority", "value": "u=0, i"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2396}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "text/html; charset=utf-8"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:55 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "text/html; charset=utf-8", "size": 12532, "text": "<!DOCTYPE html><html lang=\"en\"><head><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/><link rel=\"stylesheet\" href=\"/assets/root-CUUSY10I.css\"/><link rel=\"stylesheet\" href=\"/assets/tailwind-DxnphuB3.css\"/><link rel=\"stylesheet\" href=\"/assets/MaterialIcon-D6aQ-Xs1.css\"/><script type=\"text/javascript\">(function() {\n    var i = \"analytics\",\n        analytics = window[i] = window[i] || [];\n    if (!analytics.initialize) {\n      if (analytics.invoked) {\n        window.console && console.error && console.error(\"Segment snippet included twice.\");\n      } else {\n        analytics.invoked = true;\n        analytics.methods = [\n          \"trackSubmit\", \"trackClick\", \"trackLink\", \"trackForm\", \"pageview\",\n          \"identify\", \"reset\", \"group\", \"track\", \"ready\", \"alias\", \"debug\",\n          \"page\", \"screen\", \"once\", \"off\", \"on\", \"addSourceMiddleware\",\n          \"addIntegrationMiddleware\", \"setAnonymousId\", \"addDestinationMiddleware\",\n          \"register\"\n        ];\n        analytics.factory = function(method) {\n          return function() {\n            if (window[i].initialized) {\n              return window[i][method].apply(window[i], arguments);\n            }\n            var args = Array.prototype.slice.call(arguments);\n            if ([\"track\", \"screen\", \"alias\", \"group\", \"page\", \"identify\"].indexOf(method) > -1) {\n              var canonicalLink = document.querySelector(\"link[rel='canonical']\");\n              args.push({\n                __t: \"bpc\",\n                c: (canonicalLink && canonicalLink.getAttribute(\"href\")) || void 0,\n                p: location.pathname,\n                u: location.href,\n                s: location.search,\n                t: document.title,\n                r: document.referrer\n              });\n            }\n            args.unshift(method);\n            analytics.push(args);\n            return analytics;\n          };\n        };\n        for (var n = 0; n < analytics.methods.length; n++) {\n          var key = analytics.methods[n];\n          analytics[key] = analytics.factory(key);\n        }\n        analytics.load = function(key, options) {\n          var script = document.createElement(\"script\");\n          script.type = \"text/javascript\";\n          script.async = true;\n          script.setAttribute(\"data-global-segment-analytics-key\", i);\n          script.src = \"https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js\";\n          var firstScript = document.getElementsByTagName(\"script\")[0];\n          firstScript.parentNode.insertBefore(script, firstScript);\n          analytics._loadOptions = options;\n        };\n        analytics._cdn = \"https://evs.grdt.augmentcode.com\";\n        analytics._writeKey = \"ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg\";\n        analytics.SNIPPET_VERSION = \"5.2.0\";\n        analytics.load(analytics._writeKey);\n        analytics.ready(() => {\n          window.posthog.init(\n            \"phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW\",\n            {\n              api_host: \"https://us.i.posthog.com\",\n              segment: window.analytics,\n              capture_pageview: false,\n              capture_pageleave: true,\n            }\n          );\n          if (window.analyticsInitialized) {\n            window.analyticsInitialized.resolve();\n          } else {\n            console.error(\"analytics deferred promise not found\");\n          }\n        });\n      }\n    }\n  })();\n  </script><script type=\"text/javascript\">(function(document, posthog) {\n    var methodList, methodIndex, scriptElement, firstScript;\n    if (!posthog.__SV) {\n      window.posthog = posthog;\n      posthog._i = [];\n      posthog.init = function(apiKey, config, namespace) {\n        // Create a stub function that collects method calls until the real library loads.\n        function createStub(target, methodName) {\n          var parts = methodName.split(\".\");\n          if (parts.length === 2) {\n            target = target[parts[0]];\n            methodName = parts[1];\n          }\n          target[methodName] = function() {\n            target.push([methodName].concat(Array.prototype.slice.call(arguments, 0)));\n          };\n        }\n        // Create and insert the script element to load the PostHog library.\n        scriptElement = document.createElement(\"script\");\n        scriptElement.type = \"text/javascript\";\n        scriptElement.crossOrigin = \"anonymous\";\n        scriptElement.async = true;\n        scriptElement.src = config.api_host + \"/static/array.js\";\n        firstScript = document.getElementsByTagName(\"script\")[0];\n        firstScript.parentNode.insertBefore(scriptElement, firstScript);\n        // Initialize the PostHog namespace.\n        var ph = posthog;\n        if (namespace !== undefined) {\n          ph = posthog[namespace] = [];\n        } else {\n          namespace = \"posthog\";\n        }\n        ph.people = ph.people || [];\n        ph.toString = function(stub) {\n          var label = \"posthog\";\n          if (namespace !== \"posthog\") {\n            label += \".\" + namespace;\n          }\n          if (!stub) {\n            label += \" (stub)\";\n          }\n          return label;\n        };\n        ph.people.toString = function() {\n          return ph.toString(1) + \".people (stub)\";\n        };\n        // List of methods to be stubbed until the library loads.\n        methodList = \"capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep\".split(\" \");\n        for (methodIndex = 0; methodIndex < methodList.length; methodIndex++) {\n          createStub(ph, methodList[methodIndex]);\n        }\n        // Store initialization arguments for later use.\n        posthog._i.push([apiKey, config, namespace]);\n      };\n      posthog.__SV = 1;\n    }\n  })(document, window.posthog || []);\n  </script><script>window.FEATURE_FLAGS = {\n  \"auth_central_user_tier_change\": true,\n  \"team_management\": true,\n  \"team_management_canary_domains\": \"augm.io,turing.com\"\n}</script></head><body><div data-is-root-theme=\"true\" data-accent-color=\"indigo\" data-gray-color=\"slate\" data-has-background=\"true\" data-panel-background=\"translucent\" data-radius=\"medium\" data-scaling=\"100%\" class=\"radix-themes\"><div></div><div role=\"region\" aria-label=\"Notifications (F8)\" tabindex=\"-1\" style=\"pointer-events:none\"><ol tabindex=\"-1\" class=\"ToastViewport\"></ol></div></div><script>((STORAGE_KEY, restoreKey) => {\n    if (!window.history.state || !window.history.state.key) {\n      let key = Math.random().toString(32).slice(2);\n      window.history.replaceState({\n        key\n      }, \"\");\n    }\n    try {\n      let positions = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || \"{}\");\n      let storedY = positions[restoreKey || window.history.state.key];\n      if (typeof storedY === \"number\") {\n        window.scrollTo(0, storedY);\n      }\n    } catch (error) {\n      console.error(error);\n      sessionStorage.removeItem(STORAGE_KEY);\n    }\n  })(\"positions\", null)</script><link rel=\"modulepreload\" href=\"/assets/manifest-ef5b2f0b.js\"/><link rel=\"modulepreload\" href=\"/assets/entry.client-Cqk7vRk3.js\"/><link rel=\"modulepreload\" href=\"/assets/jsx-runtime-Dt5Dsy05.js\"/><link rel=\"modulepreload\" href=\"/assets/index-Eiji7-uP.js\"/><link rel=\"modulepreload\" href=\"/assets/index-CuYdsPF2.js\"/><link rel=\"modulepreload\" href=\"/assets/index-BMzBsBkz.js\"/><link rel=\"modulepreload\" href=\"/assets/components-8LVd5hMH.js\"/><link rel=\"modulepreload\" href=\"/assets/QueryClientProvider-CaAf6rUc.js\"/><link rel=\"modulepreload\" href=\"/assets/queryClient.client-Pxw62N6W.js\"/><link rel=\"modulepreload\" href=\"/assets/client-only-DcZ3AzU0.js\"/><link rel=\"modulepreload\" href=\"/assets/index.modern-2OXpimgy.js\"/><link rel=\"modulepreload\" href=\"/assets/theme-CyI-9aTO.js\"/><link rel=\"modulepreload\" href=\"/assets/container-BmoDEPos.js\"/><link rel=\"modulepreload\" href=\"/assets/card-CEj3CUVr.js\"/><link rel=\"modulepreload\" href=\"/assets/link-CzC5xhUN.js\"/><link rel=\"modulepreload\" href=\"/assets/flex-C-nsUnVb.js\"/><link rel=\"modulepreload\" href=\"/assets/button-BxdqWCG5.js\"/><link rel=\"modulepreload\" href=\"/assets/Toast-BhxwFwyk.js\"/><link rel=\"modulepreload\" href=\"/assets/index-DrFu-skq.js\"/><link rel=\"modulepreload\" href=\"/assets/jotaiStore.client-Dkhl-jMB.js\"/><link rel=\"modulepreload\" href=\"/assets/react-DpjKU6Fx.js\"/><link rel=\"modulepreload\" href=\"/assets/index-B6fm_qMD.js\"/><link rel=\"modulepreload\" href=\"/assets/spinner-kkbphv-v.js\"/><link rel=\"modulepreload\" href=\"/assets/index-BPTGSM8V.js\"/><link rel=\"modulepreload\" href=\"/assets/base-button-DO4oml6W.js\"/><link rel=\"modulepreload\" href=\"/assets/get-subtree-CLC68NNC.js\"/><link rel=\"modulepreload\" href=\"/assets/index-BEPoejkm.js\"/><link rel=\"modulepreload\" href=\"/assets/react-icons.esm-dV2Ob1KC.js\"/><link rel=\"modulepreload\" href=\"/assets/root-zPKyvpqO.js\"/><link rel=\"modulepreload\" href=\"/assets/useQuery-CYzdQavM.js\"/><link rel=\"modulepreload\" href=\"/assets/animations-CVSyhzJ0.js\"/><link rel=\"modulepreload\" href=\"/assets/queryOptions-CtR-tNPw.js\"/><link rel=\"modulepreload\" href=\"/assets/BaseHeader-B2kCXxdF.js\"/><link rel=\"modulepreload\" href=\"/assets/string-ebdsFuK-.js\"/><link rel=\"modulepreload\" href=\"/assets/plan-change-pending-Miutpj5Q.js\"/><link rel=\"modulepreload\" href=\"/assets/subscription-creation-pending-sdC-FSjb.js\"/><link rel=\"modulepreload\" href=\"/assets/skeleton-CFmipj9_.js\"/><link rel=\"modulepreload\" href=\"/assets/user-1GS3I7Ak.js\"/><link rel=\"modulepreload\" href=\"/assets/box-DH5rNYvQ.js\"/><link rel=\"modulepreload\" href=\"/assets/index-BCk4OIxD.js\"/><link rel=\"modulepreload\" href=\"/assets/guards-DNyQniiG.js\"/><link rel=\"modulepreload\" href=\"/assets/pending-DpeJ2IVo.js\"/><link rel=\"modulepreload\" href=\"/assets/proto3-AB7woNkT.js\"/><link rel=\"modulepreload\" href=\"/assets/_layout-BrAfpxD0.js\"/><link rel=\"modulepreload\" href=\"/assets/ProgressPage-yQ-RTun1.js\"/><link rel=\"modulepreload\" href=\"/assets/heading-idERARwQ.js\"/><link rel=\"modulepreload\" href=\"/assets/_layout.account-BQ_VhLHA.js\"/><link rel=\"modulepreload\" href=\"/assets/MaterialIcon-8drXN99w.js\"/><link rel=\"modulepreload\" href=\"/assets/PlanPicker-D4IY66JJ.js\"/><link rel=\"modulepreload\" href=\"/assets/Card-KmsFLqhf.js\"/><link rel=\"modulepreload\" href=\"/assets/constants-Dk4nkva5.js\"/><link rel=\"modulepreload\" href=\"/assets/number-BpRe0Bla.js\"/><link rel=\"modulepreload\" href=\"/assets/format-DZFSXU3w.js\"/><link rel=\"modulepreload\" href=\"/assets/plural-B8N7ucUI.js\"/><link rel=\"modulepreload\" href=\"/assets/Enabled-7tVmoqEz.js\"/><link rel=\"modulepreload\" href=\"/assets/url-_DgIuZOw.js\"/><link rel=\"modulepreload\" href=\"/assets/toDate-qOSwr3PX.js\"/><link rel=\"modulepreload\" href=\"/assets/feature-flags.client-BVZhVN7G.js\"/><link rel=\"modulepreload\" href=\"/assets/badge-DHIKM5A7.js\"/><link rel=\"modulepreload\" href=\"/assets/_layout.account.subscription-CItSLN9N.js\"/><script>window.__remixContext = {\"basename\":\"/\",\"future\":{\"v3_fetcherPersist\":true,\"v3_relativeSplatPath\":true,\"v3_throwAbortReason\":true,\"v3_routeConfig\":false,\"v3_singleFetch\":false,\"v3_lazyRouteDiscovery\":false,\"unstable_optimizeDeps\":false},\"isSpaMode\":false,\"state\":{\"loaderData\":{\"root\":{\"earliestData\":{\"year\":2025,\"month\":4,\"day\":29},\"featureFlags\":{\"auth_central_user_tier_change\":true,\"team_management\":true,\"team_management_canary_domains\":\"augm.io,turing.com\"},\"user\":{\"userId\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"tenantId\":\"ce26583a2f0a0d8130e698bb1f651f27\",\"tenantName\":\"i0-vanguard1\",\"shardNamespace\":\"i0\",\"email\":\"<EMAIL>\",\"roles\":[],\"createdAt\":*************,\"sessionId\":\"93733f56-424d-4eb9-a7c8-5ec27847c6cb\"}},\"routes/_layout.account\":{},\"routes/_layout.account.subscription\":{\"userId\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"suspensions\":[]},\"routes/_layout\":null},\"actionData\":null,\"errors\":null}};</script><script type=\"module\" async=\"\">import \"/assets/manifest-ef5b2f0b.js\";\nimport * as route0 from \"/assets/root-zPKyvpqO.js\";\nimport * as route1 from \"/assets/_layout-BrAfpxD0.js\";\nimport * as route2 from \"/assets/_layout.account-BQ_VhLHA.js\";\nimport * as route3 from \"/assets/_layout.account.subscription-CItSLN9N.js\";\n\nwindow.__remixRouteModules = {\"root\":route0,\"routes/_layout\":route1,\"routes/_layout.account\":route2,\"routes/_layout.account.subscription\":route3};\n\nimport(\"/assets/entry.client-Cqk7vRk3.js\");</script></body></html>"}, "redirectURL": "", "headersSize": 258, "bodySize": 5095}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 1372, "receive": 0}, "time": 1372, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:43:59.925+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://evs.grdt.augmentcode.com/v1/projects/ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg/settings", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "evs.grdt.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/"}, {"name": "Origin", "value": "https://app.augmentcode.com"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-site"}, {"name": "If-Modified-Since", "value": "Fri, 30 May 2025 12:15:38 GMT"}, {"name": "If-None-Match", "value": "W/\"5ebcf240045a85a772860792f175e9bc\""}, {"name": "Priority", "value": "u=4"}], "cookies": [], "queryString": [], "headersSize": 616}, "response": {"status": 304, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "access-control-allow-origin", "value": "*"}, {"name": "access-control-allow-methods", "value": "GET, HEAD"}, {"name": "access-control-max-age", "value": "3000"}, {"name": "x-amz-replication-status", "value": "COMPLETED"}, {"name": "last-modified", "value": "Fri, 30 May 2025 12:15:38 GMT"}, {"name": "server", "value": "AmazonS3"}, {"name": "x-amz-server-side-encryption", "value": "AES256"}, {"name": "x-amz-version-id", "value": "vt4P4DYWNxZqverVIQ0pJYYdV1wY52nl"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:14 GMT"}, {"name": "cache-control", "value": "public, max-age=120"}, {"name": "etag", "value": "W/\"5ebcf240045a85a772860792f175e9bc\""}, {"name": "vary", "value": "accept-encoding"}, {"name": "x-cache", "value": "Hit from cloudfront"}, {"name": "via", "value": "1.1 be5e873041a47635c5cc4c628d7093a8.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-pop", "value": "HKG54-P2"}, {"name": "x-amz-cf-id", "value": "n0Ea7XefyujUBK1Xj9wXXIvsS3cI-OkXe_GxLITYkCfi4kk3pC9Igw=="}, {"name": "age", "value": "44"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 2917, "text": "{\"_lastModified\":\"2025-05-30T12:15:36.497Z\",\"integrations\":{\"LinkedIn Conversions API\":{\"versionSettings\":{\"componentTypes\":[]}},\"Google Enhanced Conversions\":{\"versionSettings\":{\"componentTypes\":[]}},\"Actions Customerio\":{\"versionSettings\":{\"componentTypes\":[]}},\"PostHog\":{\"versionSettings\":{\"componentTypes\":[]}},\"Reddit Conversions Api\":{\"versionSettings\":{\"componentTypes\":[]}},\"Koala (Cloud)\":{\"versionSettings\":{\"componentTypes\":[]}},\"Google Analytics 4 Web\":{\"adPersonalizationConsentState\":\"\",\"adUserDataConsentState\":\"\",\"allowAdPersonalizationSignals\":false,\"allowGoogleSignals\":false,\"cookieDomain\":\"auto\",\"cookieExpirationInSeconds\":63072000,\"cookieFlags\":\"\",\"cookiePath\":\"/\",\"cookiePrefix\":\"\",\"cookieUpdate\":true,\"defaultAdsStorageConsentState\":\"granted\",\"defaultAnalyticsStorageConsentState\":\"granted\",\"enableConsentMode\":false,\"measurementID\":\"G-F6GPDJDCJY\",\"pageView\":true,\"waitTimeToUpdateConsentStage\":0,\"versionSettings\":{\"componentTypes\":[]}},\"Segment.io\":{\"apiKey\":\"ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg\",\"unbundledIntegrations\":[],\"addBundledMetadata\":true,\"maybeBundledConfigIds\":{\"Google Analytics 4 Web\":[\"67a597571d12cd1c72eb47c6\"]},\"versionSettings\":{\"version\":\"4.4.7\",\"componentTypes\":[\"browser\"]},\"apiHost\":\"api.grdt.augmentcode.com/v1\"}},\"plan\":{\"track\":{\"__default\":{\"enabled\":true,\"integrations\":{}}},\"identify\":{\"__default\":{\"enabled\":true}},\"group\":{\"__default\":{\"enabled\":true}}},\"edgeFunction\":{},\"analyticsNextEnabled\":true,\"middlewareSettings\":{},\"enabledMiddleware\":{},\"metrics\":{\"sampleRate\":0.1,\"host\":\"api.grdt.augmentcode.com/v1\"},\"legacyVideoPluginsEnabled\":false,\"remotePlugins\":[{\"name\":\"Google Analytics 4 Web\",\"creationName\":\"Google Analytics 4 Web\",\"libraryName\":\"google-analytics-4-webDestination\",\"url\":\"https://cdn.segment.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js\",\"settings\":{\"adPersonalizationConsentState\":\"\",\"adUserDataConsentState\":\"\",\"allowAdPersonalizationSignals\":false,\"allowGoogleSignals\":false,\"cookieDomain\":\"auto\",\"cookieExpirationInSeconds\":63072000,\"cookieFlags\":\"\",\"cookiePath\":\"/\",\"cookiePrefix\":\"\",\"cookieUpdate\":true,\"defaultAdsStorageConsentState\":\"granted\",\"defaultAnalyticsStorageConsentState\":\"granted\",\"enableConsentMode\":false,\"measurementID\":\"G-F6GPDJDCJY\",\"pageView\":true,\"waitTimeToUpdateConsentStage\":0,\"versionSettings\":{\"componentTypes\":[]},\"subscriptions\":[{\"id\":\"jetDNd9ycnfUNqpHm6kGXp\",\"name\":\"Set Configuration Fields\",\"enabled\":true,\"partnerAction\":\"setConfigurationFields\",\"subscribe\":\"type = \\\"page\\\"\",\"mapping\":{\"send_page_view\":true}}]}},{\"name\":\"Reddit Conversions Api\",\"creationName\":\"Reddit Conversions Api\",\"libraryName\":\"reddit-pluginsDestination\",\"url\":\"https://cdn.segment.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js\",\"settings\":{\"versionSettings\":{\"componentTypes\":[]},\"subscriptions\":[]}}],\"autoInstrumentationSettings\":{\"disableTraffic\":false,\"sampleRate\":0}}"}, "redirectURL": "", "headersSize": 674, "bodySize": 1612}, "cache": {"afterRequest": null}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 45, "receive": 0}, "time": 46, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:00.199+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/api/user", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/account/subscription"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Priority", "value": "u=4"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2306}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "set-cookie", "value": "_session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; Path=/; HttpOnly; Secure; SameSite=Lax"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:57 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [{"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}], "content": {"mimeType": "application/json; charset=utf-8", "size": 254, "text": "{\"email\":\"<EMAIL>\",\"isAdmin\":true,\"isSelfServeTeamMember\":false,\"plan\":{\"name\":\"community\",\"billingMethod\":2,\"tenantTier\":\"community\"},\"tenantTier\":\"community\",\"isSubscriptionPending\":false,\"suspensions\":[],\"showTeamManagementLink\":false}"}, "redirectURL": "", "headersSize": 916, "bodySize": 1103}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 275, "receive": 0}, "time": 275, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:00.200+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/api/team/plan-change-pending", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/account/subscription"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Priority", "value": "u=4"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2326}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:57 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 19, "text": "{\"isPending\":false}"}, "redirectURL": "", "headersSize": 265, "bodySize": 304}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 244, "receive": 0}, "time": 244, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:00.200+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/api/subscription", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/account/subscription"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Priority", "value": "u=4"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2314}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:58 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 656, "text": "{\"portalUrl\":\"https://portal.withorb.com/view?token=Im16TFUzQzc1V3ZYR0ZEQXEi.kkgGMZSA8qJp8OpfnVE1Ke1sUe0\",\"planId\":\"orb_community_plan\",\"augmentPlanType\":\"community\",\"planName\":\"Community Plan\",\"billingPeriodEnd\":\"2025-07-17T00:00:00Z\",\"trialPeriodEnd\":null,\"creditsRenewingEachBillingCycle\":50,\"creditsIncludedThisBillingCycle\":50,\"billingCycleBillingAmount\":\"0.00\",\"monthlyTotalCost\":\"0.00\",\"pricePerSeat\":\"0.00\",\"maxNumSeats\":1,\"numberOfSeatsThisBillingCycle\":1,\"numberOfSeatsNextBillingCycle\":1,\"subscriptionEndDate\":null,\"planIsExpired\":false,\"addUsageAvailable\":true,\"teamsAllowed\":false,\"additionalUsageUnitCost\":\"0.10\",\"scheduledTargetPlanId\":null}"}, "redirectURL": "", "headersSize": 265, "bodySize": 670}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 1416, "receive": 0}, "time": 1416, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:00.202+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/api/plans", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/account/subscription"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Priority", "value": "u=4"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2307}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:57 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 2202, "text": "[{\"id\":\"orb_community_plan\",\"augmentPlanType\":\"community\",\"name\":\"Community Plan\",\"description\":\"Community Plan with 50 user messages per month.\",\"agentRequests\":50,\"hasTraining\":true,\"hasTeams\":false,\"price\":\"0.00\",\"priceLabel\":\"Free\",\"color\":\"var(--sky-8)\",\"colorScheme\":{\"radixColor\":\"sky\",\"gradientStart\":\"#0ea5e9\",\"gradientEnd\":\"#0284c7\"},\"icon\":\"person\"},{\"id\":\"orb_trial_plan\",\"augmentPlanType\":\"trial\",\"name\":\"Trial Plan\",\"description\":\"Trial Plan with 300 user messages per month.\",\"agentRequests\":300,\"hasTraining\":false,\"hasTeams\":true,\"price\":\"0.00\",\"priceLabel\":\"Free\",\"color\":\"var(--gray-9)\",\"colorScheme\":{\"radixColor\":\"gray\",\"gradientStart\":\"#6b7280\",\"gradientEnd\":\"#4b5563\"},\"icon\":\"clock\"},{\"id\":\"orb_developer_plan\",\"augmentPlanType\":\"paid\",\"name\":\"Developer Plan\",\"description\":\"Developer Plan with 600 user messages per month.\",\"agentRequests\":600,\"hasTraining\":false,\"hasTeams\":true,\"price\":\"50.00\",\"priceLabel\":\"$50.00/mo\",\"color\":\"var(--indigo-9)\",\"colorScheme\":{\"radixColor\":\"indigo\",\"gradientStart\":\"#4f46e5\",\"gradientEnd\":\"#3730a3\"},\"icon\":\"rocket\"},{\"id\":\"orb_pro_plan\",\"augmentPlanType\":\"paid\",\"name\":\"Pro Plan\",\"description\":\"Pro Plan with 1500 user messages per month.\",\"agentRequests\":1500,\"hasTraining\":false,\"hasTeams\":true,\"price\":\"100.00\",\"priceLabel\":\"$100.00/mo\",\"color\":\"var(--purple-9)\",\"colorScheme\":{\"radixColor\":\"purple\",\"gradientStart\":\"#8b5cf6\",\"gradientEnd\":\"#6d28d9\"},\"icon\":\"rocket\"},{\"id\":\"orb_max_plan\",\"augmentPlanType\":\"paid\",\"name\":\"Max Plan\",\"description\":\"Max Plan with 4500 user messages per month.\",\"agentRequests\":4500,\"hasTraining\":false,\"hasTeams\":true,\"price\":\"250.00\",\"priceLabel\":\"$250.00/mo\",\"color\":\"var(--amber-10)\",\"colorScheme\":{\"radixColor\":\"amber\",\"gradientStart\":\"#f59e0b\",\"gradientEnd\":\"#d97706\"},\"icon\":\"lightning\"},{\"id\":\"mock_enterprise_plan\",\"augmentPlanType\":\"enterprise\",\"name\":\"Enterprise Plan\",\"description\":\"Enterprise plans including SSO, OIDC, SCIM, Slack integration, dedicated support, and volume discounts.\",\"agentRequests\":0,\"hasTraining\":false,\"hasTeams\":true,\"price\":\"0\",\"priceLabel\":\"\",\"color\":\"var(--gray-12)\",\"colorScheme\":{\"radixColor\":\"gray\",\"gradientStart\":\"#18181b\",\"gradientEnd\":\"#09090b\"},\"icon\":\"lock\"}]"}, "redirectURL": "", "headersSize": 265, "bodySize": 892}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 804, "receive": 0}, "time": 804, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:01.331+08:00", "request": {"bodySize": 914, "method": "POST", "url": "https://api.grdt.augmentcode.com/v1/i", "httpVersion": "HTTP/1.1", "headers": [{"name": "Host", "value": "api.grdt.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "914"}, {"name": "Origin", "value": "https://app.augmentcode.com"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-site"}, {"name": "Priority", "value": "u=4"}], "cookies": [], "queryString": [], "headersSize": 512, "postData": {"mimeType": "text/plain", "params": [], "text": "{\"timestamp\":\"2025-06-18T01:44:01.310Z\",\"integrations\":{},\"type\":\"identify\",\"userId\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"traits\":{\"id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"email\":\"<EMAIL>\"},\"context\":{\"page\":{\"path\":\"/account/subscription\",\"referrer\":\"https://www.augmentcode.com/\",\"search\":\"\",\"title\":\"\",\"url\":\"https://app.augmentcode.com/account/subscription\"},\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0\",\"locale\":\"zh-CN\",\"library\":{\"name\":\"analytics.js\",\"version\":\"next-1.81.0\"},\"timezone\":\"Asia/Shanghai\"},\"messageId\":\"ajs-next-*************-e88c67db-a590-4c7c-a99f-bce10b4c7a5e\",\"anonymousId\":\"9188e13d-2902-4c1a-8a2c-09c43ba14847\",\"writeKey\":\"ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg\",\"sentAt\":\"2025-06-18T01:44:01.314Z\",\"_metadata\":{\"failedInitializations\":[\"Google Analytics 4 Web\"],\"bundled\":[\"Segment.io\"],\"unbundled\":[],\"bundledIds\":[]}}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Date", "value": "Wed, 18 Jun 2025 01:43:58 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Content-Length", "value": "21"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Access-Control-Allow-Origin", "value": "https://app.augmentcode.com"}, {"name": "Strict-Transport-Security", "value": "max-age=31536000"}, {"name": "Vary", "value": "Origin"}], "cookies": [], "content": {"mimeType": "application/json", "size": 21, "text": "{\n  \"success\": true\n}"}, "redirectURL": "", "headersSize": 249, "bodySize": 270}, "cache": {}, "timings": {"blocked": 0, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 215, "receive": 0}, "time": 215, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:01.332+08:00", "request": {"bodySize": 989, "method": "POST", "url": "https://api.grdt.augmentcode.com/v1/p", "httpVersion": "HTTP/1.1", "headers": [{"name": "Host", "value": "api.grdt.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Content-Length", "value": "989"}, {"name": "Origin", "value": "https://app.augmentcode.com"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-site"}, {"name": "Priority", "value": "u=4"}], "cookies": [], "queryString": [], "headersSize": 512, "postData": {"mimeType": "text/plain", "params": [], "text": "{\"timestamp\":\"2025-06-18T01:44:01.311Z\",\"integrations\":{},\"type\":\"page\",\"properties\":{\"path\":\"/account/subscription\",\"referrer\":\"https://www.augmentcode.com/\",\"search\":\"\",\"title\":\"\",\"url\":\"https://app.augmentcode.com/account/subscription\"},\"context\":{\"page\":{\"path\":\"/account/subscription\",\"referrer\":\"https://www.augmentcode.com/\",\"search\":\"\",\"title\":\"\",\"url\":\"https://app.augmentcode.com/account/subscription\"},\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0\",\"locale\":\"zh-CN\",\"library\":{\"name\":\"analytics.js\",\"version\":\"next-1.81.0\"},\"timezone\":\"Asia/Shanghai\"},\"messageId\":\"ajs-next-*************-67dba590-cc7c-499f-bce1-0b4c7a5e2d0d\",\"userId\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"anonymousId\":\"9188e13d-2902-4c1a-8a2c-09c43ba14847\",\"writeKey\":\"ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg\",\"sentAt\":\"2025-06-18T01:44:01.316Z\",\"_metadata\":{\"failedInitializations\":[\"Google Analytics 4 Web\"],\"bundled\":[\"Segment.io\"],\"unbundled\":[],\"bundledIds\":[]}}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Date", "value": "Wed, 18 Jun 2025 01:43:58 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Content-Length", "value": "21"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Access-Control-Allow-Origin", "value": "https://app.augmentcode.com"}, {"name": "Strict-Transport-Security", "value": "max-age=31536000"}, {"name": "Vary", "value": "Origin"}], "cookies": [], "content": {"mimeType": "application/json", "size": 21, "text": "{\n  \"success\": true\n}"}, "redirectURL": "", "headersSize": 249, "bodySize": 270}, "cache": {}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 213, "receive": 0}, "time": 214, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:01.803+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/api/credits", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/account/subscription"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Priority", "value": "u=4"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2309}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:44:00 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 83, "text": "{\"usageUnitsAvailable\":49,\"usageUnitsUsedThisBillingCycle\":7,\"usageUnitsPending\":0}"}, "redirectURL": "", "headersSize": 265, "bodySize": 347}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 2103, "receive": 0}, "time": 2103, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}, {"startedDateTime": "2025-06-18T09:44:01.805+08:00", "request": {"bodySize": 0, "method": "GET", "url": "https://app.augmentcode.com/api/payment", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "app.augmentcode.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/******** Firefox/139.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4,de-DE;q=0.2"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://app.augmentcode.com/account/subscription"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx%2F5joO7%2BEjm8KRtY; _ga_F6GPDJDCJY=GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0; _ga=GA1.1.188197348.1750129200; _gcl_au=1.1.618244994.1750129200; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%22f9e3adfd-26c0-4118-8a9b-843c89035c37%22%2C%22%24sesid%22%3A%5B1750129246131%2C%2201977bd4-57ba-7334-b920-7a02f608e5b7%22%2C1750129203130%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fauth.augmentcode.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g%22%7D%7D; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Priority", "value": "u=4"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "intercom-id-oiuh4kg0", "value": "06e1909f-8e2b-41c4-af99-bb50fe35f21d"}, {"name": "intercom-device-id-oiuh4kg0", "value": "ca573e63-3900-49a8-84cd-8b4a2debb741"}, {"name": "ajs_anonymous_id", "value": "9188e13d-2902-4c1a-8a2c-09c43ba14847"}, {"name": "ajs_user_id", "value": "f9e3adfd-26c0-4118-8a9b-843c89035c37"}, {"name": "vector_visitor_id", "value": "01444a9e-513a-4ca8-a580-326ca68389b4"}, {"name": "_session", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3fPlteuSHedeILjrmewcPbecIKx/5joO7+Ejm8KRtY"}, {"name": "_ga_F6GPDJDCJY", "value": "GS2.1.s1750157933$o2$g0$t1750157989$j4$l0$h0"}, {"name": "_ga", "value": "GA1.1.188197348.1750129200"}, {"name": "_gcl_au", "value": "1.1.618244994.1750129200"}, {"name": "ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog", "value": "{\"distinct_id\":\"f9e3adfd-26c0-4118-8a9b-843c89035c37\",\"$sesid\":[1750129246131,\"01977bd4-57ba-7334-b920-7a02f608e5b7\",1750129203130],\"$epp\":true,\"$initial_person_info\":{\"r\":\"https://auth.augmentcode.com/\",\"u\":\"https://login.augmentcode.com/u/login/identifier?state=hKFo2SBCQXlmNDVvc09rMlFScS1CSldDVml6R2VqYzRkQmEyeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGtmQ0FUNFZWandCZnpPNXN6TUZFeFJ4WlZ2Q3o0ZG00o2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g\"}}"}, {"name": "ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog", "value": "{\"distinct_id\":\"01977d84-062f-71c8-90e2-b37fa648ffbd\",\"$sesid\":[1750157539277,\"01977d84-062e-73ff-99aa-6fc17807dc62\",1750157493806]}"}], "queryString": [], "headersSize": 2309}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/2", "headers": [{"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "cache-control", "value": "no-store"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "content-encoding", "value": "gzip"}, {"name": "date", "value": "Wed, 18 Jun 2025 01:43:59 GMT"}, {"name": "via", "value": "1.1 google"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 26, "text": "{\"hasPaymentMethod\":false}"}, "redirectURL": "", "headersSize": 265, "bodySize": 311}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 376, "receive": 0}, "time": 376, "_securityState": "secure", "serverIPAddress": "127.0.0.1", "connection": "10809", "pageref": "page_1"}]}}
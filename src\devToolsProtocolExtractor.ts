import * as vscode from 'vscode';
import * as http from 'http';

export interface DevToolsResult {
    success: boolean;
    cookies?: string;
    error?: string;
}

export class DevToolsProtocolExtractor {
    
    /**
     * 使用Chrome DevTools Protocol直接获取cookies
     * 这是最先进的方法，可以绕过所有JavaScript限制
     */
    async extractCookiesViaDevTools(): Promise<DevToolsResult> {
        try {
            const guide = `
🔧 Chrome DevTools Protocol Cookie提取

🎯 这是最先进的方法：
• 直接与Chrome浏览器通信
• 可以获取所有类型的cookies（包括HttpOnly）
• 绕过JavaScript限制
• 自动化程度最高

📋 使用步骤：

1️⃣ 启动Chrome浏览器（调试模式）
2️⃣ 访问 app.augmentcode.com 并登录
3️⃣ 点击下面的"连接Chrome"按钮
4️⃣ 自动提取所有cookies

⚠️ 前置条件：
• 需要Chrome浏览器
• 需要以调试模式启动Chrome
• 命令：chrome --remote-debugging-port=9222

💡 如果Chrome已经运行，请先关闭所有Chrome窗口，
然后用上述命令重新启动。
            `;

            const doc = await vscode.workspace.openTextDocument({
                content: guide,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);

            const action = await vscode.window.showInformationMessage(
                '🔧 请先以调试模式启动Chrome，然后连接',
                '🔗 连接Chrome',
                '📋 显示启动命令',
                '❌ 取消'
            );

            if (action === '🔗 连接Chrome') {
                return await this.connectToChromeAndExtractCookies();
            } else if (action === '📋 显示启动命令') {
                return await this.showChromeStartupCommands();
            }

            return { success: false, error: 'User cancelled DevTools extraction' };

        } catch (error) {
            return { success: false, error: `DevTools extraction error: ${error}` };
        }
    }

    private async showChromeStartupCommands(): Promise<DevToolsResult> {
        const commands = `
🚀 Chrome调试模式启动命令

Windows:
chrome.exe --remote-debugging-port=9222 --user-data-dir=temp

macOS:
/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222 --user-data-dir=temp

Linux:
google-chrome --remote-debugging-port=9222 --user-data-dir=temp

📋 步骤：
1. 关闭所有Chrome窗口
2. 在命令行运行上述命令
3. Chrome会以调试模式启动
4. 访问 app.augmentcode.com 并登录
5. 返回VSCode点击"连接Chrome"

💡 提示：
• --user-data-dir=temp 使用临时配置文件
• --remote-debugging-port=9222 启用调试端口
• 启动后Chrome标题栏会显示"正在调试"
        `;

        const doc = await vscode.workspace.openTextDocument({
            content: commands,
            language: 'bash'
        });
        await vscode.window.showTextDocument(doc);

        const action = await vscode.window.showInformationMessage(
            '📋 请按照命令启动Chrome，然后连接',
            '🔗 连接Chrome',
            '❌ 取消'
        );

        if (action === '🔗 连接Chrome') {
            return await this.connectToChromeAndExtractCookies();
        }

        return { success: false, error: 'User cancelled after showing commands' };
    }

    private async connectToChromeAndExtractCookies(): Promise<DevToolsResult> {
        try {
            console.log('🔧 [DevTools] 尝试连接Chrome调试端口...');

            // 检查Chrome调试端口是否可用
            const isAvailable = await this.checkChromeDebugPort();
            if (!isAvailable) {
                return {
                    success: false,
                    error: 'Chrome debug port not available. Please start Chrome with --remote-debugging-port=9222'
                };
            }

            // 获取Chrome标签页列表
            const tabs = await this.getChromeTabsList();
            console.log('📊 [DevTools] 找到', tabs.length, '个标签页');

            // 查找Augment标签页
            const augmentTab = tabs.find((tab: any) => 
                tab.url && tab.url.includes('augmentcode.com')
            );

            if (!augmentTab) {
                return {
                    success: false,
                    error: 'No Augment tab found. Please open app.augmentcode.com in Chrome'
                };
            }

            console.log('🔍 [DevTools] 找到Augment标签页:', augmentTab.title);

            // 连接到标签页并获取cookies
            const cookies = await this.extractCookiesFromTab(augmentTab.webSocketDebuggerUrl);

            if (!cookies) {
                return {
                    success: false,
                    error: 'Failed to extract cookies from Chrome tab'
                };
            }

            console.log('✅ [DevTools] 成功提取cookies');

            return {
                success: true,
                cookies: cookies
            };

        } catch (error) {
            console.error('❌ [DevTools] 连接错误:', error);
            return { success: false, error: `Chrome connection error: ${error}` };
        }
    }

    private async checkChromeDebugPort(): Promise<boolean> {
        return new Promise((resolve) => {
            const req = http.get('http://localhost:9222/json', (res) => {
                resolve(res.statusCode === 200);
            });

            req.on('error', () => {
                resolve(false);
            });

            req.setTimeout(3000, () => {
                req.destroy();
                resolve(false);
            });
        });
    }

    private async getChromeTabsList(): Promise<any[]> {
        return new Promise((resolve, reject) => {
            const req = http.get('http://localhost:9222/json', (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        const tabs = JSON.parse(data);
                        resolve(tabs);
                    } catch (error) {
                        reject(error);
                    }
                });
            });

            req.on('error', reject);
            req.setTimeout(5000, () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });
        });
    }

    private async extractCookiesFromTab(webSocketUrl: string): Promise<string | null> {
        return new Promise((resolve, reject) => {
            try {
                // 使用Node.js内置的WebSocket实现
                const WebSocket = require('ws');
                const ws = new WebSocket(webSocketUrl);
                let cookies: string | null = null;

                ws.on('open', () => {
                    console.log('🔗 [DevTools] WebSocket连接已建立');

                    // 启用Network域
                    ws.send(JSON.stringify({
                        id: 1,
                        method: 'Network.enable'
                    }));

                    // 获取cookies
                    ws.send(JSON.stringify({
                        id: 2,
                        method: 'Network.getCookies',
                        params: {
                            urls: ['https://app.augmentcode.com']
                        }
                    }));
                });

                ws.on('message', (data: any) => {
                    try {
                        const message = JSON.parse(data.toString());

                        if (message.id === 2 && message.result) {
                            // 处理cookies响应
                            const cookieList = message.result.cookies || [];
                            const sessionCookie = cookieList.find((cookie: any) => cookie.name === '_session');

                            if (sessionCookie) {
                                cookies = `_session=${sessionCookie.value}`;
                                console.log('✅ [DevTools] 找到_session cookie');
                            }

                            ws.close();
                        }
                    } catch (error) {
                        console.error('❌ [DevTools] 消息解析错误:', error);
                    }
                });

                ws.on('close', () => {
                    console.log('🔗 [DevTools] WebSocket连接已关闭');
                    resolve(cookies);
                });

                ws.on('error', (error: any) => {
                    console.error('❌ [DevTools] WebSocket错误:', error);
                    reject(error);
                });

                // 超时处理
                setTimeout(() => {
                    try {
                        if (ws.readyState === 1) { // WebSocket.OPEN
                            ws.close();
                        }
                        if (!cookies) {
                            reject(new Error('Cookie extraction timeout'));
                        }
                    } catch (timeoutError) {
                        reject(timeoutError);
                    }
                }, 10000);

            } catch (initError) {
                console.error('❌ [DevTools] WebSocket初始化错误:', initError);
                reject(new Error('WebSocket not available. Please install ws package: npm install ws'));
            }
        });
    }

    /**
     * 检查Chrome是否以调试模式运行
     */
    async isChromeDebugModeRunning(): Promise<boolean> {
        try {
            return await this.checkChromeDebugPort();
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取Chrome调试信息
     */
    async getChromeDebugInfo(): Promise<any> {
        try {
            const tabs = await this.getChromeTabsList();
            return {
                available: true,
                tabCount: tabs.length,
                augmentTabs: tabs.filter((tab: any) => 
                    tab.url && tab.url.includes('augmentcode.com')
                ).length
            };
        } catch (error: any) {
            return {
                available: false,
                error: error?.toString() || 'Unknown error'
            };
        }
    }
}

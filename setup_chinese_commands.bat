@echo off
chcp 65001 > nul
echo ========================================
echo   设置VSCode命令面板中文显示
echo ========================================
echo.

echo 🔍 问题说明:
echo VSCode插件的命令面板语言由VSCode本身的界面语言决定，
echo 而不是插件的语言设置。
echo.

echo 📋 解决步骤:
echo.
echo 步骤1: 设置VSCode界面语言为中文
echo   1. 在VSCode中按 Ctrl+Shift+P
echo   2. 输入 "Configure Display Language"
echo   3. 选择 "中文(简体)" 或 "zh-cn"
echo   4. 重启VSCode
echo.

echo 步骤2: 验证设置
echo   1. 重启VSCode后，按 Ctrl+Shift+P
echo   2. 输入 "augment" 查看命令
echo   3. 应该显示中文命令标题
echo.

echo 步骤3: 如果仍显示英文
echo   1. 检查系统语言设置
echo   2. 确保VSCode完全重启
echo   3. 重新安装插件
echo.

echo ⚠️  重要提示:
echo - 插件的"设置语言"功能主要影响状态栏和消息
echo - 命令面板的语言由VSCode界面语言控制
echo - 两者是独立的设置系统
echo.

echo 🔧 替代方案:
echo 如果不想改变VSCode界面语言，可以：
echo 1. 保持VSCode为英文界面
echo 2. 使用插件的"设置语言"功能设置状态栏为中文
echo 3. 命令面板保持英文（这是正常的）
echo.

echo ========================================
echo   常见问题解答
echo ========================================
echo.
echo Q: 为什么设置插件语言后命令面板还是英文？
echo A: 因为命令面板语言由VSCode界面语言决定，不是插件设置。
echo.
echo Q: 如何同时拥有中文命令面板和英文VSCode界面？
echo A: 这在技术上不可行，VSCode的设计就是这样的。
echo.
echo Q: 其他插件的命令也会变成中文吗？
echo A: 是的，所有支持中文的插件命令都会变成中文。
echo.
echo Q: 这样设置会影响VSCode的其他功能吗？
echo A: 只会改变界面语言，功能完全相同。
echo.

pause

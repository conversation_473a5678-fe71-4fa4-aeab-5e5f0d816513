// 使用真实Cookie测试Augment API
const https = require('https');

// 您提供的cookie信息
const COOKIES = 'intercom-id-oiuh4kg0=06e1909f-8e2b-41c4-af99-bb50fe35f21d; intercom-device-id-oiuh4kg0=ca573e63-3900-49a8-84cd-8b4a2debb741; ajs_anonymous_id=9188e13d-2902-4c1a-8a2c-09c43ba14847; ajs_user_id=f9e3adfd-26c0-4118-8a9b-843c89035c37; vector_visitor_id=01444a9e-513a-4ca8-a580-326ca68389b4; _session=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D%3D--b8e9c8e9c8e9c8e9c8e9c8e9c8e9c8e9c8e9c8e9; ph_phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW_posthog=%7B%22distinct_id%22%3A%2201977d84-062f-71c8-90e2-b37fa648ffbd%22%2C%22%24sesid%22%3A%5B1750157539277%2C%2201977d84-062e-73ff-99aa-6fc17807dc62%22%2C1750157493806%5D%7D';

console.log('🍪 使用Cookie测试Augment API');
console.log('='.repeat(60));

// 测试应用仪表板页面
async function testDashboard() {
    console.log('\n📊 测试仪表板页面...');
    
    return new Promise((resolve) => {
        const options = {
            hostname: 'app.augmentcode.com',
            path: '/dashboard',
            method: 'GET',
            headers: {
                'Cookie': COOKIES,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应长度: ${data.length} 字符`);
                
                if (res.statusCode === 200) {
                    console.log(`   ✅ 仪表板访问成功`);
                    // 查找可能的API端点或数据
                    const apiMatches = data.match(/api\.[^"'\s]+/g) || [];
                    const uniqueApis = [...new Set(apiMatches)];
                    if (uniqueApis.length > 0) {
                        console.log(`   🔍 发现API端点: ${uniqueApis.join(', ')}`);
                    }
                    resolve(true);
                } else {
                    console.log(`   ❌ 访问失败`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ 错误: ${err.message}`);
            resolve(false);
        });
        
        req.end();
    });
}

// 测试订阅页面
async function testSubscription() {
    console.log('\n💳 测试订阅页面...');
    
    return new Promise((resolve) => {
        const options = {
            hostname: 'app.augmentcode.com',
            path: '/account/subscription',
            method: 'GET',
            headers: {
                'Cookie': COOKIES,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应长度: ${data.length} 字符`);
                
                if (res.statusCode === 200) {
                    console.log(`   ✅ 订阅页面访问成功`);
                    
                    // 查找使用统计相关信息
                    const usageMatches = data.match(/usage|limit|quota|requests|tokens/gi) || [];
                    if (usageMatches.length > 0) {
                        console.log(`   📊 发现使用相关关键词: ${usageMatches.slice(0, 5).join(', ')}...`);
                    }
                    
                    // 查找数字（可能是使用量）
                    const numberMatches = data.match(/\b\d{1,6}\b/g) || [];
                    if (numberMatches.length > 0) {
                        console.log(`   🔢 发现数字: ${numberMatches.slice(0, 10).join(', ')}...`);
                    }
                    
                    resolve(true);
                } else {
                    console.log(`   ❌ 访问失败`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ 错误: ${err.message}`);
            resolve(false);
        });
        
        req.end();
    });
}

// 测试API端点（使用cookie作为认证）
async function testApiWithCookies(endpoint, name) {
    console.log(`\n🔌 ${name}...`);
    
    return new Promise((resolve) => {
        const options = {
            hostname: 'i1.api.augmentcode.com',
            path: endpoint,
            method: 'GET',
            headers: {
                'Cookie': COOKIES,
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://app.augmentcode.com/',
                'Origin': 'https://app.augmentcode.com'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`   状态码: ${res.statusCode}`);
                console.log(`   响应长度: ${data.length} 字符`);
                
                if (res.statusCode === 200) {
                    try {
                        const json = JSON.parse(data);
                        console.log(`   数据键: [${Object.keys(json).join(', ')}]`);
                        console.log(`   ✅ ${name}成功`);
                        
                        // 如果是使用数据，显示详细信息
                        if (endpoint === '/usage' && json) {
                            console.log(`   📊 使用数据详情:`);
                            Object.entries(json).forEach(([key, value]) => {
                                console.log(`      ${key}: ${value}`);
                            });
                        }
                        
                        resolve(true);
                    } catch (e) {
                        console.log(`   响应: ${data.substring(0, 200)}...`);
                        console.log(`   ✅ ${name}成功 (非JSON响应)`);
                        resolve(true);
                    }
                } else {
                    console.log(`   ❌ ${name}失败`);
                    console.log(`   响应: ${data.substring(0, 200)}...`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (err) => {
            console.log(`   ❌ 网络错误: ${err.message}`);
            resolve(false);
        });
        
        req.end();
    });
}

// 主测试函数
async function runTests() {
    console.log(`开始时间: ${new Date().toISOString()}`);
    
    const results = {
        dashboard: await testDashboard(),
        subscription: await testSubscription(),
        apiHealth: await testApiWithCookies('/health', '🏥 API健康检查'),
        apiUser: await testApiWithCookies('/user', '👤 API用户信息'),
        apiUsage: await testApiWithCookies('/usage', '📊 API使用数据'),
        apiSubscription: await testApiWithCookies('/subscription', '💳 API订阅信息')
    };
    
    console.log('\n' + '='.repeat(60));
    console.log('📋 测试结果汇总:');
    Object.entries(results).forEach(([test, success]) => {
        console.log(`   ${test}: ${success ? '✅ 通过' : '❌ 失败'}`);
    });
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 测试通过`);
    console.log(`完成时间: ${new Date().toISOString()}`);
}

// 运行测试
runTests().catch(console.error);

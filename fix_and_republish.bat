@echo off
echo ========================================
echo   Augment Usage Tracker - 修复并重新发布
echo ========================================
echo.

echo [1/4] 修复README.md文件...
if exist README.md (
    echo 删除损坏的README.md...
    del /f /q README.md
    if errorlevel 1 (
        echo 警告: 无法删除README.md，可能被占用
        echo 请手动删除README.md文件后重新运行此脚本
        pause
        exit /b 1
    )
)

if exist README_NEW.md (
    echo 使用正确的README内容...
    copy README_NEW.md README.md > nul
    if errorlevel 1 (
        echo 错误: 无法复制README_NEW.md
        pause
        exit /b 1
    )
    echo ✅ README.md已修复
) else (
    echo 错误: 找不到README_NEW.md文件
    pause
    exit /b 1
)

echo.
echo [2/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo 错误: TypeScript编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [3/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo 错误: 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [4/4] 重新发布到商店...
echo 注意: 这将覆盖当前版本1.0.0
set /p confirm="确定要重新发布吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo 错误: 发布失败
        echo 请检查是否已登录: vsce login augment-usage-tracker
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo ========================================
echo   修复完成！
echo ========================================
echo.
echo 修复内容:
echo - ✅ README.md编码问题已修复
echo - ✅ 插件已重新打包
echo - ✅ 版本1.0.0已更新（如果选择发布）
echo.
echo 请等待5-10分钟后检查插件商店页面:
echo https://marketplace.visualstudio.com/items?itemName=augment-usage-tracker.augment-usage-tracker
echo.
pause

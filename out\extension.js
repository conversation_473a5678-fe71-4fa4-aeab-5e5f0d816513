"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const statusBar_1 = require("./statusBar");
const usageTracker_1 = require("./usageTracker");
const storage_1 = require("./storage");
const augmentDetector_1 = require("./augmentDetector");
const config_1 = require("./config");
let statusBarManager;
let usageTracker;
let storageManager;
let augmentDetector;
let configManager;
function activate(context) {
    console.log('Augment Usage Tracker is now active!');
    // Initialize managers
    storageManager = new storage_1.StorageManager(context);
    configManager = new config_1.ConfigManager();
    augmentDetector = new augmentDetector_1.AugmentDetector();
    usageTracker = new usageTracker_1.UsageTracker(storageManager, configManager);
    statusBarManager = new statusBar_1.StatusBarManager(usageTracker, configManager);
    // Register commands
    const resetUsageCommand = vscode.commands.registerCommand('augmentTracker.resetUsage', () => {
        usageTracker.resetUsage();
        vscode.window.showInformationMessage('Augment usage statistics have been reset.');
    });
    const openSettingsCommand = vscode.commands.registerCommand('augmentTracker.openSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'augmentTracker');
    });
    const showDetailsCommand = vscode.commands.registerCommand('augmentTracker.showDetails', () => {
        showUsageDetails();
    });
    // Start tracking
    usageTracker.startTracking();
    statusBarManager.show();
    // Check for Augment plugin
    augmentDetector.detectAugmentPlugin().then(isInstalled => {
        if (isInstalled) {
            console.log('Augment plugin detected');
            // TODO: Try to integrate with real Augment data
        }
        else {
            console.log('Augment plugin not detected, using simulation mode');
        }
    });
    // Add to subscriptions
    context.subscriptions.push(resetUsageCommand, openSettingsCommand, showDetailsCommand, statusBarManager, usageTracker);
    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('augmentTracker')) {
            configManager.reloadConfig();
            statusBarManager.updateDisplay();
        }
    });
}
exports.activate = activate;
function showUsageDetails() {
    const usage = usageTracker.getCurrentUsage();
    const limit = configManager.getUsageLimit();
    const percentage = Math.round((usage / limit) * 100);
    const message = `
Augment Usage Statistics:
• Current Usage: ${usage}
• Monthly Limit: ${limit}
• Usage Percentage: ${percentage}%
• Remaining: ${limit - usage}

Last Reset: ${usageTracker.getLastResetDate()}
    `.trim();
    vscode.window.showInformationMessage(message, 'Reset Usage', 'Open Settings').then(selection => {
        if (selection === 'Reset Usage') {
            vscode.commands.executeCommand('augmentTracker.resetUsage');
        }
        else if (selection === 'Open Settings') {
            vscode.commands.executeCommand('augmentTracker.openSettings');
        }
    });
}
function deactivate() {
    if (statusBarManager) {
        statusBarManager.dispose();
    }
    if (usageTracker) {
        usageTracker.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map
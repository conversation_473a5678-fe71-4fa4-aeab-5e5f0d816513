"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const statusBar_1 = require("./statusBar");
const usageTracker_1 = require("./usageTracker");
const storage_1 = require("./storage");
const augmentDetector_1 = require("./augmentDetector");
const config_1 = require("./config");
let statusBarManager;
let usageTracker;
let storageManager;
let augmentDetector;
let configManager;
function activate(context) {
    console.log('Augment Usage Tracker is now active!');
    // Initialize managers
    storageManager = new storage_1.StorageManager(context);
    configManager = new config_1.ConfigManager();
    augmentDetector = new augmentDetector_1.AugmentDetector();
    usageTracker = new usageTracker_1.UsageTracker(storageManager, configManager);
    statusBarManager = new statusBar_1.StatusBarManager(usageTracker, configManager);
    // Register commands
    const resetUsageCommand = vscode.commands.registerCommand('augmentTracker.resetUsage', () => {
        usageTracker.resetUsage();
        vscode.window.showInformationMessage('Augment usage statistics have been reset.');
    });
    const openSettingsCommand = vscode.commands.registerCommand('augmentTracker.openSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'augmentTracker');
    });
    const showDetailsCommand = vscode.commands.registerCommand('augmentTracker.showDetails', () => {
        showUsageDetails();
    });
    const inputRealDataCommand = vscode.commands.registerCommand('augmentTracker.inputRealData', () => {
        usageTracker.promptUserForRealData();
    });
    const setupApiTokenCommand = vscode.commands.registerCommand('augmentTracker.setupApiToken', async () => {
        const success = await augmentDetector.promptForApiToken();
        if (success) {
            vscode.window.showInformationMessage('Augment API token configured successfully!');
            // Re-detect status with new token
            const status = await augmentDetector.getAugmentStatus();
            statusBarManager.updateAugmentStatus(status);
            if (status.hasRealData && status.usageData) {
                usageTracker.updateWithRealData(status.usageData);
            }
        }
    });
    const testApiConnectionCommand = vscode.commands.registerCommand('augmentTracker.testApiConnection', async () => {
        const result = await augmentDetector.testApiConnection();
        if (result.success) {
            vscode.window.showInformationMessage('✅ Augment API connection successful!');
        }
        else {
            const message = result.hasToken
                ? `❌ API connection failed: ${result.error}`
                : '❌ No API token configured. Use "Setup API Token" command first.';
            vscode.window.showErrorMessage(message);
        }
    });
    const clearApiTokenCommand = vscode.commands.registerCommand('augmentTracker.clearApiToken', () => {
        augmentDetector.clearApiToken();
        vscode.window.showInformationMessage('Augment API token cleared.');
        // Update status bar to reflect change
        statusBarManager.updateDisplay();
    });
    const debugApiCallsCommand = vscode.commands.registerCommand('augmentTracker.debugApiCalls', async () => {
        const choice = await vscode.window.showQuickPick([
            { label: '🏥 Health Check', description: 'Test basic API connectivity', value: 'health' },
            { label: '👤 User Info', description: 'Get user account information', value: 'user' },
            { label: '📊 Usage Data', description: 'Get usage statistics', value: 'usage' },
            { label: '💳 Subscription', description: 'Get subscription information', value: 'subscription' }
        ], {
            placeHolder: 'Select API endpoint to test',
            ignoreFocusOut: true
        });
        if (!choice)
            return;
        vscode.window.showInformationMessage(`🔍 Testing ${choice.label}... Check Developer Console for details.`);
        console.log(`\n🧪 === API Debug Test: ${choice.label} ===`);
        console.log(`⏰ Started at: ${new Date().toISOString()}`);
        try {
            const apiClient = augmentDetector.apiClient;
            let result;
            switch (choice.value) {
                case 'health':
                    result = await apiClient.checkHealth();
                    break;
                case 'user':
                    result = await apiClient.getUserInfo();
                    break;
                case 'usage':
                    result = await apiClient.getUsageData();
                    break;
                case 'subscription':
                    result = await apiClient.getSubscriptionInfo();
                    break;
            }
            console.log(`📋 API Response:`, result);
            if (result.success) {
                vscode.window.showInformationMessage(`✅ ${choice.label} successful! Check console for details.`);
            }
            else {
                vscode.window.showErrorMessage(`❌ ${choice.label} failed: ${result.error}`);
            }
        }
        catch (error) {
            console.error(`💥 Test failed:`, error);
            vscode.window.showErrorMessage(`💥 Test failed: ${error}`);
        }
        console.log(`⏰ Completed at: ${new Date().toISOString()}`);
        console.log(`🧪 === End API Debug Test ===\n`);
    });
    const setupCookiesCommand = vscode.commands.registerCommand('augmentTracker.setupCookies', async () => {
        const apiClient = augmentDetector.apiClient;
        const success = await apiClient.promptForCookies();
        if (success) {
            vscode.window.showInformationMessage('🍪 Augment cookies configured successfully!');
            // Re-detect status with new cookies
            const status = await augmentDetector.getAugmentStatus();
            statusBarManager.updateAugmentStatus(status);
            if (status.hasRealData && status.usageData) {
                usageTracker.updateWithRealData(status.usageData);
            }
        }
    });
    // Start tracking
    usageTracker.startTracking();
    statusBarManager.show();
    // Check for Augment plugin and get detailed status
    augmentDetector.getAugmentStatus().then(status => {
        console.log('Augment Status:', status);
        if (status.installed) {
            if (status.active) {
                if (status.hasRealData) {
                    console.log(`Augment plugin detected with real data (${status.integrationMethod})`);
                    // Update usage tracker with real data if available
                    if (status.usageData) {
                        usageTracker.updateWithRealData(status.usageData);
                    }
                }
                else {
                    console.log('Augment plugin detected but no real data available');
                }
            }
            else {
                console.log('Augment plugin installed but not active');
            }
        }
        else {
            console.log('Augment plugin not detected, using simulation mode');
        }
        // Update status bar with integration info
        statusBarManager.updateAugmentStatus(status);
    });
    // Add to subscriptions
    context.subscriptions.push(resetUsageCommand, openSettingsCommand, showDetailsCommand, inputRealDataCommand, setupApiTokenCommand, testApiConnectionCommand, clearApiTokenCommand, debugApiCallsCommand, setupCookiesCommand, statusBarManager, usageTracker);
    // Listen for configuration changes
    vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('augmentTracker')) {
            configManager.reloadConfig();
            statusBarManager.updateDisplay();
        }
        // Check if Augment configuration changed
        if (event.affectsConfiguration('augment')) {
            // Re-detect Augment status
            augmentDetector.getAugmentStatus().then(status => {
                statusBarManager.updateAugmentStatus(status);
                if (status.hasRealData && status.usageData) {
                    usageTracker.updateWithRealData(status.usageData);
                }
            });
        }
    });
    // Monitor Augment extension state changes
    const augmentStateWatcher = augmentDetector.onAugmentStateChange(status => {
        console.log('Augment state changed:', status);
        statusBarManager.updateAugmentStatus(status);
        if (status.hasRealData && status.usageData) {
            usageTracker.updateWithRealData(status.usageData);
        }
    });
    context.subscriptions.push(augmentStateWatcher);
}
exports.activate = activate;
function showUsageDetails() {
    const usage = usageTracker.getCurrentUsage();
    const limit = configManager.getUsageLimit();
    const percentage = Math.round((usage / limit) * 100);
    const message = `
Augment Usage Statistics:
• Current Usage: ${usage}
• Monthly Limit: ${limit}
• Usage Percentage: ${percentage}%
• Remaining: ${limit - usage}

Last Reset: ${usageTracker.getLastResetDate()}
    `.trim();
    vscode.window.showInformationMessage(message, 'Reset Usage', 'Open Settings').then(selection => {
        if (selection === 'Reset Usage') {
            vscode.commands.executeCommand('augmentTracker.resetUsage');
        }
        else if (selection === 'Open Settings') {
            vscode.commands.executeCommand('augmentTracker.openSettings');
        }
    });
}
function deactivate() {
    if (statusBarManager) {
        statusBarManager.dispose();
    }
    if (usageTracker) {
        usageTracker.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map
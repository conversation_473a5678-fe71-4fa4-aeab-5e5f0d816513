# 文档索引 | Documentation Index

## 📚 主要文档 | Main Documentation

### 中文文档 | Chinese Documentation

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| **[README.md](README.md)** | 主要说明文档 | 所有用户 |
| **[README.zh-cn.md](README.zh-cn.md)** | 完整中文文档 | 中文用户 |
| **[DEVELOPMENT.zh-cn.md](DEVELOPMENT.zh-cn.md)** | 中文开发指南 | 中文开发者 |

### English Documentation

| Document | Description | Target Audience |
|----------|-------------|-----------------|
| **[README.en.md](README.en.md)** | Complete English documentation | English users |
| **[DEVELOPMENT.md](DEVELOPMENT.md)** | English development guide | English developers |

## 🔧 专业文档 | Specialized Documentation

### 功能指南 | Feature Guides

| 文档 | 描述 | 语言 |
|------|------|------|
| **[AUTO_COOKIE_EXTRACTION_GUIDE.md](AUTO_COOKIE_EXTRACTION_GUIDE.md)** | 自动Cookie提取功能指南 | 中文 |
| **[API_HEADER_EXTRACTION_GUIDE.md](API_HEADER_EXTRACTION_GUIDE.md)** | API响应头提取功能指南（新增） | 中文 |
| **[COOKIE_MANAGEMENT_GUIDE.md](COOKIE_MANAGEMENT_GUIDE.md)** | 完整Cookie管理功能指南 | 中文 |
| **[LOGOUT_FEATURE_GUIDE.md](LOGOUT_FEATURE_GUIDE.md)** | 退出登录功能指南（新增） | 中文 |
| **[MANUAL_FEATURES_GUIDE.md](MANUAL_FEATURES_GUIDE.md)** | 手动刷新和语言设置指南 | 中英文 |
| **[TESTING_GUIDE.md](TESTING_GUIDE.md)** | 功能测试指南 | 中文 |
| **[I18N_GUIDE.md](I18N_GUIDE.md)** | 国际化开发指南 | 中英文 |

### 技术文档 | Technical Documentation

| 文档 | 描述 | 语言 |
|------|------|------|
| **[LICENSE](LICENSE)** | MIT许可证 | 英文 |
| **[package.json](package.json)** | 扩展配置清单 | JSON |
| **[tsconfig.json](tsconfig.json)** | TypeScript配置 | JSON |

## 📖 文档结构 | Documentation Structure

### 用户文档层次 | User Documentation Hierarchy

```
用户文档 User Docs
├── README.md (中文主文档)
├── README.en.md (英文完整文档)
├── README.zh-cn.md (中文完整文档)
└── 功能指南 Feature Guides
    ├── AUTO_COOKIE_EXTRACTION_GUIDE.md
    ├── API_HEADER_EXTRACTION_GUIDE.md (新增)
    ├── COOKIE_MANAGEMENT_GUIDE.md
    ├── LOGOUT_FEATURE_GUIDE.md (新增)
    ├── MANUAL_FEATURES_GUIDE.md
    ├── TESTING_GUIDE.md
    └── I18N_GUIDE.md
```

### 开发者文档层次 | Developer Documentation Hierarchy

```
开发者文档 Developer Docs
├── DEVELOPMENT.md (英文开发指南)
├── DEVELOPMENT.zh-cn.md (中文开发指南)
├── 技术规范 Technical Specs
│   ├── package.json
│   ├── tsconfig.json
│   └── .vscodeignore
└── 源码文档 Source Code
    └── src/ (TypeScript源码)
```

## 🎯 文档使用指南 | Documentation Usage Guide

### 对于新用户 | For New Users

1. **开始阅读**: [README.md](README.md) (中文) 或 [README.en.md](README.en.md) (English)
2. **详细了解**: [README.zh-cn.md](README.zh-cn.md) 或 [README.en.md](README.en.md)
3. **Cookie配置**: [AUTO_COOKIE_EXTRACTION_GUIDE.md](AUTO_COOKIE_EXTRACTION_GUIDE.md)
4. **API提取**: [API_HEADER_EXTRACTION_GUIDE.md](API_HEADER_EXTRACTION_GUIDE.md)
5. **退出功能**: [LOGOUT_FEATURE_GUIDE.md](LOGOUT_FEATURE_GUIDE.md)
6. **功能探索**: [MANUAL_FEATURES_GUIDE.md](MANUAL_FEATURES_GUIDE.md)

### 对于开发者 | For Developers

1. **开发环境**: [DEVELOPMENT.zh-cn.md](DEVELOPMENT.zh-cn.md) 或 [DEVELOPMENT.md](DEVELOPMENT.md)
2. **国际化开发**: [I18N_GUIDE.md](I18N_GUIDE.md)
3. **源码结构**: `src/` 目录

### 对于贡献者 | For Contributors

1. **贡献指南**: [DEVELOPMENT.md](DEVELOPMENT.md) 中的贡献部分
2. **代码规范**: [DEVELOPMENT.md](DEVELOPMENT.md) 中的代码规范
3. **许可证**: [LICENSE](LICENSE)

## 🔍 快速查找 | Quick Reference

### 常见问题解决 | Common Issues

| 问题类型 | 查看文档 |
|----------|----------|
| 安装和配置 | [README.md](README.md) 快速开始部分 |
| 认证问题 | [README.zh-cn.md](README.zh-cn.md) 认证配置部分 |
| 功能使用 | [MANUAL_FEATURES_GUIDE.md](MANUAL_FEATURES_GUIDE.md) |
| 开发问题 | [DEVELOPMENT.md](DEVELOPMENT.md) 故障排除部分 |
| 语言设置 | [I18N_GUIDE.md](I18N_GUIDE.md) |

### 配置参考 | Configuration Reference

| 配置项 | 文档位置 |
|--------|----------|
| 基本配置 | [README.md](README.md) 配置选项部分 |
| 详细配置 | [README.zh-cn.md](README.zh-cn.md) 配置选项部分 |
| 开发配置 | [DEVELOPMENT.md](DEVELOPMENT.md) 项目结构部分 |

### API参考 | API Reference

| API类型 | 文档位置 |
|---------|----------|
| Augment API集成 | [README.zh-cn.md](README.zh-cn.md) 真实数据集成部分 |
| 扩展API | `src/` 目录中的TypeScript文件 |
| 配置API | [package.json](package.json) configuration部分 |

## 📝 文档维护 | Documentation Maintenance

### 更新频率 | Update Frequency

- **README文档**: 每次功能更新时同步更新
- **开发指南**: 开发流程变更时更新
- **功能指南**: 新功能添加时更新
- **技术文档**: 配置变更时更新

### 版本控制 | Version Control

- 所有文档都纳入Git版本控制
- 重要更改在commit message中标注
- 文档更新与代码更新同步进行

### 质量保证 | Quality Assurance

- 定期检查链接有效性
- 确保中英文文档内容同步
- 验证代码示例的正确性
- 保持文档格式一致性

---

**选择适合您的文档开始探索！** 📖

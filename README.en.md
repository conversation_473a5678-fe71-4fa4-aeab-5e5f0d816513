# Augment Usage Tracker

A VSCode extension that displays Augment AI usage statistics in the status bar.

## 🚀 Features

- **Status Bar Display**: Shows usage as "Augment: 123/1000 ●"
- **Real-time API Integration**: Gets actual usage from Augment API
- **5-second Auto Refresh**: Updates usage data every 5 seconds
- **Multiple Authentication**: API token or browser cookies
- **One-click Login**: Automated browser login with cookie extraction
- **Manual Refresh**: Instantly fetch latest data
- **Multi-language Support**: English and Chinese interface

## 📦 Quick Start

### Install Extension
```bash
code --install-extension augment-usage-tracker-1.0.0.vsix
```

### Setup Authentication
1. Run `Ctrl+Shift+P` → "🌐 Web Login (Auto)"
2. Login in browser and follow cookie extraction guide
3. View real usage data in status bar

## 🔧 Main Commands

| Command | Function |
|---------|----------|
| 🌐 Web Login (Auto) | Automated login and setup |
| Setup Browser Cookies | Manual cookie configuration |
| Check Authentication Status | Verify setup |
| Show Usage Details | Display detailed statistics |
| Reset Usage Statistics | Reset local data |
| 🔄 Manual Refresh | Instantly refresh data |
| 🌐 Set Language | Switch interface language |

## 📊 Status Bar

```
$(pulse) Augment: 7/56 ● (12%)
```

- **7/56**: Current usage / Total limit
- **●**: Real data indicator (○ for simulated data)
- **(12%)**: Usage percentage
- **Click**: Open Augment website
- **Hover**: View detailed information

## ⚙️ Configuration

| Setting | Default | Description |
|---------|---------|-------------|
| `augmentTracker.enabled` | `true` | Enable/disable tracker |
| `augmentTracker.refreshInterval` | `5` | Refresh interval (seconds) |
| `augmentTracker.showInStatusBar` | `true` | Show in status bar |
| `augmentTracker.language` | `"auto"` | Interface language |
| `augment.cookies` | `""` | Browser session cookies |

### Language Options
- `"auto"`: Auto (Follow VSCode language setting)
- `"en"`: English
- `"zh-cn"`: Simplified Chinese

## 🔐 Authentication

### Method 1: Web Login (Recommended)
1. Run `Ctrl+Shift+P` → "🌐 Web Login (Auto)"
2. Login in the opened browser
3. Follow the automatic cookie extraction guide

### Method 2: Manual Cookie Setup
1. Login to https://app.augmentcode.com
2. Copy browser cookies
3. Run `Ctrl+Shift+P` → "Setup Browser Cookies"
4. Paste cookies

## 🔄 Manual Refresh

### Use Cases
- Just completed Augment operations, want to see usage changes immediately
- Auto refresh has issues, manually trigger check
- Verify API connection and data accuracy

### Usage
```
Ctrl+Shift+P → "🔄 Manual Refresh"
```

### Expected Results
- **Success**: "Data refreshed successfully: 7/56 credits"
- **Failed**: "Failed to refresh data: [error message]"
- **No Auth**: "No authentication configured for refresh"

## 🌐 Language Setting

### Supported Languages
- **Auto**: Automatically choose based on VSCode language setting
- **English**: English interface
- **Simplified Chinese**: Chinese interface

### How to Set
1. Run `Ctrl+Shift+P` → "🌐 Set Language"
2. Select desired language (current language shows ✓)
3. Recommend restarting VSCode to fully apply changes

## 📈 Real Data Integration

The extension integrates with Augment's real API endpoints:

- **Credits API**: `/api/credits` - Real usage statistics
- **User API**: `/api/user` - Account information
- **Subscription API**: `/api/subscription` - Plan and billing info

### API Response Example
```json
{
  "usageUnitsAvailable": 49,
  "usageUnitsUsedThisBillingCycle": 7,
  "usageUnitsPending": 0
}
```

## 🔍 Troubleshooting

### No Real Data Showing
1. Check authentication: `Ctrl+Shift+P` → "Check Authentication Status"
2. Verify cookies haven't expired
3. Test API connection: `Ctrl+Shift+P` → "🔄 Manual Refresh"

### Auto Refresh Not Working
1. Check refresh interval setting
2. Verify extension is enabled
3. Look for console errors in Developer Tools (F12)

### Authentication Errors
1. Re-login to Augment web app
2. Get fresh cookies or token
3. Clear old authentication data

## 📝 Console Logging

The extension provides detailed console logging:

```
🔄 Starting real data refresh with 5s interval
📊 Credits API Response: { ... }
✅ Parsed usage data: { ... }
```

Enable Developer Tools (F12) to see detailed API interactions.

## 🛡️ Privacy & Security

- All authentication data stored locally
- No data transmitted to third parties
- User has complete control over credentials
- Secure cookie and token handling

## 🔄 Updates & Maintenance

The extension automatically refreshes data every 5 seconds and provides:
- Real-time usage statistics
- Automatic authentication validation
- Smart fallback to simulation mode
- Comprehensive error handling

## 📞 Support

If you encounter any issues or have questions:

- 📝 **Issues**: [GitHub Issues](https://github.com/augment-tracker/vscode-extension/issues)
- 📧 **Email**: <EMAIL>
- 📖 **Documentation**: [中文文档](README.zh-cn.md) | [English Docs](README.en.md)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### MIT License Summary
- ✅ **Commercial use**: Allowed
- ✅ **Modification**: Allowed  
- ✅ **Distribution**: Allowed
- ✅ **Private use**: Allowed
- ❌ **Liability**: Not provided
- ❌ **Warranty**: Not provided

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Contribution Process
1. Fork the project
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Create Pull Request

---

**Enjoy real-time Augment usage tracking in VSCode!** 🚀

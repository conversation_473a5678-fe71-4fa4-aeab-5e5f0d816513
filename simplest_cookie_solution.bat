@echo off
chcp 65001 > nul
echo ========================================
echo   最简单方便的Cookie获取方案
echo ========================================
echo.

echo 🎯 用户需求: 找最简单方便的方式获取cookie
echo.

echo ✅ 3种超简单方案:
echo [1] 🍪 一键书签 - 最方便（推荐）
echo [2] 📋 3步获取 - 最简单
echo [3] 🔧 VSCode内置 - 最集成
echo.

echo [1/4] 编译TypeScript...
call tsc
if errorlevel 1 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

echo.
echo [2/4] 重新打包插件...
call npx @vscode/vsce package --no-dependencies
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

echo.
echo [3/4] 发布最简化版本...
set /p confirm="确定要发布最简化Cookie方案吗? (y/N): "
if /i "%confirm%"=="y" (
    call npx @vscode/vsce publish --force
    if errorlevel 1 (
        echo ❌ 发布失败
        pause
        exit /b 1
    )
    echo ✅ 发布完成
) else (
    echo 取消发布
)

echo.
echo [4/4] 生成最简单使用指南...

echo ========================================
echo   方案1: 🍪 一键书签（最方便）
echo ========================================
echo.

echo 📋 使用步骤（只需做一次）:
echo 1. 打开 simple_cookie_extractor.html 文件
echo 2. 拖拽"🍪 拖我到书签栏"按钮到浏览器书签栏
echo 3. 访问 app.augmentcode.com 并登录
echo 4. 点击书签栏中的"🍪 拖我到书签栏"
echo 5. 自动复制cookie或显示获取指导
echo 6. 在VSCode中粘贴
echo.

echo 💡 优点:
echo - 一键操作，最方便
echo - 自动检测HttpOnly cookie
echo - 自动复制到剪贴板
echo - 支持所有浏览器
echo.

echo ========================================
echo   方案2: 📋 3步获取（最简单）
echo ========================================
echo.

echo 📋 超简单3步:
echo 步骤1️⃣: 按 F12 打开开发者工具
echo 步骤2️⃣: Application → Cookies → app.augmentcode.com
echo 步骤3️⃣: 双击 _session 的 Value，复制
echo.

echo 💡 优点:
echo - 步骤最少，只需3步
echo - 适用于所有cookie类型
echo - 不需要额外工具
echo - 最可靠的方法
echo.

echo ========================================
echo   方案3: 🔧 VSCode内置（最集成）
echo ========================================
echo.

echo 📋 使用方法:
echo 1. 按 Ctrl+Shift+P
echo 2. 输入 "🍪 超简单Cookie配置"
echo 3. 选择获取方式:
echo    - 🍪 我已复制cookie，直接粘贴
echo    - 📋 3步获取：F12 → Application → 复制
echo    - 🔖 使用一键书签
echo 4. 按照指导操作
echo.

echo 💡 优点:
echo - 集成在VSCode中
echo - 提供详细指导
echo - 多种方法可选
echo - 自动配置和刷新
echo.

echo ========================================
echo   推荐使用顺序
echo ========================================
echo.

echo 🥇 首选: 一键书签方法
echo 理由: 最方便，一键操作，自动处理
echo 适合: 经常需要更新cookie的用户
echo.

echo 🥈 备选: 3步获取方法
echo 理由: 最简单，步骤最少，最可靠
echo 适合: 偶尔配置cookie的用户
echo.

echo 🥉 备用: VSCode内置方法
echo 理由: 集成度高，指导详细
echo 适合: 需要详细指导的用户
echo.

echo ========================================
echo   各方案对比
echo ========================================
echo.

echo 📊 方便程度:
echo 一键书签 ★★★★★ (设置一次，永久使用)
echo 3步获取  ★★★★☆ (每次3步，简单快速)
echo VSCode内置 ★★★☆☆ (需要打开命令面板)
echo.

echo 📊 易用程度:
echo 3步获取  ★★★★★ (最简单，人人会用)
echo 一键书签 ★★★★☆ (需要创建书签)
echo VSCode内置 ★★★☆☆ (需要熟悉VSCode)
echo.

echo 📊 可靠程度:
echo 3步获取  ★★★★★ (最可靠，直接获取)
echo VSCode内置 ★★★★☆ (集成度高)
echo 一键书签 ★★★☆☆ (依赖JavaScript)
echo.

echo ========================================
echo   HttpOnly Cookie处理
echo ========================================
echo.

echo 🔒 如果cookie是HttpOnly:
echo.

echo ✅ 有效方法:
echo - 📋 3步获取（Application标签页）
echo - 🔧 Network标签页查看请求头
echo - 📝 手动复制粘贴
echo.

echo ❌ 无效方法:
echo - document.cookie（返回空）
echo - JavaScript自动提取
echo - 控制台cookie命令
echo.

echo 💡 识别HttpOnly cookie:
echo 1. 在Application标签页查看cookie
echo 2. 如果HttpOnly列显示✓，则是HttpOnly
echo 3. 如果document.cookie看不到_session，则是HttpOnly
echo.

echo ========================================
echo   快速故障排除
echo ========================================
echo.

echo ❓ 问题: 书签点击没反应
echo ✅ 解决: 确保在app.augmentcode.com页面使用
echo.

echo ❓ 问题: 找不到_session cookie
echo ✅ 解决: 确保已登录，或cookie是HttpOnly
echo.

echo ❓ 问题: 复制的格式不对
echo ✅ 解决: 只需要Value值，插件会自动处理格式
echo.

echo ❓ 问题: 状态栏不刷新
echo ✅ 解决: 使用"🍪 超简单Cookie配置"命令
echo.

echo ========================================
echo   最终推荐
echo ========================================
echo.

echo 🎯 最简单方便的方式:
echo.

echo 对于大多数用户:
echo 1. 使用"📋 3步获取"方法
echo 2. F12 → Application → Cookies → 复制_session的Value
echo 3. 在VSCode中运行"🍪 超简单Cookie配置"
echo 4. 选择"🍪 我已复制cookie，直接粘贴"
echo.

echo 对于高级用户:
echo 1. 打开 simple_cookie_extractor.html
echo 2. 创建一键书签
echo 3. 在Augment页面点击书签
echo 4. 自动复制并在VSCode中粘贴
echo.

echo ✅ 最简单Cookie获取方案已完成！
echo.

echo 现在用户有3种超简单的方式获取cookie：
echo 🍪 一键书签 - 最方便
echo 📋 3步获取 - 最简单  
echo 🔧 VSCode内置 - 最集成
echo.

echo 推荐使用"📋 3步获取"方法，
echo 只需要 F12 → Application → 复制_session 即可！
echo.

pause
